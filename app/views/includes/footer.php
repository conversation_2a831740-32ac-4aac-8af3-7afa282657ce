<!-- Universal Modal Manager -->
<script src="/assets/js/modal-manager.js"></script>

<script>
// Modern Confirmation Modal System
class ConfirmationModal {
    constructor() {
        this.modal = document.getElementById('confirmationModal');
        this.modalContent = document.getElementById('confirmationModalContent');
        this.titleElement = document.getElementById('confirmationModalTitle');
        this.messageElement = document.getElementById('confirmationModalMessage');
        this.iconElement = document.getElementById('confirmationModalIcon');
        this.confirmButton = document.getElementById('confirmationModalConfirm');
        this.cancelButton = document.getElementById('confirmationModalCancel');

        this.currentResolve = null;
        this.currentReject = null;

        this.init();
    }

    init() {
        // Bind event listeners
        this.cancelButton.addEventListener('click', () => this.hide(false));
        this.confirmButton.addEventListener('click', () => this.hide(true));

        // Close on backdrop click
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hide(false);
            }
        });

        // Close on Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !this.modal.classList.contains('hidden')) {
                this.hide(false);
            }
        });
    }

    show(options = {}) {
        return new Promise((resolve, reject) => {
            this.currentResolve = resolve;
            this.currentReject = reject;

            // Set default options
            const config = {
                title: 'Confirmation',
                message: 'Êtes-vous sûr de vouloir effectuer cette action ?',
                type: 'question', // question, warning, danger, success, info
                confirmText: 'Confirmer',
                cancelText: 'Annuler',
                confirmButtonClass: 'modal-btn-primary',
                ...options
            };

            // Update modal content
            this.titleElement.textContent = config.title;
            this.messageElement.innerHTML = config.message;
            this.confirmButton.textContent = config.confirmText;
            this.cancelButton.textContent = config.cancelText;

            // Update icon and styling based on type
            this.updateModalType(config.type);

            // Update button styling
            this.confirmButton.className = `px-4 py-2 text-sm font-medium text-white rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200 ${config.confirmButtonClass}`;

            // Show modal with animation
            this.modal.classList.remove('hidden');

            // Trigger animation after a small delay to ensure the element is rendered
            setTimeout(() => {
                this.modal.classList.add('modal-show');
                this.modalContent.classList.add('modal-show');
            }, 10);

            // Focus on confirm button for accessibility
            setTimeout(() => {
                this.confirmButton.focus();
            }, 300);
        });
    }

    hide(confirmed) {
        // Hide with animation
        this.modal.classList.remove('modal-show');
        this.modalContent.classList.remove('modal-show');

        setTimeout(() => {
            this.modal.classList.add('hidden');

            // Resolve the promise
            if (this.currentResolve) {
                this.currentResolve(confirmed);
                this.currentResolve = null;
                this.currentReject = null;
            }
        }, 300);
    }

    updateModalType(type) {
        // Reset icon classes
        this.iconElement.className = 'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3';

        // Update icon and colors based on type
        switch (type) {
            case 'warning':
                this.iconElement.classList.add('modal-icon-warning');
                this.iconElement.innerHTML = '<i class="fas fa-exclamation-triangle text-xl"></i>';
                break;
            case 'danger':
                this.iconElement.classList.add('modal-icon-danger');
                this.iconElement.innerHTML = '<i class="fas fa-exclamation-circle text-xl"></i>';
                break;
            case 'success':
                this.iconElement.classList.add('modal-icon-success');
                this.iconElement.innerHTML = '<i class="fas fa-check-circle text-xl"></i>';
                break;
            case 'info':
                this.iconElement.classList.add('modal-icon-info');
                this.iconElement.innerHTML = '<i class="fas fa-info-circle text-xl"></i>';
                break;
            case 'question':
            default:
                this.iconElement.classList.add('modal-icon-question');
                this.iconElement.innerHTML = '<i class="fas fa-question-circle text-xl"></i>';
                break;
        }
    }
}

// Initialize the modal system
const confirmationModal = new ConfirmationModal();

// Global function to replace confirm() calls
window.modernConfirm = function(options) {
    if (typeof options === 'string') {
        options = { message: options };
    }
    return confirmationModal.show(options);
};

// Convenience functions for different types
window.confirmWarning = function(message, title = 'Attention') {
    return confirmationModal.show({
        title,
        message,
        type: 'warning',
        confirmButtonClass: 'modal-btn-danger'
    });
};

window.confirmDanger = function(message, title = 'Action dangereuse') {
    return confirmationModal.show({
        title,
        message,
        type: 'danger',
        confirmButtonClass: 'modal-btn-danger'
    });
};

window.confirmSuccess = function(message, title = 'Confirmation') {
    return confirmationModal.show({
        title,
        message,
        type: 'success',
        confirmButtonClass: 'modal-btn-success'
    });
};

// Enhanced confirm function for initialization
window.confirmInitialization = function() {
    return confirmationModal.show({
        title: 'Initialisation des soldes',
        message: `
            <div class="space-y-3">
                <p class="font-medium">Êtes-vous sûr de vouloir initialiser les soldes de congés pour tous les utilisateurs ?</p>
                <div class="bg-blue-50 p-3 rounded-lg">
                    <p class="text-sm text-blue-800 font-medium mb-2">Cette action va :</p>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• Créer ou mettre à jour les soldes de congés pour l'année ${new Date().getFullYear()}</li>
                        <li>• Appliquer les allocations par défaut (19 jours congés payés, 3 jours exceptionnels, etc.)</li>
                        <li>• Définir les taux d'attribution mensuels selon les rôles</li>
                    </ul>
                </div>
                <p class="text-sm text-gray-600">Cette opération peut prendre quelques secondes selon le nombre d'utilisateurs.</p>
            </div>
        `,
        type: 'warning',
        confirmText: 'Initialiser',
        confirmButtonClass: 'modal-btn-primary'
    });
};
</script>

</body>
</html>
