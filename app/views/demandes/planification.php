<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Planning des Congés - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_planificateur.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Planning des congés</h1>
                    <p class="text-gray-600">G<PERSON>rez les congés et visualisez la disponibilité des équipes</p>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="relative">
                        <select id="departmentFilter" class="pr-8 pl-3 py-2 appearance-none bg-white border border-gray-300 rounded-md text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                            <option value="all">Tous les départements</option>
                            <option value="dev">Développement</option>
                            <option value="marketing">Marketing</option>
                            <option value="rh">Ressources Humaines</option>
                            <option value="finance">Finance</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    <div class="relative">
                        <input type="month" id="monthPicker" class="pr-3 pl-3 py-2 bg-white border border-gray-300 rounded-md text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500" value="2024-07">
                    </div>
                    <button class="bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-md flex items-center">
                        <i class="fas fa-download mr-2"></i> Exporter
                    </button>
                </div>
            </div>
        </header>

        <!-- Calendar navigation -->
        <div class="flex justify-between items-center mb-6 bg-white p-4 rounded-lg shadow-sm">
            <button id="prevMonth" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-1 px-3 rounded-md">
                <i class="fas fa-chevron-left"></i> Mois précédent
            </button>
            <h2 class="text-xl font-bold" id="currentMonth">Juillet 2024</h2>
            <button id="nextMonth" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-1 px-3 rounded-md">
                Mois suivant <i class="fas fa-chevron-right"></i>
            </button>
        </div>

        <!-- Calendar -->
        <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
            <div class="grid grid-cols-7 gap-px bg-gray-200">
                <!-- Day headers -->
                <div class="bg-gray-100 p-2 text-center font-medium text-gray-700">Lun</div>
                <div class="bg-gray-100 p-2 text-center font-medium text-gray-700">Mar</div>
                <div class="bg-gray-100 p-2 text-center font-medium text-gray-700">Mer</div>
                <div class="bg-gray-100 p-2 text-center font-medium text-gray-700">Jeu</div>
                <div class="bg-gray-100 p-2 text-center font-medium text-gray-700">Ven</div>
                <div class="bg-gray-100 p-2 text-center font-medium text-gray-700">Sam</div>
                <div class="bg-gray-100 p-2 text-center font-medium text-gray-700">Dim</div>

                <!-- First week with some days from previous month -->
                <div class="bg-white p-1 h-28">
                    <div class="h-full p-1 text-gray-400">
                        <div class="text-sm">29</div>
                    </div>
                </div>
                <div class="bg-white p-1 h-28">
                    <div class="h-full p-1 text-gray-400">
                        <div class="text-sm">30</div>
                    </div>
                </div>
                <!-- July 1st -->
                <div class="bg-white p-1 h-28">
                    <div class="h-full p-1">
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-sm">1</span>
                            <span class="text-xs px-1.5 py-0.5 bg-green-100 text-green-800 rounded-full">95%</span>
                        </div>
                        <!-- Employees on leave today -->
                        <div class="overflow-y-auto max-h-[calc(100%-24px)]">
                            <div class="text-xs p-1 mb-1 bg-blue-100 text-blue-800 rounded">
                                Martin L. (DEV)
                            </div>
                        </div>
                    </div>
                </div>
                <!-- July 2nd -->
                <div class="bg-white p-1 h-28">
                    <div class="h-full p-1">
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-sm">2</span>
                            <span class="text-xs px-1.5 py-0.5 bg-green-100 text-green-800 rounded-full">92%</span>
                        </div>
                        <!-- Employees on leave today -->
                        <div class="overflow-y-auto max-h-[calc(100%-24px)]">
                            <div class="text-xs p-1 mb-1 bg-blue-100 text-blue-800 rounded">
                                Martin L. (DEV)
                            </div>
                            <div class="text-xs p-1 mb-1 bg-purple-100 text-purple-800 rounded">
                                Sophie D. (MKT)
                            </div>
                        </div>
                    </div>
                </div>
                <!-- July 3rd -->
                <div class="bg-white p-1 h-28">
                    <div class="h-full p-1">
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-sm">3</span>
                            <span class="text-xs px-1.5 py-0.5 bg-green-100 text-green-800 rounded-full">92%</span>
                        </div>
                        <!-- Employees on leave today -->
                        <div class="overflow-y-auto max-h-[calc(100%-24px)]">
                            <div class="text-xs p-1 mb-1 bg-blue-100 text-blue-800 rounded">
                                Martin L. (DEV)
                            </div>
                            <div class="text-xs p-1 mb-1 bg-purple-100 text-purple-800 rounded">
                                Sophie D. (MKT)
                            </div>
                        </div>
                    </div>
                </div>
                <!-- July 4th -->
                <div class="bg-white p-1 h-28">
                    <div class="h-full p-1">
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-sm">4</span>
                            <span class="text-xs px-1.5 py-0.5 bg-green-100 text-green-800 rounded-full">92%</span>
                        </div>
                        <!-- Employees on leave today -->
                        <div class="overflow-y-auto max-h-[calc(100%-24px)]">
                            <div class="text-xs p-1 mb-1 bg-blue-100 text-blue-800 rounded">
                                Martin L. (DEV)
                            </div>
                            <div class="text-xs p-1 mb-1 bg-purple-100 text-purple-800 rounded">
                                Sophie D. (MKT)
                            </div>
                        </div>
                    </div>
                </div>
                <!-- July 5th -->
                <div class="bg-white p-1 h-28">
                    <div class="h-full p-1">
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-sm">5</span>
                            <span class="text-xs px-1.5 py-0.5 bg-green-100 text-green-800 rounded-full">90%</span>
                        </div>
                        <!-- Employees on leave today -->
                        <div class="overflow-y-auto max-h-[calc(100%-24px)]">
                            <div class="text-xs p-1 mb-1 bg-blue-100 text-blue-800 rounded">
                                Martin L. (DEV)
                            </div>
                            <div class="text-xs p-1 mb-1 bg-purple-100 text-purple-800 rounded">
                                Sophie D. (MKT)
                            </div>
                            <div class="text-xs p-1 mb-1 bg-green-100 text-green-800 rounded">
                                Julien P. (RH)
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Second week -->
                <div class="bg-white p-1 h-28">
                    <div class="h-full p-1">
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-sm">8</span>
                            <span class="text-xs px-1.5 py-0.5 bg-green-100 text-green-800 rounded-full">88%</span>
                        </div>
                        <!-- Employees on leave today -->
                        <div class="overflow-y-auto max-h-[calc(100%-24px)]">
                            <div class="text-xs p-1 mb-1 bg-yellow-100 text-yellow-800 rounded">
                                Christine M. (FIN)
                            </div>
                            <div class="text-xs p-1 mb-1 bg-indigo-100 text-indigo-800 rounded">
                                Pierre F. (SUPP)
                            </div>
                        </div>
                    </div>
                </div>
                <!-- July 9th - High priority day -->
                <div class="bg-white p-1 h-28 border-2 border-red-300">
                    <div class="h-full p-1">
                        <div class="flex justify-between items-center mb-1">
                            <span class="text-sm">9</span>
                            <span class="text-xs px-1.5 py-0.5 bg-red-100 text-red-800 rounded-full">55%</span>
                        </div>
                        <div class="text-xs font-medium text-center p-1 mb-1 bg-red-100 text-red-700 rounded">
                            Attention: Capacité réduite
                        </div>
                        <!-- Employees on leave today -->
                        <div class="overflow-y-auto max-h-[calc(100%-48px)]">
                            <div class="text-xs p-1 mb-1 bg-blue-100 text-blue-800 rounded">
                                Thomas R. (DEV)
                            </div>
                            <div class="text-xs p-1 mb-1 bg-blue-100 text-blue-800 rounded">
                                Anne S. (DEV)
                            </div>
                            <div class="text-xs p-1 mb-1 bg-purple-100 text-purple-800 rounded">
                                Marc L. (MKT)
                            </div>
                            <div class="text-xs p-1 mb-1 bg-green-100 text-green-800 rounded">
                                Elise B. (RH)
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Rest of the calendar would follow the same pattern -->
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">10</div></div></div>
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">11</div></div></div>
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">12</div></div></div>
                <!-- Weekend -->
                <div class="bg-gray-50 p-1 h-28"><div class="h-full p-1"><div class="text-sm text-gray-500">13</div></div></div>
                <div class="bg-gray-50 p-1 h-28"><div class="h-full p-1"><div class="text-sm text-gray-500">14</div></div></div>

                <!-- Third week -->
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">15</div></div></div>
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">16</div></div></div>
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">17</div></div></div>
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">18</div></div></div>
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">19</div></div></div>
                <!-- Weekend -->
                <div class="bg-gray-50 p-1 h-28"><div class="h-full p-1"><div class="text-sm text-gray-500">20</div></div></div>
                <div class="bg-gray-50 p-1 h-28"><div class="h-full p-1"><div class="text-sm text-gray-500">21</div></div></div>

                <!-- Fourth week (partial) -->
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">22</div></div></div>
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">23</div></div></div>
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">24</div></div></div>
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">25</div></div></div>
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">26</div></div></div>
                <!-- Weekend -->
                <div class="bg-gray-50 p-1 h-28"><div class="h-full p-1"><div class="text-sm text-gray-500">27</div></div></div>
                <div class="bg-gray-50 p-1 h-28"><div class="h-full p-1"><div class="text-sm text-gray-500">28</div></div></div>

                <!-- Fifth week (partial) -->
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">29</div></div></div>
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">30</div></div></div>
                <div class="bg-white p-1 h-28"><div class="h-full p-1"><div class="text-sm">31</div></div></div>
                <div class="bg-white p-1 h-28">
                    <div class="h-full p-1 text-gray-400">
                        <div class="text-sm">1</div>
                    </div>
                </div>
                <div class="bg-white p-1 h-28">
                    <div class="h-full p-1 text-gray-400">
                        <div class="text-sm">2</div>
                    </div>
                </div>
                <div class="bg-white p-1 h-28">
                    <div class="h-full p-1 text-gray-400">
                        <div class="text-sm">3</div>
                    </div>
                </div>
                <div class="bg-white p-1 h-28">
                    <div class="h-full p-1 text-gray-400">
                        <div class="text-sm">4</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Legend -->
        <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
            <h3 class="text-lg font-medium text-gray-800 mb-3">Légende</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="flex items-center">
                    <span class="w-4 h-4 rounded-full bg-green-100 mr-2"></span>
                    <span>RH</span>
                </div>
                <div class="flex items-center">
                    <span class="w-4 h-4 rounded-full bg-blue-100 mr-2"></span>
                    <span>Développement</span>
                </div>
                <div class="flex items-center">
                    <span class="w-4 h-4 rounded-full bg-purple-100 mr-2"></span>
                    <span>Marketing</span>
                </div>
                <div class="flex items-center">
                    <span class="w-4 h-4 rounded-full bg-yellow-100 mr-2"></span>
                    <span>Finance</span>
                </div>
            </div>
            <div class="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4">
                <div class="flex items-center">
                    <span class="w-12 h-4 bg-green-100 text-green-800 text-xs flex items-center justify-center rounded-full mr-2">80%+</span>
                    <span>Capacité optimale</span>
                </div>
                <div class="flex items-center">
                    <span class="w-12 h-4 bg-yellow-100 text-yellow-800 text-xs flex items-center justify-center rounded-full mr-2">60-80%</span>
                    <span>Capacité moyenne</span>
                </div>
                <div class="flex items-center">
                    <span class="w-12 h-4 bg-red-100 text-red-800 text-xs flex items-center justify-center rounded-full mr-2">&lt;60%</span>
                    <span>Sous-capacité</span>
                </div>
            </div>
        </div>

        <!-- Critical Dates -->
        <div class="bg-white p-4 rounded-lg shadow-sm">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-800">Dates critiques</h3>
                <button class="text-teal-600 hover:text-teal-800 text-sm font-medium">
                    Gérer les alertes
                </button>
            </div>
            <div class="space-y-3">
                <div class="p-3 bg-red-50 rounded-lg flex justify-between items-center">
                    <div>
                        <div class="text-sm font-medium text-gray-800">9 juillet 2024</div>
                        <div class="text-sm text-red-700">12 personnes absentes (55% disponibles)</div>
                    </div>
                    <button class="text-red-600 hover:text-red-800">
                        <i class="fas fa-bell-slash"></i>
                    </button>
                </div>
                <div class="p-3 bg-red-50 rounded-lg flex justify-between items-center">
                    <div>
                        <div class="text-sm font-medium text-gray-800">15 août 2024</div>
                        <div class="text-sm text-red-700">15 personnes absentes (48% disponibles)</div>
                    </div>
                    <button class="text-red-600 hover:text-red-800">
                        <i class="fas fa-bell-slash"></i>
                    </button>
                </div>
                <div class="p-3 bg-yellow-50 rounded-lg flex justify-between items-center">
                    <div>
                        <div class="text-sm font-medium text-gray-800">24-31 décembre 2024</div>
                        <div class="text-sm text-yellow-700">10 personnes absentes (75% disponibles)</div>
                    </div>
                    <button class="text-yellow-600 hover:text-yellow-800">
                        <i class="fas fa-bell-slash"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mainContent = document.getElementById('main-content');

            // Month navigation
            const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                              'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
            let currentDate = new Date(2024, 6); // July 2024

            function updateCalendarTitle() {
                document.getElementById('currentMonth').textContent =
                    `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
                document.getElementById('monthPicker').value =
                    `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
            }

            document.getElementById('prevMonth').addEventListener('click', function() {
                currentDate.setMonth(currentDate.getMonth() - 1);
                updateCalendarTitle();
                // Here you would update the calendar content
            });

            document.getElementById('nextMonth').addEventListener('click', function() {
                currentDate.setMonth(currentDate.getMonth() + 1);
                updateCalendarTitle();
                // Here you would update the calendar content
            });

            document.getElementById('monthPicker').addEventListener('change', function(e) {
                const [year, month] = e.target.value.split('-');
                currentDate = new Date(parseInt(year), parseInt(month) - 1);
                updateCalendarTitle();
                // Here you would update the calendar content
            });
        });
    </script>
</body>
</html>
