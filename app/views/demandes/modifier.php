<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Modifier demande - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>

<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_employe.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Modifier une demande</h1>
                    <p class="text-gray-600">Modifier votre demande de congé ou d'absence</p>
                </div>
                <a href="/mes-demandes" class="text-purple-600 hover:text-purple-800 flex items-center gap-2">
                    <i class="fas fa-arrow-left"></i> Retour à mes demandes
                </a>
            </div>
        </header>

        <div class="bg-white p-6 rounded-lg shadow-sm">
            <?php if (isset($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
                <p><?= htmlspecialchars($error) ?></p>
            </div>
            <?php endif; ?>

            <?php if (strtolower($demande['statut']) !== 'en cours'): ?>
            <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6">
                <p>Cette demande est
                    <?php if (strtolower($demande['statut']) === 'acceptée'): ?>
                    approuvée
                    <?php elseif (strtolower($demande['statut']) === 'refusée'): ?>
                    rejetée
                    <?php else: ?>
                    <?= strtolower(htmlspecialchars($demande['statut'])) ?>
                    <?php endif; ?>
                    et ne peut plus être modifiée.
                </p>
            </div>

            <div class="flex justify-end mt-6">
                <a href="/mes-demandes" class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">Retour à
                    mes demandes</a>
            </div>
            <?php else: ?>
            <form action="/modifier-demande?id=<?= $demande['id'] ?>" method="POST" enctype="multipart/form-data">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type de demande *</label>
                        <select id="type" name="type"
                            class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                            required>
                            <option value="" disabled>-- Sélectionnez un type --</option>
                            <option value="payé" <?= isset($type) && $type === 'payé' ? 'selected' : '' ?>>Congé payé
                            </option>
                            <option value="sans solde" <?= isset($type) && $type === 'sans solde' ? 'selected' : '' ?>>
                                Congé sans solde</option>
                            <option value="maladie" <?= isset($type) && $type === 'maladie' ? 'selected' : '' ?>>Congé
                                maladie</option>
                            <option value="exceptionnel"
                                <?= isset($type) && $type === 'exceptionnel' ? 'selected' : '' ?>>Congé exceptionnel
                            </option>
                            <option value="familial" <?= isset($type) && $type === 'familial' ? 'selected' : '' ?>>Congé
                                familial</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="date_debut" class="block text-sm font-medium text-gray-700 mb-1">Date de début
                            *</label>
                        <input type="date" id="date_debut" name="date_debut"
                            value="<?= htmlspecialchars($demande['date_debut']) ?>"
                            class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent min-h-[44px]"
                            required>
                        <div class="mt-2 flex flex-wrap items-center">
                            <div class="flex items-center mr-2 mb-2 sm:mb-0">
                                <input type="checkbox" id="demi_jour_debut" name="demi_jour_debut"
                                    class="h-5 w-5 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                                    <?= isset($demande['demi_journee']) && $demande['demi_journee'] && (strpos($demande['demi_type'], 'matin') !== false || strpos($demande['demi_type'], 'apres-midi') !== false) ? 'checked' : '' ?>>
                                <label for="demi_jour_debut" class="ml-2 block text-sm text-gray-700">Demi-journée</label>
                            </div>
                            <select id="periode_debut" name="periode_debut"
                                class="pl-2 pr-6 py-1 text-sm border-gray-300 rounded-md min-h-[36px]"
                                <?= isset($demande['demi_journee']) && $demande['demi_journee'] && (strpos($demande['demi_type'], 'matin') !== false || strpos($demande['demi_type'], 'apres-midi') !== false) ? '' : 'disabled' ?>>
                                <option value="matin" <?= isset($demande['demi_type']) && strpos($demande['demi_type'], 'matin') !== false ? 'selected' : '' ?>>Matin</option>
                                <option value="apres-midi" <?= isset($demande['demi_type']) && strpos($demande['demi_type'], 'apres-midi') !== false ? 'selected' : '' ?>>Après-midi</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label for="date_fin" class="block text-sm font-medium text-gray-700 mb-1">Date de fin *</label>
                        <input type="date" id="date_fin" name="date_fin"
                            value="<?= htmlspecialchars($demande['date_fin']) ?>"
                            class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent min-h-[44px]"
                            required>
                        <div class="mt-2 flex flex-wrap items-center">
                            <div class="flex items-center mr-2 mb-2 sm:mb-0">
                                <input type="checkbox" id="demi_jour_fin" name="demi_jour_fin"
                                    class="h-5 w-5 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                                    <?= isset($demande['demi_journee']) && $demande['demi_journee'] && (strpos($demande['demi_type'], 'matin') !== false || strpos($demande['demi_type'], 'apres-midi') !== false) ? 'checked' : '' ?>>
                                <label for="demi_jour_fin" class="ml-2 block text-sm text-gray-700">Demi-journée</label>
                            </div>
                            <select id="periode_fin" name="periode_fin"
                                class="pl-2 pr-6 py-1 text-sm border-gray-300 rounded-md min-h-[36px]"
                                <?= isset($demande['demi_journee']) && $demande['demi_journee'] && (strpos($demande['demi_type'], 'matin') !== false || strpos($demande['demi_type'], 'apres-midi') !== false) ? '' : 'disabled' ?>>
                                <option value="matin" <?= isset($demande['demi_type']) && strpos($demande['demi_type'], 'matin') !== false ? 'selected' : '' ?>>Matin</option>
                                <option value="apres-midi" <?= isset($demande['demi_type']) && strpos($demande['demi_type'], 'apres-midi') !== false ? 'selected' : '' ?>>Après-midi</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <label for="motif" class="block text-sm font-medium text-gray-700 mb-1">Motif / Commentaires</label>
                    <textarea id="motif" name="motif" rows="4"
                        class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"><?= htmlspecialchars($demande['motif'] ?? '') ?></textarea>
                </div>

                <div class="mb-6">
                    <label for="justificatif" class="block text-sm font-medium text-gray-700 mb-1">Justificatif (si
                        nécessaire)</label>
                    <div class="mt-1 flex flex-wrap items-center">
                        <label for="justificatif"
                            class="relative cursor-pointer bg-white rounded-md font-medium text-purple-600 hover:text-purple-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-purple-500 mb-2 sm:mb-0">
                            <span class="border border-gray-300 rounded-md px-3 py-2 text-sm inline-block min-h-[44px] flex items-center">Choisir un fichier</span>
                            <input id="justificatif" name="justificatif" type="file" class="sr-only"
                                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                        </label>
                        <span class="ml-0 sm:ml-2 text-sm text-gray-500 w-full sm:w-auto" id="file-name">
                            <?= isset($demande['justificatif']) && !empty($demande['justificatif']) ? htmlspecialchars($demande['justificatif']) : 'Aucun fichier sélectionné' ?>
                        </span>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Formats acceptés: PDF, Word, JPG, PNG (max. 5 MB)</p>
                    <?php if (isset($demande['justificatif']) && !empty($demande['justificatif'])): ?>
                    <p class="mt-2 text-xs text-gray-700">
                        <a href="/uploads/justificatifs/<?= htmlspecialchars($demande['justificatif']) ?>" target="_blank" class="text-purple-600 hover:text-purple-800">
                            <i class="fas fa-file-download"></i> Voir le justificatif actuel
                        </a>
                    </p>
                    <?php endif; ?>
                </div>

                <div id="calculJours" class="mb-6 p-4 bg-gray-50 rounded-lg">
                    <p class="text-sm text-gray-600">Votre demande porte sur <span id="nbJours"
                            class="font-bold">0</span> jour(s)</p>
                </div>

                <div class="flex justify-end gap-4">
                    <a href="/mes-demandes"
                        class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">Annuler</a>
                    <button type="submit"
                        class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">Modifier la
                        demande</button>
                </div>
            </form>
            <?php endif; ?>
        </div>
    </div>

    <script>
    // Sidebar toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Calculate number of days
        const dateDebut = document.getElementById('date_debut');
        const dateFin = document.getElementById('date_fin');
        const calculJours = document.getElementById('calculJours');
        const nbJours = document.getElementById('nbJours');
        const demiJourDebut = document.getElementById('demi_jour_debut');
        const demiJourFin = document.getElementById('demi_jour_fin');
        const periodeDebut = document.getElementById('periode_debut');
        const periodeFin = document.getElementById('periode_fin');

        // Enable/disable period selectors
        demiJourDebut.addEventListener('change', function() {
            periodeDebut.disabled = !this.checked;
        });

        demiJourFin.addEventListener('change', function() {
            periodeFin.disabled = !this.checked;
        });

        // Format date inputs to make sure they're in ISO format (YYYY-MM-DD)
        function formatDateInput(input) {
            input.addEventListener('input', function(e) {
                // Check if the entered value matches the French formats
                const frenchDatePattern1 = /^(\d{2})\/(\d{2})\/(\d{4})$/; // DD/MM/YYYY
                const frenchDatePattern2 = /^(\d{2})-(\d{2})-(\d{4})$/;   // DD-MM-YYYY
                const value = e.target.value;

                if (frenchDatePattern1.test(value)) {
                    // Convert from DD/MM/YYYY to YYYY-MM-DD
                    const matches = value.match(frenchDatePattern1);
                    const formattedDate = `${matches[3]}-${matches[2]}-${matches[1]}`;
                    e.target.value = formattedDate;
                } else if (frenchDatePattern2.test(value)) {
                    // Convert from DD-MM-YYYY to YYYY-MM-DD
                    const matches = value.match(frenchDatePattern2);
                    const formattedDate = `${matches[3]}-${matches[2]}-${matches[1]}`;
                    e.target.value = formattedDate;
                }
            });
        }

        // Apply date formatting to date inputs
        formatDateInput(dateDebut);
        formatDateInput(dateFin);

        // Add blur event to handle manual entries
        dateDebut.addEventListener('blur', function() {
            // Try to convert from DD/MM/YYYY format when losing focus
            const value = this.value;
            if (value && value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
                const parts = value.split(/[\/\-]/);
                if (parts.length === 3) {
                    this.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                }
            }
        });

        dateFin.addEventListener('blur', function() {
            // Try to convert from DD/MM/YYYY format when losing focus
            const value = this.value;
            if (value && value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
                const parts = value.split(/[\/\-]/);
                if (parts.length === 3) {
                    this.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                }
            }
        });

        function updateDaysCount() {
            if (dateDebut.value && dateFin.value) {
                try {
                    const start = new Date(dateDebut.value);
                    const end = new Date(dateFin.value);

                    if (!isNaN(start) && !isNaN(end) && end >= start) {
                        // Calculate days difference (+1 because inclusive)
                        const diffTime = end - start;
                        let diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

                        if (demiJourDebut.checked) {
                            diffDays -= 0.5;
                        }

                        if (demiJourFin.checked) {
                            diffDays -= 0.5;
                        }

                        nbJours.textContent = diffDays;
                        calculJours.classList.remove('hidden');
                    } else {
                        calculJours.classList.add('hidden');
                    }
                } catch (e) {
                    calculJours.classList.add('hidden');
                }
            } else {
                calculJours.classList.add('hidden');
            }
        }

        dateDebut.addEventListener('change', updateDaysCount);
        dateFin.addEventListener('change', updateDaysCount);
        demiJourDebut.addEventListener('change', updateDaysCount);
        demiJourFin.addEventListener('change', updateDaysCount);

        // Initialize if values are already set
        updateDaysCount();

        // Submit form validation
        document.querySelector('form').addEventListener('submit', function(event) {
            try {
                // At this point, all dates should be in YYYY-MM-DD format due to our conversion handlers
                const startDate = new Date(dateDebut.value);
                const endDate = new Date(dateFin.value);

                if (isNaN(startDate) || isNaN(endDate)) {
                    event.preventDefault();
                    alert('Format de date invalide. Veuillez utiliser le format JJ/MM/AAAA ou AAAA-MM-JJ.');

                    // Try to automatically fix the format if possible
                    if (dateDebut.value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
                        const parts = dateDebut.value.split(/[\/\-]/);
                        if (parts.length === 3) {
                            dateDebut.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                        }
                    }

                    if (dateFin.value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
                        const parts = dateFin.value.split(/[\/\-]/);
                        if (parts.length === 3) {
                            dateFin.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                        }
                    }
                } else if (endDate < startDate) {
                    event.preventDefault();
                    alert('La date de fin doit être postérieure ou égale à la date de début.');
                }
            } catch (e) {
                event.preventDefault();
                alert('Format de date invalide. Veuillez utiliser le format JJ/MM/AAAA ou AAAA-MM-JJ.');
            }
        });

        // File upload handling
        const fileInput = document.getElementById('justificatif');
        const fileNameSpan = document.getElementById('file-name');

        fileInput.addEventListener('change', function() {
            if (this.files && this.files.length > 0) {
                const file = this.files[0];

                // Check file size (max 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('Le fichier est trop volumineux. La taille maximale est de 5 MB.');
                    this.value = "";
                    fileNameSpan.textContent = 'Aucun fichier sélectionné';
                    return;
                }

                // Display file name
                fileNameSpan.textContent = file.name;
            } else {
                fileNameSpan.textContent = 'Aucun fichier sélectionné';
            }
        });
    });
    </script>
</body>

</html>