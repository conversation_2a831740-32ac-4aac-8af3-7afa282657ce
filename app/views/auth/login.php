<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>

<body class="login-page">
    <div class="max-w-md w-full modal-card mx-4">
        <div class="px-4 sm:px-8 py-6">
            <div class="text-center mb-8">
                <!-- <i class="fas fa-calendar-check text-purple-600 text-4xl mb-3"></i> -->
                <h2 class="text-2xl font-bold text-gray-800">Gestion des Congés</h2>
                <p class="text-gray-500 mt-1">Connectez-vous à votre compte</p>
            </div>

            <?php if (isset($_GET['error'])): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-5 rounded">
                <p>
                    <?php if ($_GET['error'] === 'identifiants_incorrects'): ?>
                    Identifiants incorrects. Veuillez réessayer.
                    <?php elseif ($_GET['error'] === 'role_inconnu'): ?>
                    Problème avec votre profil. Contactez un administrateur.
                    <?php else: ?>
                    Une erreur est survenue. Veuillez réessayer.
                    <?php endif; ?>
                </p>
            </div>
            <?php endif; ?>

            <form action="/login/submit" method="POST">
                <div class="mb-4">
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Adresse email</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                        <input type="email" id="email" name="email"
                            class="focus:ring-purple-500 focus:border-purple-500 block w-full pl-10 text-sm border-gray-300 rounded-md max-w-full"
                            placeholder="<EMAIL>" required autofocus>
                    </div>
                </div>

                <div class="mb-6">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Mot de passe</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input type="password" id="password" name="password"
                            class="focus:ring-purple-500 focus:border-purple-500 block w-full pl-10 text-sm border-gray-300 rounded-md max-w-full"
                            placeholder="Votre mot de passe" required>
                    </div>
                </div>

                <div>
                    <button type="submit"
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Se connecter
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>

</html>