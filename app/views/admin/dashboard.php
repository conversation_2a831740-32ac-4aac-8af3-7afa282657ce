<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Congés - Tableau de bord Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>

<body class="bg-gray-50">
    <?php include_once __DIR__ . '/../shared/sidebar_admin.php'; ?>

    <div class="md:ml-[240px] p-4 md:p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
                <div>
                    <h1 class="text-xl sm:text-2xl font-bold text-gray-800">Bienvenue dans votre espace d'administration
                        <?= htmlspecialchars($_SESSION['prenom']) ?>👋</h1>
                    <p class="text-sm sm:text-base text-gray-600">Gérez les comptes, les droits et les historiques des congés dans
                        l'organisation.</p>
                </div>
            </div>
        </header>

        <!-- Dashboard Stats -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow p-4 border-l-4 border-blue-500">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-500 mr-4">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Total Utilisateurs</p>
                        <p class="text-xl font-bold"><?= $totalUsers ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-4 border-l-4 border-green-500">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-500 mr-4">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Demandes Totales</p>
                        <p class="text-xl font-bold"><?= $totalRequests ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-4 border-l-4 border-yellow-500">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-500 mr-4">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Demandes en Attente</p>
                        <p class="text-xl font-bold"><?= $pendingRequests ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-4 border-l-4 border-purple-500">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-500 mr-4">
                        <i class="fas fa-building"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Départements</p>
                        <p class="text-xl font-bold"><?= count($departmentBalances) ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Department Leave Balances -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Soldes Départementaux</h2>
                    <a href="/admin/department_leave_balances" class="text-sm text-blue-600 hover:underline">Voir tous</a>
                </div>

                <?php if (empty($departmentBalances)): ?>
                    <div class="p-4 bg-yellow-50 rounded text-yellow-700">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm">
                                    Les soldes départementaux n'ont pas encore été initialisés.
                                </p>
                            </div>
                        </div>
                        <div class="mt-3">
                            <a href="/admin/department-leave-balances/initialize" class="text-sm font-medium text-yellow-700 hover:text-yellow-600">
                                Initialiser les soldes <span aria-hidden="true">&rarr;</span>
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full">
                            <thead>
                                <tr class="text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <th class="px-4 py-2">Département</th>
                                    <th class="px-4 py-2">Restant</th>
                                    <th class="px-4 py-2">Status</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <?php
                                // Display up to 5 departments, prioritizing low balance ones
                                $displayDepts = array_merge($lowBalanceDepartments, array_diff_key($departmentBalances, $lowBalanceDepartments));
                                $displayDepts = array_slice($displayDepts, 0, 5);

                                foreach ($displayDepts as $dept):
                                    $percentRemaining = ($dept['remaining_days'] / $dept['total_days']) * 100;

                                    // Determine status color
                                    if ($percentRemaining <= 20) {
                                        $statusColor = 'bg-red-500';
                                        $statusTextColor = 'text-red-700';
                                        $statusBgColor = 'bg-red-100';
                                    } elseif ($percentRemaining <= 50) {
                                        $statusColor = 'bg-yellow-500';
                                        $statusTextColor = 'text-yellow-700';
                                        $statusBgColor = 'bg-yellow-100';
                                    } else {
                                        $statusColor = 'bg-green-500';
                                        $statusTextColor = 'text-green-700';
                                        $statusBgColor = 'bg-green-100';
                                    }
                                ?>
                                <tr>
                                    <td class="px-4 py-3">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?= htmlspecialchars($dept['department_name']) ?>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="text-sm text-gray-900">
                                            <?= number_format($dept['remaining_days'], 1) ?> / <?= number_format($dept['total_days'], 1) ?> jours
                                        </div>
                                        <div class="w-24 bg-gray-200 rounded-full h-2 mt-1">
                                            <div class="<?= $statusColor ?> h-2 rounded-full" style="width: <?= $percentRemaining ?>%"></div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $statusBgColor ?> <?= $statusTextColor ?>">
                                            <?php
                                            if ($percentRemaining <= 20) {
                                                echo "Critique";
                                            } elseif ($percentRemaining <= 50) {
                                                echo "Attention";
                                            } else {
                                                echo "Normal";
                                            }
                                            ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if (count($lowBalanceDepartments) > 0): ?>
                    <div class="mt-4 p-3 bg-red-50 rounded text-red-700 text-sm">
                        <p class="font-medium">⚠️ Attention</p>
                        <p><?= count($lowBalanceDepartments) ?> département(s) ont un solde critique (moins de 3 jours).</p>
                    </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>

            <!-- Recent Users -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">Utilisateurs Récents</h2>
                    <a href="/list-users" class="text-sm text-blue-600 hover:underline">Voir tous</a>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <th class="px-4 py-2">Utilisateur</th>
                                <th class="px-4 py-2">Rôle</th>
                                <th class="px-4 py-2">Département</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <?php foreach ($recentUsers as $user): ?>
                            <tr>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8">
                                            <?php if (!empty($user['photo_url'])): ?>
                                                <img class="h-8 w-8 rounded-full" src="<?= $user['photo_url'] ?>" alt="Photo de profil">
                                            <?php else: ?>
                                                <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-600">
                                                    <?= strtoupper(substr($user['prenom'], 0, 1) . substr($user['nom'], 0, 1)) ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?= htmlspecialchars($user['prenom'] . ' ' . $user['nom']) ?>
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                <?= htmlspecialchars($user['email']) ?>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <?php
                                    $roleClasses = [
                                        'admin' => 'bg-purple-100 text-purple-800',
                                        'responsable' => 'bg-blue-100 text-blue-800',
                                        'planificateur' => 'bg-green-100 text-green-800',
                                        'employe' => 'bg-gray-100 text-gray-800'
                                    ];
                                    $roleClass = $roleClasses[$user['role']] ?? 'bg-gray-100 text-gray-800';
                                    ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $roleClass ?>">
                                        <?= ucfirst(htmlspecialchars($user['role'])) ?>
                                    </span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="text-sm text-gray-900">
                                        <?= htmlspecialchars($user['departement'] ?? 'N/A') ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>

</html>