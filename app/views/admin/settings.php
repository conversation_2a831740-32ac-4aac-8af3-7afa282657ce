<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Paramètres - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once __DIR__ . '/../shared/sidebar_admin.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <h1 class="text-2xl font-bold text-gray-800">Paramètres généraux</h1>
            <p class="text-gray-600">Configuration générale de l'application</p>
        </header>

        <?php if (isset($success)): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded">
                <p><?= $success ?></p>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded">
                <p><?= $error ?></p>
            </div>
        <?php endif; ?>

        <div class="bg-white rounded-lg shadow-sm p-6">
            <form action="/settings" method="POST">
                <div class="space-y-8 divide-y divide-gray-200">
                    <div class="space-y-6">
                        <div>
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Paramètres généraux</h3>
                            <p class="mt-1 text-sm text-gray-500">Informations de base de l'application</p>
                        </div>

                        <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                            <div class="sm:col-span-4">
                                <label for="app_name" class="block text-sm font-medium text-gray-700">
                                    Nom de l'application
                                </label>
                                <div class="mt-1">
                                    <input type="text" name="app_name" id="app_name" value="<?= htmlspecialchars($settings['app_name']) ?>" class="shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                            <div class="sm:col-span-6">
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input id="email_notifications" name="email_notifications" type="checkbox" <?= $settings['email_notifications'] ? 'checked' : '' ?> class="focus:ring-purple-500 h-4 w-4 text-purple-600 border-gray-300 rounded">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="email_notifications" class="font-medium text-gray-700">Activer les notifications par email</label>
                                        <p class="text-gray-500">Les utilisateurs recevront des emails pour les événements importants</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="pt-6 space-y-6">
                        <div>
                            <h3 class="text-lg font-medium leading-6 text-gray-900">Gestion des congés</h3>
                            <p class="mt-1 text-sm text-gray-500">Configuration des règles de gestion des congés</p>
                        </div>

                        <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                            <div class="sm:col-span-2">
                                <label for="days_before_expiration" class="block text-sm font-medium text-gray-700">
                                    Jours avant expiration des congés
                                </label>
                                <div class="mt-1">
                                    <input type="number" name="days_before_expiration" id="days_before_expiration" min="0" max="365" value="<?= htmlspecialchars($settings['days_before_expiration']) ?>" class="shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                </div>
                                <p class="mt-2 text-sm text-gray-500">Nombre de jours avant qu'un congé non utilisé n'expire</p>
                            </div>
                        </div>
                    </div>

                    <div class="pt-5">
                        <div class="flex justify-end">
                            <button type="button" onclick="window.location.reload()" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Annuler
                            </button>
                            <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                                Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Actions du système</h3>

            <div class="space-y-4">
                <div class="flex justify-between items-center pb-4 border-b">
                    <div>
                        <h4 class="font-medium text-gray-800">Purger les anciennes notifications</h4>
                        <p class="text-sm text-gray-500">Supprime les notifications de plus de 30 jours</p>
                    </div>
                    <a href="/purge-notifications" onclick="return confirm('Êtes-vous sûr de vouloir purger les anciennes notifications ?')" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        Exécuter
                    </a>
                </div>

                <div class="flex justify-between items-center pb-4 border-b">
                    <div>
                        <h4 class="font-medium text-gray-800">Sauvegarde de la base de données</h4>
                        <p class="text-sm text-gray-500">Crée une sauvegarde complète de la base de données</p>
                    </div>
                    <a href="/backup-database" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Démarrer
                    </a>
                </div>

                <div class="flex justify-between items-center">
                    <div>
                        <h4 class="font-medium text-gray-800">Envoyer le rapport d'activité par email</h4>
                        <p class="text-sm text-gray-500">Génère et envoie un rapport d'activité aux administrateurs</p>
                    </div>
                    <a href="/send-activity-report" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        Envoyer
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
    </script>
</body>
</html>
