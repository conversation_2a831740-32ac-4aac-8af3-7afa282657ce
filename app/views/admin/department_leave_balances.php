<?php require_once APPROOT . '/views/includes/header.php'; ?>
<?php require_once APPROOT . '/views/includes/admin_sidebar.php'; ?>

    <div class="container mx-auto px-4 py-8">
        <!-- Breadcrumb Navigation -->
        <nav class="mb-4" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2 text-sm">
                <li>
                    <a href="/dashboard-admin" class="text-purple-600 hover:text-purple-800">
                        <i class="fas fa-home mr-1"></i>Accueil
                    </a>
                </li>
                <li>
                    <span class="text-gray-500">/</span>
                </li>
                <li>
                    <span class="text-purple-600">Paramètres du système</span>
                </li>
                <li>
                    <span class="text-gray-500">/</span>
                </li>
                <li class="text-gray-500">
                    Soldes Département
                </li>
            </ol>
        </nav>

        <h1 class="text-2xl font-bold mb-6">Gestion des Soldes de Congés Départementaux</h1>

        <?php if (isset($success)): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
                <p><?= $success ?></p>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                <p><?= $error ?></p>
            </div>
        <?php endif; ?>

        <!-- Actions Section -->
        <div class="card mb-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-4">
                <!-- Left side - Search/Filter (if needed in future) -->
                <div class="flex-1">
                    <h2 class="text-lg font-semibold mb-4">Gestion des Soldes Départementaux</h2>
                    <p class="text-gray-600">Gérez les allocations de congés par département</p>
                </div>

                <!-- Right side - Action Buttons -->
                <div class="flex-shrink-0">
                    <h2 class="text-lg font-semibold mb-4">Actions</h2>
                    <div class="flex flex-wrap gap-2">
                        <a href="#"
                           onclick="handleDepartmentInitialization(event)"
                           data-url="/admin/department-leave-balances/initialize"
                           class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center">
                            <i class="fas fa-sync-alt mr-2"></i> Initialiser les soldes départementaux
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Department Balances Table -->
        <div class="table-card">
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-4">Soldes de congés par département</h2>
            </div>

            <?php if (empty($departmentBalances)): ?>
                <div class="p-6">
                    <p class="text-gray-500">Aucun solde départemental trouvé.</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Département
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Solde Total
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Jours Utilisés
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Jours Restants
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($departmentBalances as $balance): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?= $balance['department_name'] ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= $balance['total_days'] ?> jours
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= $balance['used_days'] ?> jours
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= $balance['remaining_days'] ?> jours
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                            <?php
                                            $usedDays = isset($balance['used_days']) ? $balance['used_days'] : 0;
                                            $totalDays = isset($balance['total_days']) ? $balance['total_days'] : 0;
                                            $percentageUsed = ($totalDays > 0) ? ($usedDays / $totalDays) * 100 : 0;
                                            $percentageUsed = min(100, max(0, $percentageUsed));
                                            $percentageRemaining = 100 - $percentageUsed;
                                            ?>
                                            <div class="bg-green-500 h-2 rounded-full" style="width: <?= $percentageRemaining ?>%"></div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="/admin/edit_department_leave_balance?department=<?= urlencode($balance['department_name']) ?>"
                                           class="bg-purple-600 hover:bg-purple-700 text-white py-1 px-10 rounded text-s items-center">
                                            <i class="fas fa-edit mr-1"></i> Modifier
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Modern confirmation dialog for department leave balance initialization
        async function handleDepartmentInitialization(event) {
            event.preventDefault();

            try {
                const confirmed = await modernConfirm({
                    title: 'Initialisation des soldes départementaux',
                    message: `
                        <div class="space-y-3">
                            <p class="font-medium">Êtes-vous sûr de vouloir initialiser les soldes de congés départementaux ?</p>
                            <div class="bg-blue-50 p-3 rounded-lg">
                                <p class="text-sm text-blue-800 font-medium mb-2">Cette action va :</p>
                                <ul class="text-sm text-blue-700 space-y-1">
                                    <li>• Créer ou réinitialiser les soldes pour tous les départements</li>
                                    <li>• Attribuer 15 jours de congés par département</li>
                                    <li>• Réinitialiser les compteurs d'utilisation</li>
                                </ul>
                            </div>
                            <p class="text-sm text-gray-600">Cette opération affectera tous les départements de l'organisation.</p>
                        </div>
                    `,
                    type: 'warning',
                    confirmText: 'Initialiser',
                    confirmButtonClass: 'modal-btn-primary'
                });

                if (confirmed) {
                    const url = event.target.closest('a').dataset.url;
                    window.location.href = url;
                }
            } catch (error) {
                console.error('Error in department initialization confirmation:', error);
            }
        }
    </script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
