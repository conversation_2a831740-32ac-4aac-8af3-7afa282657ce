<?php require_once APPROOT . '/views/includes/header.php'; ?>
<?php require_once APPROOT . '/views/includes/admin_sidebar.php'; ?>

    <div class="container mx-auto px-4 py-8">
        <!-- Breadcrumb Navigation -->
        <nav class="mb-4" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2 text-sm">
                <li>
                    <a href="/dashboard-admin" class="text-purple-600 hover:text-purple-800">
                        <i class="fas fa-home mr-1"></i>Accueil
                    </a>
                </li>
                <li>
                    <span class="text-gray-500">/</span>
                </li>
                <li>
                    <span class="text-purple-600">Paramètres du système</span>
                </li>
                <li>
                    <span class="text-gray-500">/</span>
                </li>
                <li class="text-gray-500">
                    Soldes Congés
                </li>
            </ol>
        </nav>

        <h1 class="text-2xl font-bold mb-6">Gestion des Soldes de Congés</h1>

        <?php if (isset($success)): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
                <p><?= $success ?></p>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                <p><?= $error ?></p>
            </div>
        <?php endif; ?>

        <!-- Search and Actions Section -->
        <div class="card mb-6">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-4">
                <!-- Left side - Search Controls -->
                <div class="flex-1">
                    <h2 class="text-lg font-semibold mb-4">Rechercher un employé</h2>
                    <form action="/admin/leave-balances" method="GET" class="flex flex-wrap gap-4">
                        <div class="w-full sm:w-auto">
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Nom ou prénom</label>
                            <input type="text" id="search" name="search" value="<?= $search ?? '' ?>" placeholder="Rechercher..."
                                class="w-full sm:w-64 text-sm border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                        </div>
                        <div class="w-full sm:w-auto">
                            <label for="department" class="block text-sm font-medium text-gray-700 mb-1">Département</label>
                            <select id="department" name="department"
                                class="text-sm border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                                <option value="">Tous les départements</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?= $dept ?>" <?= (isset($department) && $department === $dept) ? 'selected' : '' ?>>
                                        <?= $dept ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="w-full sm:w-auto flex items-end">
                            <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg flex items-center">
                                <i class="fas fa-search mr-2"></i> Rechercher
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Right side - Action Buttons -->
                <div class="flex-shrink-0">
                    <h2 class="text-lg font-semibold mb-4">Actions</h2>
                    <div class="flex flex-wrap gap-2">
                        <a href="#"
                           onclick="handleInitialization(event)"
                           data-url="/admin/leave-balances/initialize"
                           class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg flex items-center">
                            <i class="fas fa-sync-alt mr-2"></i> Initialiser les soldes
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employee Leave Balances Table -->
        <div class="table-card">
            <div class="p-6">
                <h2 class="text-lg font-semibold mb-4">Soldes de congés des employés</h2>
            </div>

            <?php if (empty($employees)): ?>
                <div class="p-6">
                    <p class="text-gray-500">Aucun employé trouvé.</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200" id="employeesTable">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Employé
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Département
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Congés payés
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Congés exceptionnels
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Congés sans solde
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Taux mensuel
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($employees as $employee): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?= $employee['prenom'] ?> <?= $employee['nom'] ?>
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    <?= $employee['email'] ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?= $employee['departement'] ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= $employee['balances']['payé']['remaining'] ?> / <?= $employee['balances']['payé']['total'] ?> jours
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                            <?php
                                            $used = isset($employee['balances']['payé']['used']) ? $employee['balances']['payé']['used'] : 0;
                                            $total = isset($employee['balances']['payé']['total']) ? $employee['balances']['payé']['total'] : 0;
                                            $payePercentage = ($total > 0) ? ($used / $total) * 100 : 0;
                                            $payePercentage = min(100, max(0, $payePercentage));
                                            ?>
                                            <div class="bg-green-500 h-2 rounded-full" style="width: <?= 100 - $payePercentage ?>%"></div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= $employee['balances']['exceptionnel']['remaining'] ?> / <?= $employee['balances']['exceptionnel']['total'] ?> jours
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                            <?php
                                            $exceptionnelUsed = isset($employee['balances']['exceptionnel']['used']) ? $employee['balances']['exceptionnel']['used'] : 0;
                                            $exceptionnelTotal = isset($employee['balances']['exceptionnel']['total']) ? $employee['balances']['exceptionnel']['total'] : 0;
                                            $exceptionnelPercentage = ($exceptionnelTotal > 0) ? ($exceptionnelUsed / $exceptionnelTotal) * 100 : 0;
                                            $exceptionnelPercentage = min(100, max(0, $exceptionnelPercentage));
                                            ?>
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: <?= 100 - $exceptionnelPercentage ?>%"></div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            Illimité
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                            <div class="bg-purple-500 h-2 rounded-full" style="width: 100%"></div>
                                        </div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            <span>Utilisé: <?= $employee['balances']['sans solde']['used'] ?> jours</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= $employee['balances']['payé']['accrual_rate'] ?> jours/mois
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            <?php if ($employee['balances']['payé']['last_accrual_date']): ?>
                                                Dernière attribution: <?= date('d/m/Y', strtotime($employee['balances']['payé']['last_accrual_date'])) ?>
                                            <?php else: ?>
                                                Aucune attribution
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="/admin/leave-balances/edit/<?= $employee['id'] ?>" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                            Modifier
                                        </a>
                                        <a href="/admin/leave-balances/history/<?= $employee['id'] ?>" class="text-gray-600 hover:text-gray-900">
                                            Historique
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="flex justify-center mt-6">
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <?php if ($currentPage > 1): ?>
                                <a href="/admin/leave-balances?page=<?= $currentPage - 1 ?>&search=<?= $search ?? '' ?>&department=<?= $department ?? '' ?>"
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Précédent</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>

                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <a href="/admin/leave-balances?page=<?= $i ?>&search=<?= $search ?? '' ?>&department=<?= $department ?? '' ?>"
                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium <?= $i === $currentPage ? 'text-indigo-600 bg-indigo-50' : 'text-gray-700 hover:bg-gray-50' ?>">
                                    <?= $i ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($currentPage < $totalPages): ?>
                                <a href="/admin/leave-balances?page=<?= $currentPage + 1 ?>&search=<?= $search ?? '' ?>&department=<?= $department ?? '' ?>"
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Suivant</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Real-time search functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search');
            const departmentSelect = document.getElementById('department');
            const tableRows = document.querySelectorAll('#employeesTable tbody tr');

            // Function to filter table rows based on search input and department
            function filterTable() {
                const searchTerm = searchInput.value.toLowerCase();
                const departmentFilter = departmentSelect.value.toLowerCase();

                tableRows.forEach(row => {
                    // Get employee name from first column
                    const employeeNameElement = row.querySelector('td:first-child .text-sm.font-medium');
                    const employeeName = employeeNameElement ? employeeNameElement.textContent.toLowerCase() : '';

                    // Get employee email from first column
                    const employeeEmailElement = row.querySelector('td:first-child .text-sm.text-gray-500');
                    const employeeEmail = employeeEmailElement ? employeeEmailElement.textContent.toLowerCase() : '';

                    // Get department from second column
                    const departmentElement = row.querySelector('td:nth-child(2) .text-sm');
                    const department = departmentElement ? departmentElement.textContent.toLowerCase() : '';

                    // Check if row matches search criteria
                    const matchesSearch = searchTerm === '' ||
                                        employeeName.includes(searchTerm) ||
                                        employeeEmail.includes(searchTerm);

                    const matchesDepartment = departmentFilter === '' ||
                                            department.includes(departmentFilter);

                    // Show or hide row based on filters
                    if (matchesSearch && matchesDepartment) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });

                // Update "no results" message
                updateNoResultsMessage();
            }

            // Function to show/hide "no results" message
            function updateNoResultsMessage() {
                const visibleRows = Array.from(tableRows).filter(row => row.style.display !== 'none');
                const tableBody = document.querySelector('#employeesTable tbody');
                let noResultsRow = document.getElementById('no-results-row');

                if (visibleRows.length === 0) {
                    if (!noResultsRow) {
                        noResultsRow = document.createElement('tr');
                        noResultsRow.id = 'no-results-row';
                        noResultsRow.innerHTML = '<td colspan="7" class="px-6 py-4 text-center text-gray-500">Aucun employé trouvé avec les critères de recherche.</td>';
                        tableBody.appendChild(noResultsRow);
                    }
                    noResultsRow.style.display = '';
                } else {
                    if (noResultsRow) {
                        noResultsRow.style.display = 'none';
                    }
                }
            }

            // Add event listeners for real-time filtering
            if (searchInput) {
                searchInput.addEventListener('input', filterTable);
            }

            if (departmentSelect) {
                departmentSelect.addEventListener('change', filterTable);
            }

            // Initial filter on page load
            filterTable();
        });

        // Modern confirmation dialog for leave balance initialization
        async function handleInitialization(event) {
            event.preventDefault();

            try {
                const confirmed = await confirmInitialization();
                if (confirmed) {
                    const url = event.target.closest('a').dataset.url;
                    window.location.href = url;
                }
            } catch (error) {
                console.error('Error in initialization confirmation:', error);
            }
        }
    </script>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
