<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Demandes à approuver - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once __DIR__ . '/../shared/sidebar_admin.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Demandes à approuver</h1>
                    <p class="text-gray-600">Validation des demandes de congés</p>
                </div>
                <a href="/planning" class="text-teal-600 hover:text-teal-800 flex items-center">
                    <i class="fas fa-calendar-alt mr-1"></i> Voir le planning
                </a>
            </div>
        </header>

        <?php if (isset($success)): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded">
                <p><?= $success ?></p>
            </div>
        <?php endif; ?>

        <?php if (empty($demandes)): ?>
            <div class="card text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                    <i class="fas fa-check text-green-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune demande en attente</h3>
                <p class="text-gray-500">Toutes les demandes de congés ont été traitées. Revenez plus tard pour vérifier si de nouvelles demandes ont été soumises.</p>
            </div>
        <?php else: ?>
            <div class="space-y-4">
                <?php foreach ($demandes as $demande): ?>
                    <div class="card relative overflow-hidden">
                        <div class="absolute top-0 bottom-0 left-0 w-1 bg-teal-500"></div>
                        <div class="flex flex-col md:flex-row md:justify-between md:items-center">
                            <div class="mb-4 md:mb-0">
                                <h3 class="text-lg font-semibold text-gray-800">
                                    <?= htmlspecialchars($demande['prenom'] . ' ' . $demande['nom']) ?>
                                </h3>
                                <div class="text-sm text-gray-500">
                                    <span class="inline-block mr-3">
                                        <i class="fas fa-hashtag mr-1"></i> <?= !empty($demande['reference_demande']) ? htmlspecialchars($demande['reference_demande']) : '<span class="text-gray-400">-</span>' ?>
                                    </span>
                                    <span class="inline-block mr-3">
                                        <i class="fas fa-building mr-1"></i> <?= htmlspecialchars($demande['departement'] ?? 'Non spécifié') ?>
                                    </span>
                                    <span class="inline-block mr-3">
                                        <i class="fas fa-calendar-alt mr-1"></i> <?= date('d/m/Y', strtotime($demande['date_debut'])) ?> - <?= date('d/m/Y', strtotime($demande['date_fin'])) ?>
                                    </span>
                                    <span class="inline-block">
                                        <i class="fas fa-clock mr-1"></i> <?= $demande['nbJours'] ?> jour<?= $demande['nbJours'] > 1 ? 's' : '' ?>
                                    </span>
                                </div>
                                <div class="mt-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                                        <?= htmlspecialchars($demande['type_formatted']) ?>
                                    </span>
                                </div>
                            </div>
                            <div class="flex flex-wrap gap-2">
                                <button type="button" onclick="showDetails(<?= json_encode($demande['prenom'] . ' ' . $demande['nom']) ?>, <?= json_encode($demande['type_formatted']) ?>, <?= json_encode(date('d/m/Y', strtotime($demande['date_debut']))) ?>, <?= json_encode(date('d/m/Y', strtotime($demande['date_fin']))) ?>, <?= json_encode($demande['nbJours']) ?>, <?= json_encode($demande['motif'] ?? 'Non spécifié') ?>, <?= json_encode($demande['id']) ?>, <?= json_encode($demande['reference_demande'] ?? '') ?>)" class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                                    <i class="fas fa-eye mr-1"></i> Détails
                                </button>
                                <a href="#" onclick="handleApproval(event, '/planificateur/approuverDemande?id=<?= $demande['id'] ?>')" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    <i class="fas fa-check mr-1"></i> Approuver
                                </a>
                                <a href="/planificateur/rejeterDemande?id=<?= $demande['id'] ?>" class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                    <i class="fas fa-times mr-1"></i> Rejeter
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Details Modal -->
    <div id="detailsModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Détails de la demande</h3>
                    <button type="button" onclick="closeModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="px-6 py-4">
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Référence</p>
                    <p class="text-base font-medium text-gray-900" id="modal-reference">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Employé</p>
                    <p class="text-base font-medium text-gray-900" id="modal-employee">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Type de congé</p>
                    <p class="text-base font-medium text-gray-900" id="modal-type">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Période</p>
                    <p class="text-base font-medium text-gray-900" id="modal-period">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Durée</p>
                    <p class="text-base font-medium text-gray-900" id="modal-duration">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Motif</p>
                    <p class="text-base font-medium text-gray-900" id="modal-motif">-</p>
                </div>
            </div>
            <div class="bg-gray-50 px-6 py-3 flex justify-end space-x-2">
                <button type="button" onclick="closeModal()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                    Fermer
                </button>
                <a href="#" id="modal-approve-link" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <i class="fas fa-check mr-1"></i> Approuver
                </a>
                <a href="#" id="modal-reject-link" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <i class="fas fa-times mr-1"></i> Rejeter
                </a>
            </div>
        </div>
    </div>

    <script>
        function showDetails(employee, type, dateDebut, dateFin, duree, motif, demandeId, reference) {
            document.getElementById('modal-employee').textContent = employee;
            document.getElementById('modal-type').textContent = type;
            document.getElementById('modal-period').textContent = dateDebut + (dateDebut !== dateFin ? ' - ' + dateFin : '');
            document.getElementById('modal-duration').textContent = duree + ' jour' + (duree > 1 ? 's' : '');
            document.getElementById('modal-motif').textContent = motif || 'Aucun motif spécifié';

            // Update reference if element exists
            const referenceElement = document.getElementById('modal-reference');
            if (referenceElement) {
                referenceElement.textContent = reference || '-';
            }

            // Set the links for approve and reject
            document.getElementById('modal-approve-link').href = '/planificateur/approuverDemande?id=' + demandeId;
            document.getElementById('modal-reject-link').href = '/planificateur/rejeterDemande?id=' + demandeId;

            // Show the modal
            document.getElementById('detailsModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('detailsModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('detailsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !document.getElementById('detailsModal').classList.contains('hidden')) {
                closeModal();
            }
        });

        // Modern confirmation for approval actions
        async function handleApproval(event, url) {
            event.preventDefault();

            try {
                const confirmed = await confirmSuccess(
                    'Êtes-vous sûr de vouloir approuver cette demande de congé ?',
                    'Approuver la demande'
                );

                if (confirmed) {
                    window.location.href = url;
                }
            } catch (error) {
                console.error('Error in approval confirmation:', error);
            }
        }
    </script>
</body>
</html>
