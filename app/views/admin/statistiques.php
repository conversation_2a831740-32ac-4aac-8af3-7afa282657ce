<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Statistiques Admin - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <!-- Chart.js for statistics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>

<body class="bg-gray-50">
    <?php include_once __DIR__ . '/../shared/sidebar_admin.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Statistiques Globales</h1>
                    <p class="text-gray-600">Vue d'ensemble de l'activité des congés dans l'organisation</p>
                </div>
                <div>
                    <select id="yearFilter"
                        class="border border-gray-300 rounded-md px-3 py-1.5 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-purple-600 focus:border-transparent">
                        <?php
                            $currentYear = date('Y');
                            $prevYear = $currentYear - 1;
                            $nextYear = $currentYear + 1;
                        ?>
                        <option value="<?= $nextYear ?>"><?= $nextYear ?></option>
                        <option value="<?= $currentYear ?>" selected><?= $currentYear ?></option>
                        <option value="<?= $prevYear ?>"><?= $prevYear ?></option>
                    </select>
                </div>
            </div>
        </header>

        <!-- Stats Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-sm font-semibold text-gray-800">Demandes totales</h2>
                    <span class="text-purple-500"><i class="fas fa-file-alt"></i></span>
                </div>
                <div class="text-3xl font-bold text-gray-900"><?= $totalDemandes ?></div>
                <div class="mt-1 text-sm text-gray-500">demandes créées</div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-sm font-semibold text-gray-800">Demandes approuvées</h2>
                    <span class="text-green-500"><i class="fas fa-check-circle"></i></span>
                </div>
                <div class="text-3xl font-bold text-gray-900"><?= $demandesApprouvees ?></div>
                <div class="mt-1 text-sm text-green-500"><?= $pourcentageApprouve ?>% d'approbation</div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-sm font-semibold text-gray-800">Demandes rejetées</h2>
                    <span class="text-red-500"><i class="fas fa-times-circle"></i></span>
                </div>
                <div class="text-3xl font-bold text-gray-900"><?= $demandesRejetees ?></div>
                <div class="mt-1 text-sm text-gray-500"><?= round(($demandesRejetees/$totalDemandes)*100) ?>% de rejet
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-sm font-semibold text-gray-800">En attente</h2>
                    <span class="text-yellow-500"><i class="fas fa-clock"></i></span>
                </div>
                <div class="text-3xl font-bold text-gray-900"><?= $demandesEnAttente ?></div>
                <div class="mt-1 text-sm text-gray-500"><?= round(($demandesEnAttente/$totalDemandes)*100) ?>% en
                    attente</div>
            </div>
        </div>

        <!-- Charts -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Monthly Requests -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Demandes par mois</h2>
                <canvas id="monthlyRequestsChart" height="250"></canvas>
            </div>

            <!-- Requests by Type -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Demandes par type</h2>
                <canvas id="requestsByTypeChart" height="250"></canvas>
            </div>
        </div>

        <!-- Department Analysis -->
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Analyse par département</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Département</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Effectif</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Demandes</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Taux d'approbation</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Jours/personne</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">Développement</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">54</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">78%</div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 78%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">18.3</td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">Marketing</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">42</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">92%</div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 92%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">22.5</td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">Ressources Humaines</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">28</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">85%</div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 85%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">19.2</td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">Comptabilité</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">7</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">36</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">68%</div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 68%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15.7</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Peak Periods Analysis -->
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Périodes de forte demande</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-3">Jours les plus demandés</h3>
                    <ul class="space-y-2">
                        <li class="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                            <span class="font-medium">15 août 2024</span>
                            <span class="text-red-600 font-medium">32 demandes</span>
                        </li>
                        <li class="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                            <span class="font-medium">24 décembre 2024</span>
                            <span class="text-orange-600 font-medium">28 demandes</span>
                        </li>
                        <li class="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                            <span class="font-medium">31 décembre 2024</span>
                            <span class="text-orange-600 font-medium">25 demandes</span>
                        </li>
                        <li class="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                            <span class="font-medium">14 juillet 2024</span>
                            <span class="text-yellow-600 font-medium">18 demandes</span>
                        </li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-3">Mois les plus chargés</h3>
                    <canvas id="busyMonthsChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script src="/assets/js/statistiques_admin.js">
// Define the data for charts
var chartData = {
    demandesParMois: <?= json_encode(array_values($demandesParMois)) ?>,
    demandesParTypeLabels: <?= json_encode(array_keys($demandesParType)) ?>,
    demandesParTypeValues: <?= json_encode(array_values($demandesParType)) ?>
};
    </script>
</body>

</html>