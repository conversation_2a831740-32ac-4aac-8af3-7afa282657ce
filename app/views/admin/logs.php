<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Journaux système - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once __DIR__ . '/../shared/sidebar_admin.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Journaux système</h1>
                    <p class="text-gray-600">Activités et événements du système</p>
                </div>
                <div class="flex space-x-2">
                    <a href="#" onclick="handleLogClear(event)" data-url="/logs?clear=1" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg">
                        <i class="fas fa-trash-alt mr-2"></i> Effacer
                    </a>
                    <a href="/export-logs" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg">
                        <i class="fas fa-file-download mr-2"></i> Exporter
                    </a>
                </div>
            </div>
        </header>

        <div class="bg-white rounded-lg shadow-sm mb-6 p-4 flex flex-wrap items-center justify-between">
            <div class="flex flex-wrap items-center mb-2 sm:mb-0">
                <div class="mr-4">
                    <label for="action-filter" class="block text-sm font-medium text-gray-700 mb-1">Type d'action</label>
                    <select id="action-filter" class="text-sm border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Tous</option>
                        <option value="login">Login</option>
                        <option value="logout">Logout</option>
                        <option value="create">Création</option>
                        <option value="update">Modification</option>
                        <option value="delete">Suppression</option>
                        <option value="backup">Sauvegarde</option>
                    </select>
                </div>
                <div class="mr-4">
                    <label for="date-filter" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                    <input type="date" id="date-filter" class="text-sm border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                </div>
            </div>
            <div class="w-full sm:w-auto mt-2 sm:mt-0">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Rechercher</label>
                <input type="text" id="search" placeholder="Utilisateur, action, détails..." class="w-full sm:w-64 text-sm border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Heure</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilisateur</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Détails</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($logs)): ?>
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-gray-500">Aucune entrée de journal trouvée</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($logs as $log): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= date('d/m/Y H:i:s', strtotime($log['date'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?= htmlspecialchars($log['user']) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($log['action'] === 'Login'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                <?= htmlspecialchars($log['action']) ?>
                                            </span>
                                        <?php elseif ($log['action'] === 'Logout'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                <?= htmlspecialchars($log['action']) ?>
                                            </span>
                                        <?php elseif ($log['action'] === 'Create request'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                                                <?= htmlspecialchars($log['action']) ?>
                                            </span>
                                        <?php elseif ($log['action'] === 'Approve request'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                <?= htmlspecialchars($log['action']) ?>
                                            </span>
                                        <?php elseif ($log['action'] === 'Backup'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                <?= htmlspecialchars($log['action']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                <?= htmlspecialchars($log['action']) ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500">
                                        <?= htmlspecialchars($log['details']) ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Affichage de <span class="font-medium">1</span> à <span class="font-medium"><?= count($logs) ?></span> sur <span class="font-medium"><?= count($logs) ?></span> résultats
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Previous</span>
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            <a href="#" aria-current="page" class="z-10 bg-purple-50 border-purple-500 text-purple-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                1
                            </a>
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Next</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Modern confirmation for log clearing
        async function handleLogClear(event) {
            event.preventDefault();

            try {
                const confirmed = await confirmDanger(
                    `Êtes-vous sûr de vouloir effacer tous les journaux système ?<br><br>
                    <div class="bg-red-50 p-3 rounded-lg mt-3">
                        <p class="text-sm text-red-800 font-medium">⚠️ Cette action est irréversible</p>
                        <p class="text-sm text-red-700">Tous les journaux d'activité seront définitivement supprimés.</p>
                    </div>`,
                    'Effacer les journaux'
                );

                if (confirmed) {
                    const url = event.target.closest('a').dataset.url;
                    window.location.href = url;
                }
            } catch (error) {
                console.error('Error in log clear confirmation:', error);
            }
        }
    </script>
</body>
</html>
