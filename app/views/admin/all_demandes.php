<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toutes les demandes - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once __DIR__ . '/../shared/sidebar_admin.php'; ?>

    <div class="md:ml-[240px] p-4 md:p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 class="text-xl sm:text-2xl font-bold text-gray-800">Toutes les demandes</h1>
                    <p class="text-sm sm:text-base text-gray-600">Liste des demandes de congés dans le système</p>
                </div>
                <!-- <div>
                    <a href="/export-demandes" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg flex items-center text-sm sm:text-base whitespace-nowrap">
                        <i class="fas fa-file-excel mr-2"></i> Exporter
                    </a>
                </div> -->
            </div>
        </header>

        <div class="bg-white rounded-lg shadow-sm mb-6 p-4">
            <form action="/all-demandes" method="GET" class="flex flex-wrap items-center justify-between">
                <div class="flex flex-wrap items-center mb-2 sm:mb-0">
                    <div class="mr-4">
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                        <select id="status" name="status" class="text-sm border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                            <option value="">Tous</option>
                            <option value="approved" <?= isset($_GET['status']) && $_GET['status'] === 'approved' ? 'selected' : '' ?>>Approuvée</option>
                            <option value="pending" <?= isset($_GET['status']) && $_GET['status'] === 'pending' ? 'selected' : '' ?>>En attente</option>
                            <option value="rejected" <?= isset($_GET['status']) && $_GET['status'] === 'rejected' ? 'selected' : '' ?>>Rejetée</option>
                        </select>
                    </div>
                    <div class="mr-4">
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                        <select id="type" name="type" class="text-sm border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                            <option value="">Tous</option>
                            <option value="paid" <?= isset($_GET['type']) && $_GET['type'] === 'paid' ? 'selected' : '' ?>>Congé payé</option>
                            <option value="unpaid" <?= isset($_GET['type']) && $_GET['type'] === 'unpaid' ? 'selected' : '' ?>>Congé sans solde</option>
                            <option value="sick" <?= isset($_GET['type']) && $_GET['type'] === 'sick' ? 'selected' : '' ?>>Maladie</option>
                            <option value="exceptional" <?= isset($_GET['type']) && $_GET['type'] === 'exceptional' ? 'selected' : '' ?>>Congé exceptionnel</option>
                        </select>
                    </div>
                </div>
                <div class="w-full sm:w-auto mt-2 sm:mt-0">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Rechercher</label>
                    <div class="flex">
                        <input type="text" id="search" name="search" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>" placeholder="Nom, prénom, motif..." class="w-full sm:w-64 text-sm border-gray-300 rounded-l-md focus:ring-purple-500 focus:border-purple-500">
                        <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white px-4 rounded-r-md">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <div class="table-card">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Référence</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employé</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Début</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Fin</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Motif</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($demandes)): ?>
                            <tr>
                                <td colspan="9" class="px-6 py-4 text-center text-gray-500">Aucune demande trouvée</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($demandes as $demande): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?= !empty($demande['reference_demande']) ? htmlspecialchars($demande['reference_demande']) : '<span class="text-gray-400">-</span>' ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($demande['user_prenom']) ?> <?= htmlspecialchars($demande['user_nom']) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= htmlspecialchars($demande['type']) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= date('d/m/Y', strtotime($demande['date_debut'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= date('d/m/Y', strtotime($demande['date_fin'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php
                                            $start = new DateTime($demande['date_debut']);
                                            $end = new DateTime($demande['date_fin']);
                                            $interval = $start->diff($end);
                                            echo ($interval->days + 1) . ' jour(s)';
                                        ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($demande['statut'] === 'Approuvée'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                <?= htmlspecialchars($demande['statut']) ?>
                                            </span>
                                        <?php elseif ($demande['statut'] === 'En attente'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                <?= htmlspecialchars($demande['statut']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                <?= htmlspecialchars($demande['statut']) ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= htmlspecialchars($demande['motif']) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="/demande-details?id=<?= $demande['id'] ?>" class="text-indigo-600 hover:text-indigo-900">
                                            <i class="fas fa-eye"></i> Détails
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Affichage de <span class="font-medium">1</span> à <span class="font-medium"><?= count($demandes) ?></span> sur <span class="font-medium"><?= count($demandes) ?></span> résultats
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Previous</span>
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            <a href="#" aria-current="page" class="z-10 bg-purple-50 border-purple-500 text-purple-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                1
                            </a>
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Next</span>
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Client-side filtering for quick results
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search');
            const tableRows = document.querySelectorAll('tbody tr');

            // Function to filter table rows based on search input
            function filterTable() {
                const searchTerm = searchInput.value.toLowerCase();

                tableRows.forEach(row => {
                    const reference = row.querySelector('td:first-child')?.textContent.toLowerCase() || '';
                    const employeeName = row.querySelector('td:nth-child(2)')?.textContent.toLowerCase() || '';
                    const leaveType = row.querySelector('td:nth-child(3)')?.textContent.toLowerCase() || '';
                    const motif = row.querySelector('td:nth-child(8)')?.textContent.toLowerCase() || '';

                    if (reference.includes(searchTerm) || employeeName.includes(searchTerm) || leaveType.includes(searchTerm) || motif.includes(searchTerm)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }

            // Add event listener for real-time filtering
            searchInput.addEventListener('input', filterTable);
        });
    </script>
</body>
</html>
