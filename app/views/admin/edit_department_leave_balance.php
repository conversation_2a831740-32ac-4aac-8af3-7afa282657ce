<?php require_once APPROOT . '/views/includes/header.php'; ?>
<?php require_once APPROOT . '/views/includes/admin_sidebar.php'; ?>

<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Modifier le solde départemental - <?= htmlspecialchars($department) ?></h1>

    <nav class="mb-6" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-2 text-sm">
            <li>
                <a href="/dashboard-admin" class="text-purple-600 hover:text-purple-800">
                    <i class="fas fa-home mr-1"></i>Accueil
                </a>
            </li>
            <li>
                <span class="text-gray-500">/</span>
            </li>
            <li>
                <span class="text-purple-600">Paramètres du système</span>
            </li>
            <li>
                <span class="text-gray-500">/</span>
            </li>
            <li>
                <a href="/admin/department-leave-balances" class="text-purple-600 hover:text-purple-800">
                    Soldes Département
                </a>
            </li>
            <li>
                <span class="text-gray-500">/</span>
            </li>
            <li class="text-gray-500">
                Modifier <?= htmlspecialchars($department) ?>
            </li>
        </ol>
    </nav>

    <?php if (isset($error)): ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
            <p><?= $error ?></p>
        </div>
    <?php endif; ?>

    <div class="card mb-6">
        <h2 class="text-lg font-semibold mb-4">Informations sur le Département</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <p class="text-gray-600">Département:</p>
                <p class="font-medium"><?= htmlspecialchars($department) ?></p>
            </div>
            <div>
                <p class="text-gray-600">Année:</p>
                <p class="font-medium"><?= $year ?></p>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div class="bg-blue-50 p-4 rounded">
                <p class="text-gray-600">Solde total actuel:</p>
                <p class="text-xl font-bold text-blue-700"><?= number_format($balance['total_days'], 1) ?> jours</p>
            </div>
            <div class="bg-orange-50 p-4 rounded">
                <p class="text-gray-600">Jours utilisés:</p>
                <p class="text-xl font-bold text-orange-700"><?= number_format($balance['used_days'], 1) ?> jours</p>
            </div>
            <div class="bg-green-50 p-4 rounded">
                <p class="text-gray-600">Jours restants:</p>
                <p class="text-xl font-bold text-green-700"><?= number_format($balance['remaining_days'], 1) ?> jours</p>
            </div>
        </div>

        <div class="w-full bg-gray-200 rounded-full h-4 mb-6">
            <?php
            $usedDays = isset($balance['used_days']) ? $balance['used_days'] : 0;
            $totalDays = isset($balance['total_days']) ? $balance['total_days'] : 0;
            $percentageUsed = ($totalDays > 0) ? ($usedDays / $totalDays) * 100 : 0;
            $percentageUsed = min(100, max(0, $percentageUsed));
            ?>
            <div class="bg-blue-500 h-4 rounded-full" style="width: <?= 100 - $percentageUsed ?>%"></div>
        </div>
    </div>

    <div class="card">
        <h2 class="text-lg font-semibold mb-4">Modifier le solde</h2>
        <form action="/admin/edit_department_leave_balance?department=<?= urlencode($department) ?>&year=<?= $year ?>" method="POST">
            <div class="mb-4">
                <label for="total_days" class="block text-sm font-medium text-gray-700 mb-1">Solde total (jours) :</label>
                <input type="number" id="total_days" name="total_days" min="0" step="0.5" value="<?= $balance['total_days'] ?>"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    required>
                <p class="text-sm text-gray-500 mt-1">
                    Note: Modifier le solde total affectera le nombre de jours restants, mais ne modifiera pas les jours déjà utilisés.
                </p>
            </div>

            <div class="flex flex-wrap gap-4">
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
                    Enregistrer les modifications
                </button>
                <a href="/admin/department-leave-balances" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-2 px-4 rounded">
                    Annuler
                </a>
            </div>
        </form>
    </div>
</div>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
