<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>C<PERSON>er un utilisateur - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once __DIR__ . '/../shared/sidebar_admin.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Créer un nouvel utilisateur</h1>
                    <p class="text-gray-600">Ajouter un nouvel utilisateur au système</p>
                </div>
                <a href="/list-users" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i> Retour à la liste
                </a>
            </div>
        </header>

        <?php if (isset($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded">
                <p><?= htmlspecialchars($error) ?></p>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded">
                <p><?= htmlspecialchars($success) ?></p>
                <div class="mt-2">
                    <a href="/create-user" class="text-green-600 hover:underline">Créer un autre utilisateur</a> |
                    <a href="/list-users" class="text-green-600 hover:underline">Retour à la liste</a>
                </div>
            </div>
        <?php endif; ?>

        <div class="bg-white rounded-lg shadow-sm p-6">
            <form action="/create-user" method="POST" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="matricule_rh" class="block text-sm font-medium text-gray-700">Matricule RH *</label>
                        <input type="text" name="matricule_rh" id="matricule_rh" value="<?= htmlspecialchars($data['matricule_rh'] ?? '') ?>" required
                               placeholder="Ex: EMP-001, RSC001, etc."
                               class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        <p class="mt-1 text-sm text-gray-500">Identifiant unique RH (sera généré automatiquement si vide)</p>
                    </div>

                    <div>
                        <label for="nom" class="block text-sm font-medium text-gray-700">Nom *</label>
                        <input type="text" name="nom" id="nom" value="<?= htmlspecialchars($data['nom'] ?? '') ?>" required
                               class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="prenom" class="block text-sm font-medium text-gray-700">Prénom *</label>
                        <input type="text" name="prenom" id="prenom" value="<?= htmlspecialchars($data['prenom'] ?? '') ?>" required
                               class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email * <span class="text-purple-600">(Identifiant de connexion principal)</span></label>
                        <input type="email" name="email" id="email" value="<?= htmlspecialchars($data['email'] ?? '') ?>" required
                               class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        <p class="mt-1 text-sm text-gray-500">L'email sera utilisé comme identifiant de connexion principal. Assurez-vous qu'il soit unique.</p>
                    </div>

                    <div>
                        <label for="login" class="block text-sm font-medium text-gray-700">Nom d'utilisateur * <span class="text-gray-500">(Compatibilité)</span></label>
                        <input type="text" name="login" id="login" value="<?= htmlspecialchars($data['login'] ?? '') ?>" required
                               class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        <p class="mt-1 text-sm text-gray-500">Maintenu pour la compatibilité avec les anciens systèmes.</p>
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">Mot de passe *</label>
                        <input type="password" name="password" id="password" required
                               class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700">Rôle *</label>
                        <select name="role" id="role" required
                                class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            <option value="">Sélectionner un rôle</option>
                            <option value="employe" <?= (($data['role'] ?? '') === 'employe') ? 'selected' : '' ?>>Employé</option>
                            <option value="responsable" <?= (($data['role'] ?? '') === 'responsable') ? 'selected' : '' ?>>Responsable</option>
                            <option value="planificateur" <?= (($data['role'] ?? '') === 'planificateur') ? 'selected' : '' ?>>Planificateur</option>
                            <option value="admin" <?= (($data['role'] ?? '') === 'admin') ? 'selected' : '' ?>>Administrateur</option>
                        </select>
                    </div>

                    <div>
                        <label for="departement" class="block text-sm font-medium text-gray-700">Département *</label>
                        <select name="departement" id="departement" required
                                class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            <option value="">Sélectionner un département</option>
                            <option value="IT" <?= (($data['departement'] ?? '') === 'IT') ? 'selected' : '' ?>>IT</option>
                            <option value="RH" <?= (($data['departement'] ?? '') === 'RH') ? 'selected' : '' ?>>Ressources Humaines</option>
                            <option value="Finance" <?= (($data['departement'] ?? '') === 'Finance') ? 'selected' : '' ?>>Finance</option>
                            <option value="Marketing" <?= (($data['departement'] ?? '') === 'Marketing') ? 'selected' : '' ?>>Marketing</option>
                            <option value="Ventes" <?= (($data['departement'] ?? '') === 'Ventes') ? 'selected' : '' ?>>Ventes</option>
                            <option value="Support client" <?= (($data['departement'] ?? '') === 'Support client') ? 'selected' : '' ?>>Support client</option>
                            <option value="Production" <?= (($data['departement'] ?? '') === 'Production') ? 'selected' : '' ?>>Production</option>
                            <option value="Logistique" <?= (($data['departement'] ?? '') === 'Logistique') ? 'selected' : '' ?>>Logistique</option>
                        </select>
                    </div>

                    <div>
                        <label for="poste" class="block text-sm font-medium text-gray-700">Poste</label>
                        <input type="text" name="poste" id="poste" value="<?= htmlspecialchars($data['poste'] ?? '') ?>"
                               class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="telephone" class="block text-sm font-medium text-gray-700">Téléphone</label>
                        <input type="tel" name="telephone" id="telephone" value="<?= htmlspecialchars($data['telephone'] ?? '') ?>"
                               class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="date_entree" class="block text-sm font-medium text-gray-700">Date d'entrée</label>
                        <input type="date" name="date_entree" id="date_entree" value="<?= htmlspecialchars($data['date_entree'] ?? '') ?>"
                               class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-6 border-t">
                    <a href="/list-users" class="bg-gray-300 hover:bg-gray-400 text-gray-800 py-2 px-4 rounded-lg">
                        Annuler
                    </a>
                    <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg">
                        <i class="fas fa-save mr-2"></i> Créer l'utilisateur
                    </button>
                </div>
            </form>
        </div>

        <!-- CSV Import Section -->
        <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Import CSV</h3>
            <p class="text-sm text-gray-600 mb-4">
                Importez plusieurs utilisateurs à la fois en utilisant un fichier CSV.
                Le fichier doit contenir les colonnes : <strong>matricule_rh, nom, prenom, login, email, password, role, departement</strong> (obligatoires),
                puis poste, telephone, date_entree (optionnelles)
            </p>

            <form action="/admin/import-csv" method="POST" enctype="multipart/form-data" class="space-y-4">
                <div>
                    <label for="csv_file" class="block text-sm font-medium text-gray-700">Fichier CSV</label>
                    <input type="file" name="csv_file" id="csv_file" accept=".csv" required
                           class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                </div>

                <div class="flex justify-between items-center">
                    <a href="/admin/download-csv-template" class="text-purple-600 hover:underline text-sm">
                        <i class="fas fa-download mr-1"></i> Télécharger le modèle CSV
                    </a>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg">
                        <i class="fas fa-upload mr-2"></i> Importer CSV
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Auto-generate matricule_rh when fields change
        function generateMatricule() {
            const role = document.getElementById('role').value;
            const nom = document.getElementById('nom').value;
            const prenom = document.getElementById('prenom').value;
            const matriculeField = document.getElementById('matricule_rh');

            if (role && nom && prenom && !matriculeField.value) {
                const prefixes = {
                    'employe': 'EMP',
                    'responsable': 'RSC',
                    'planificateur': 'PLN',
                    'admin': 'ADM'
                };

                const prefix = prefixes[role] || 'EMP';
                const initials = prenom.charAt(0).toUpperCase() + nom.charAt(0).toUpperCase();
                const number = Math.floor(Math.random() * 900) + 100; // 3-digit number

                matriculeField.value = prefix + '-' + initials + number;
            }
        }

        // Add event listeners
        document.getElementById('role').addEventListener('change', generateMatricule);
        document.getElementById('nom').addEventListener('blur', generateMatricule);
        document.getElementById('prenom').addEventListener('blur', generateMatricule);

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            if (password.length < 6) {
                e.preventDefault();
                alert('Le mot de passe doit contenir au moins 6 caractères');
                return false;
            }

            const departement = document.getElementById('departement').value;
            if (!departement) {
                e.preventDefault();
                alert('Veuillez sélectionner un département');
                return false;
            }
        });
    </script>
</body>
</html>
