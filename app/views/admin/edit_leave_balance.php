<?php require_once APPROOT . '/views/includes/header.php'; ?>
<?php require_once APPROOT . '/views/includes/admin_sidebar.php'; ?>

    <div class="container mx-auto px-4 py-8">
        <!-- Breadcrumb Navigation -->
        <nav class="mb-4" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2 text-sm">
                <li>
                    <a href="/dashboard-admin" class="text-purple-600 hover:text-purple-800">
                        <i class="fas fa-home mr-1"></i>Accueil
                    </a>
                </li>
                <li>
                    <span class="text-gray-500">/</span>
                </li>
                <li>
                    <span class="text-purple-600">Paramètres du système</span>
                </li>
                <li>
                    <span class="text-gray-500">/</span>
                </li>
                <li>
                    <a href="/admin/leave_balances" class="text-purple-600 hover:text-purple-800">
                        Soldes Congés
                    </a>
                </li>
                <li>
                    <span class="text-gray-500">/</span>
                </li>
                <li class="text-gray-500">
                    Modifier
                </li>
            </ol>
        </nav>

        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">Modifier les soldes de congés</h1>
            <a href="/admin/leave_balances" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded">
                Retour
            </a>
        </div>

        <?php if (isset($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                <p><?= $error ?></p>
            </div>
        <?php endif; ?>

        <div class="card mb-6">
            <h2 class="text-lg font-semibold mb-4">Informations de l'employé</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <p class="text-gray-600">Nom:</p>
                    <p class="font-medium"><?= $user['prenom'] ?> <?= $user['nom'] ?></p>
                </div>
                <div>
                    <p class="text-gray-600">Email:</p>
                    <p class="font-medium"><?= $user['email'] ?></p>
                </div>
                <div>
                    <p class="text-gray-600">Département:</p>
                    <p class="font-medium"><?= $user['departement'] ?></p>
                </div>
                <div>
                    <p class="text-gray-600">Rôle:</p>
                    <p class="font-medium"><?= ucfirst($user['role']) ?></p>
                </div>
            </div>
        </div>

        <div class="card">
            <h2 class="text-lg font-semibold mb-4">Soldes de congés</h2>
            <form action="/admin/leave-balances/edit/<?= $user['id'] ?>" method="POST">
                <div class="space-y-6">
                    <!-- Congés payés -->
                    <div class="border-b border-gray-200 pb-6">
                        <h3 class="text-md font-medium mb-3">Congés payés</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label for="paye_total" class="block text-sm font-medium text-gray-700 mb-1">
                                    Total annuel
                                </label>
                                <input type="number" step="0.5" min="0" id="paye_total" name="paye_total"
                                    value="<?= $balances['payé']['total'] ?>"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            </div>
                            <div>
                                <label for="paye_used" class="block text-sm font-medium text-gray-700 mb-1">
                                    Jours utilisés
                                </label>
                                <input type="number" step="0.5" min="0" id="paye_used" name="paye_used"
                                    value="<?= $balances['payé']['used'] ?>"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            </div>
                            <div>
                                <label for="accrual_rate" class="block text-sm font-medium text-gray-700 mb-1">
                                    Taux d'acquisition mensuel
                                </label>
                                <input type="number" step="0.01" min="0" id="accrual_rate" name="accrual_rate"
                                    value="<?= $balances['payé']['accrual_rate'] ?>"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            </div>
                        </div>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">
                                Solde restant: <span class="font-medium"><?= $balances['payé']['remaining'] ?> jours</span>
                                <?php if ($balances['payé']['last_accrual_date']): ?>
                                    | Dernière attribution: <span class="font-medium"><?= date('d/m/Y', strtotime($balances['payé']['last_accrual_date'])) ?></span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>

                    <!-- Congés exceptionnels -->
                    <div class="border-b border-gray-200 pb-6">
                        <h3 class="text-md font-medium mb-3">Congés exceptionnels (limité à 3 jours)</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="exceptionnel_total" class="block text-sm font-medium text-gray-700 mb-1">
                                    Total annuel
                                </label>
                                <input type="number" step="0.5" min="0" max="3" id="exceptionnel_total" name="exceptionnel_total"
                                    value="<?= $balances['exceptionnel']['total'] ?>"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <p class="text-xs text-gray-500 mt-1">Maximum: 3 jours</p>
                            </div>
                            <div>
                                <label for="exceptionnel_used" class="block text-sm font-medium text-gray-700 mb-1">
                                    Jours utilisés
                                </label>
                                <input type="number" step="0.5" min="0" id="exceptionnel_used" name="exceptionnel_used"
                                    value="<?= $balances['exceptionnel']['used'] ?>"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            </div>
                        </div>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">
                                Solde restant: <span class="font-medium"><?= $balances['exceptionnel']['remaining'] ?> jours</span>
                            </p>
                        </div>
                    </div>

                    <!-- Congés sans solde -->
                    <div>
                        <h3 class="text-md font-medium mb-3">Congés sans solde (illimité)</h3>
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <div>
                                <label for="sans_solde_used" class="block text-sm font-medium text-gray-700 mb-1">
                                    Jours utilisés
                                </label>
                                <input type="number" step="0.5" min="0" id="sans_solde_used" name="sans_solde_used"
                                    value="<?= $balances['sans solde']['used'] ?>"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            </div>
                            <input type="hidden" id="sans_solde_total" name="sans_solde_total" value="999">
                        </div>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">
                                Solde restant: <span class="font-medium">Illimité</span>
                            </p>
                            <p class="text-xs text-gray-500 mt-1">Les congés sans solde sont illimités, mais leur utilisation est soumise à approbation.</p>
                        </div>
                    </div>

                    <div class="flex justify-end pt-4">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
                            Enregistrer les modifications
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

<?php require_once APPROOT . '/views/includes/footer.php'; ?>
