<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON> - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once __DIR__ . '/../shared/sidebar_admin.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <!-- Breadcrumb Navigation -->
        <nav class="mb-4" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-2 text-sm">
                <li>
                    <a href="/dashboard-admin" class="text-purple-600 hover:text-purple-800">
                        <i class="fas fa-home mr-1"></i>Accueil
                    </a>
                </li>
                <li>
                    <span class="text-gray-500">/</span>
                </li>
                <li>
                    <span class="text-purple-600">Paramètres du système</span>
                </li>
                <li>
                    <span class="text-gray-500">/</span>
                </li>
                <li class="text-gray-500">
                    Jours Fériés
                </li>
            </ol>
        </nav>

        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Jours Fériés</h1>
                    <p class="text-gray-600">Gestion des jours non travaillés</p>
                </div>
                <button id="addHolidayBtn" class="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg flex items-center">
                    <i class="fas fa-plus mr-2"></i> Ajouter un jour férié
                </button>
            </div>
        </header>

        <?php if (isset($success) && $success): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded">
                <p>Le jour férié a été ajouté avec succès.</p>
            </div>
        <?php endif; ?>

        <?php if (isset($deleted) && $deleted): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded">
                <p>Le jour férié a été supprimé avec succès.</p>
            </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded">
                <p><?= $error ?></p>
            </div>
        <?php endif; ?>

        <div class="bg-white rounded-lg shadow-sm mb-6 p-4 flex flex-wrap items-center justify-between">
            <div class="w-full sm:w-auto mb-2 sm:mb-0">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Rechercher</label>
                <input type="text" id="search" placeholder="Nom du jour férié..." class="w-full sm:w-64 text-sm border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
            </div>
            <div>
                <label for="year" class="block text-sm font-medium text-gray-700 mb-1">Année</label>
                <select id="year" name="year" class="text-sm border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                    <?php
                    $currentYear = date('Y');
                    for ($y = $currentYear - 1; $y <= $currentYear + 5; $y++) {
                        $selected = ($y == ($year ?? $currentYear)) ? 'selected' : '';
                        echo "<option value=\"$y\" $selected>$y</option>";
                    }
                    ?>
                </select>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($holidays)): ?>
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-gray-500">Aucun jour férié trouvé</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($holidays as $holiday): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($holiday['nom']) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= date('d/m/Y', strtotime($holiday['date'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($holiday['est_recurrent']): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Récurrent
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                Unique
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Add/Edit Holiday Modal -->
    <div id="holidayModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden flex items-center justify-center p-4 z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden">
            <div class="bg-purple-600 px-4 py-3 flex items-center justify-between">
                <h3 class="text-lg font-medium text-white" id="modalTitle">Ajouter un jour férié</h3>
                <button type="button" class="text-white hover:text-gray-200" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form action="/jours-feries-admin" method="POST">
                <input type="hidden" id="holiday_id" name="id" value="">
                <div class="p-6">
                    <div class="mb-4">
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Nom <span class="text-red-500">*</span></label>
                        <input type="text" id="name" name="name" class="shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="Nom du jour férié" required>
                    </div>
                    <div class="mb-4">
                        <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Date <span class="text-red-500">*</span></label>
                        <input type="date" id="date" name="date" class="shadow-sm focus:ring-purple-500 focus:border-purple-500 block w-full sm:text-sm border-gray-300 rounded-md" required>
                    </div>
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="is_recurring" name="is_recurring" class="focus:ring-purple-500 h-4 w-4 text-purple-600 border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">Jour férié récurrent (chaque année)</span>
                        </label>
                    </div>
                    <div class="border-t pt-5">
                        <div class="flex justify-end">
                            <button type="button" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 mr-2" onclick="closeModal()">
                                Annuler
                            </button>
                            <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                                Enregistrer
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mainContent = document.getElementById('main-content');

            // Search functionality
            document.getElementById('search').addEventListener('input', function(e) {
                const searchValue = e.target.value.toLowerCase();
                const rows = document.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const name = row.querySelector('td:first-child').textContent.toLowerCase();
                    if (name.includes(searchValue)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });

            // Year filter - use History API instead of page reload
            document.getElementById('year').addEventListener('change', function(e) {
                try {
                    const year = e.target.value;
                    const url = '/jours-feries-admin?year=' + encodeURIComponent(year);

                    // Update URL without page reload using History API
                    history.pushState({}, '', url);

                    // Fetch new data via AJAX
                    fetchFilteredHolidays(year);
                } catch (error) {
                    console.error('Error in year filter handler:', error);
                    // Fallback to page reload
                    window.location.href = '/jours-feries-admin?year=' + encodeURIComponent(e.target.value);
                }
            });

            // Function to fetch filtered holidays via AJAX
            function fetchFilteredHolidays(year) {
                try {
                    const formData = new FormData();
                    formData.append('year', year);
                    formData.append('ajax', '1');

                    fetch('/jours-feries-admin', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.text();
                    })
                    .then(html => {
                        // Update table content
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(html, 'text/html');
                        const newTableBody = doc.querySelector('tbody');
                        const currentTableBody = document.querySelector('tbody');

                        if (newTableBody && currentTableBody) {
                            currentTableBody.innerHTML = newTableBody.innerHTML;
                            // Re-apply current search filter
                            const searchInput = document.getElementById('search');
                            if (searchInput && searchInput.value) {
                                // Trigger search to filter the new results
                                searchInput.dispatchEvent(new Event('input'));
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching filtered holidays:', error);
                        // Fallback to page reload
                        window.location.href = '/jours-feries-admin?year=' + encodeURIComponent(year);
                    });
                } catch (error) {
                    console.error('Error in fetchFilteredHolidays:', error);
                    // Fallback to page reload
                    window.location.href = '/jours-feries-admin?year=' + encodeURIComponent(year);
                }
            }

            // Add holiday button
            document.getElementById('addHolidayBtn').addEventListener('click', function() {
                document.getElementById('modalTitle').textContent = 'Ajouter un jour férié';
                document.getElementById('holiday_id').value = '';
                document.getElementById('name').value = '';
                document.getElementById('date').value = '';
                document.getElementById('is_recurring').checked = false;
                document.getElementById('holidayModal').classList.remove('hidden');
            });
        });

        // Enhanced Modal functionality with click-outside support
        function closeModal() {
            document.getElementById('holidayModal').classList.add('hidden');
        }

        function editHoliday(id, name, date, isRecurring) {
            document.getElementById('modalTitle').textContent = 'Modifier un jour férié';
            document.getElementById('holiday_id').value = id;
            document.getElementById('name').value = name;
            document.getElementById('date').value = date;
            document.getElementById('is_recurring').checked = isRecurring;
            document.getElementById('holidayModal').classList.remove('hidden');
        }

        // Set up click-outside functionality for holiday modal
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('holidayModal');

            // Click-outside functionality is now handled by the global modal manager
            // The modal manager will automatically detect this modal and add the functionality

            // Escape key functionality (also handled by modal manager, but keeping for compatibility)
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                    closeModal();
                }
            });
        });
    </script>
</body>
</html>
