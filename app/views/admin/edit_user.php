<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Modifier utilisateur - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once __DIR__ . '/../shared/sidebar_admin.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Modifier un utilisateur</h1>
                    <p class="text-gray-600">Modification des informations de l'utilisateur</p>
                </div>
                <a href="/list-users" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i> Retour à la liste
                </a>
            </div>
        </header>

        <?php if (isset($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded">
                <p><?= $error ?></p>
            </div>
        <?php endif; ?>

        <div class="bg-white rounded-lg shadow-sm p-6">
            <form action="/edit-user?id=<?= $user['id'] ?>" method="POST" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="nom" class="block text-sm font-medium text-gray-700">Nom</label>
                        <input type="text" name="nom" id="nom" value="<?= htmlspecialchars($user['nom']) ?>" required class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="prenom" class="block text-sm font-medium text-gray-700">Prénom</label>
                        <input type="text" name="prenom" id="prenom" value="<?= htmlspecialchars($user['prenom']) ?>" required class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" name="email" id="email" value="<?= htmlspecialchars($user['email']) ?>" required class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700">Rôle</label>
                        <select name="role" id="role" required class="mt-1 focus:ring-purple-500 focus:border-purple-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            <option value="admin" <?= $user['role'] === 'admin' ? 'selected' : '' ?>>Admin</option>
                            <option value="responsable" <?= $user['role'] === 'responsable' ? 'selected' : '' ?>>Responsable</option>
                            <option value="planificateur" <?= $user['role'] === 'planificateur' ? 'selected' : '' ?>>Planificateur</option>
                            <option value="employe" <?= $user['role'] === 'employe' ? 'selected' : '' ?>>Employé</option>
                        </select>
                    </div>
                </div>

                <div class="border-t pt-5">
                    <div class="flex justify-end">
                        <button type="button" onclick="window.location='/list-users'" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-2">
                            Annuler
                        </button>
                        <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                            Enregistrer les modifications
                        </button>
                    </div>
                </div>
            </form>

            <div class="mt-10 border-t pt-6">
                <h3 class="text-lg font-medium text-gray-900">Options de sécurité</h3>
                <div class="mt-4">
                    <a href="/reset-password?id=<?= $user['id'] ?>" onclick="return confirm('Êtes-vous sûr de vouloir réinitialiser le mot de passe de cet utilisateur ?')" class="text-indigo-600 hover:text-indigo-900">
                        <i class="fas fa-key mr-1"></i> Réinitialiser le mot de passe
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
    </script>
</body>
</html>
