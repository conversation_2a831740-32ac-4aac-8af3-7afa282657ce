<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Liste des utilisateurs - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once __DIR__ . '/../shared/sidebar_admin.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Liste des utilisateurs</h1>
                    <p class="text-gray-600">Gestion des comptes utilisateur</p>
                </div>
                <a href="/create-user" class="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg flex items-center">
                    <i class="fas fa-user-plus mr-2"></i> Nouvel utilisateur
                </a>
            </div>
        </header>

        <?php if (isset($_GET['success']) && $_GET['success'] == 1): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded">
                <p>L'utilisateur a été modifié avec succès</p>
            </div>
        <?php elseif (isset($_GET['success']) && $_GET['success'] == 2): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded">
                <p>L'utilisateur a été supprimé avec succès</p>
            </div>
        <?php elseif (isset($_GET['error']) && $_GET['error'] == 1): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded">
                <p>Une erreur s'est produite</p>
            </div>
        <?php endif; ?>

        <div class="bg-white rounded-lg shadow-sm mb-6 p-4">
            <form action="/list-users" method="GET" class="flex flex-wrap items-center">
                <div class="w-full sm:w-auto mb-2 sm:mb-0 sm:mr-4">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Rechercher</label>
                    <input type="text" id="search" name="search" value="<?= htmlspecialchars($_GET['search'] ?? '') ?>" placeholder="Email, nom, prénom..." class="w-full sm:w-64 text-sm border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                </div>
                <div class="w-full sm:w-auto mb-2 sm:mb-0 sm:mr-4">
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Rôle</label>
                    <select id="role" name="role" class="text-sm border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500">
                        <option value="">Tous</option>
                        <option value="admin" <?= isset($_GET['role']) && $_GET['role'] === 'admin' ? 'selected' : '' ?>>Admin</option>
                        <option value="responsable" <?= isset($_GET['role']) && $_GET['role'] === 'responsable' ? 'selected' : '' ?>>Responsable</option>
                        <option value="planificateur" <?= isset($_GET['role']) && $_GET['role'] === 'planificateur' ? 'selected' : '' ?>>Planificateur</option>
                        <option value="employe" <?= isset($_GET['role']) && $_GET['role'] === 'employe' ? 'selected' : '' ?>>Employé</option>
                    </select>
                </div>
                <div class="w-full sm:w-auto mt-4 sm:mt-6">
                    <button type="submit" class="bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-search mr-2"></i> Rechercher
                    </button>
                </div>
            </form>
        </div>

        <div class="table-card">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom complet</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rôle</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date de création</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($users)): ?>
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-gray-500">Aucun utilisateur trouvé</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-10 w-10">
                                                <div class="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-700">
                                                    <?= strtoupper(substr($user['prenom'], 0, 1) . substr($user['nom'], 0, 1)) ?>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($user['prenom']) ?> <?= htmlspecialchars($user['nom']) ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500"><?= htmlspecialchars($user['email']) ?></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($user['role'] === 'admin'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Admin</span>
                                        <?php elseif ($user['role'] === 'responsable'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Responsable</span>
                                        <?php elseif ($user['role'] === 'planificateur'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Planificateur</span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Employé</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= date('d/m/Y', strtotime($user['date_creation'])) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="/edit-user?id=<?= $user['id'] ?>" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                            <i class="fas fa-edit"></i> Modifier
                                        </a>
                                        <?php if ($_SESSION['user_id'] != $user['id']): ?>
                                        <a href="#" onclick="handleUserDeletion(event, '/delete-user?id=<?= $user['id'] ?>', '<?= htmlspecialchars($user['prenom'] . ' ' . $user['nom']) ?>')" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash"></i> Supprimer
                                        </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Client-side filtering for quick results
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search');
            const roleSelect = document.getElementById('role');
            const tableRows = document.querySelectorAll('tbody tr');

            // Function to filter table rows based on search input and role
            function filterTable() {
                const searchTerm = searchInput.value.toLowerCase();
                const roleFilter = roleSelect.value.toLowerCase();

                tableRows.forEach(row => {
                    const userName = row.querySelector('td:first-child')?.textContent.toLowerCase() || '';
                    const userEmail = row.querySelector('td:nth-child(2)')?.textContent.toLowerCase() || '';
                    const userRole = row.querySelector('td:nth-child(3) span')?.textContent.toLowerCase() || '';

                    const matchesSearch = userName.includes(searchTerm) || userEmail.includes(searchTerm);
                    const matchesRole = roleFilter === '' || userRole.includes(roleFilter);

                    if (matchesSearch && matchesRole) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }

            // Add event listeners for real-time filtering
            searchInput.addEventListener('input', filterTable);
            roleSelect.addEventListener('change', filterTable);
        });

        // Modern confirmation for user deletion
        async function handleUserDeletion(event, url, userName) {
            event.preventDefault();

            try {
                const confirmed = await confirmDanger(
                    `Êtes-vous sûr de vouloir supprimer l'utilisateur <strong>${userName}</strong> ?<br><br>
                    <div class="bg-red-50 p-3 rounded-lg mt-3">
                        <p class="text-sm text-red-800 font-medium">⚠️ Cette action est irréversible</p>
                        <p class="text-sm text-red-700">Toutes les données associées à cet utilisateur seront définitivement supprimées.</p>
                    </div>`,
                    'Supprimer l\'utilisateur'
                );

                if (confirmed) {
                    window.location.href = url;
                }
            } catch (error) {
                console.error('Error in user deletion confirmation:', error);
            }
        }
    </script>
</body>
</html>
