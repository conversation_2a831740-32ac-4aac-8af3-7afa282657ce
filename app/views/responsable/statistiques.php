<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Statistiques Responsable - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <!-- Chart.js for statistics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>

<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_responsable.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">

        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Statistiques de l'équipe</h1>
                    <p class="text-gray-600">Analyse des congés et disponibilités de votre équipe</p>
                </div>
                <div>
                    <select id="periodFilter"
                        class="border border-gray-300 rounded-md px-3 py-1.5 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:border-transparent">
                        <option value="last30">30 derniers jours</option>
                        <option value="last90">3 derniers mois</option>
                        <option value="year" selected>Année <?= date('Y') ?></option>
                        <option value="all">Tout l'historique</option>
                    </select>
                </div>
            </div>
        </header>

        <!-- Stats Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-sm font-semibold text-gray-800">Demandes totales</h2>
                    <span class="text-indigo-500"><i class="fas fa-file-alt"></i></span>
                </div>
                <div class="text-3xl font-bold text-gray-900"><?= $totalDemandes ?></div>
                <div class="mt-1 text-sm text-gray-500">demandes équipe</div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-sm font-semibold text-gray-800">Demandes approuvées</h2>
                    <span class="text-green-500"><i class="fas fa-check-circle"></i></span>
                </div>
                <div class="text-3xl font-bold text-gray-900"><?= $demandesApprouvees ?></div>
                <div class="mt-1 text-sm text-green-500"><?= $pourcentageApprouve ?>% d'approbation</div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-sm font-semibold text-gray-800">Demandes rejetées</h2>
                    <span class="text-red-500"><i class="fas fa-times-circle"></i></span>
                </div>
                <div class="text-3xl font-bold text-gray-900"><?= $demandesRejetees ?></div>
                <div class="mt-1 text-sm text-gray-500"><?= $totalDemandes > 0 ? round(($demandesRejetees/$totalDemandes)*100) : 0 ?>% de rejet
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-sm font-semibold text-gray-800">En attente</h2>
                    <span class="text-yellow-500"><i class="fas fa-clock"></i></span>
                </div>
                <div class="text-3xl font-bold text-gray-900"><?= $demandesEnAttente ?></div>
                <div class="mt-1 text-sm text-gray-500"><?= $totalDemandes > 0 ? round(($demandesEnAttente/$totalDemandes)*100) : 0 ?>% en
                    attente</div>
            </div>
        </div>

        <!-- Charts -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Monthly Requests -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Demandes par mois</h2>
                <div class="chart-container">
                    <canvas id="monthlyRequestsChart" ></canvas>
                </div>
            </div>

            <!-- Team Coverage -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Couverture de l'équipe</h2>
                <div class="flex flex-col items-center justify-center h-[250px]">
                    <div class="w-48 h-48 relative flex items-center justify-center">
                        <svg class="w-full h-full" viewBox="0 0 100 100">
                            <circle class="text-gray-200" stroke-width="10" stroke="currentColor" fill="transparent"
                                r="40" cx="50" cy="50" />
                            <circle class="text-indigo-600" stroke-width="10" stroke="currentColor" fill="transparent"
                                r="40" cx="50" cy="50" stroke-dasharray="251.2"
                                stroke-dashoffset="<?= 251.2 - (251.2 * (100 - round($averageTeamAvailability))) / 100 ?>"
                                transform="rotate(-90 50 50)" />
                        </svg>
                        <div class="absolute inset-0 flex flex-col items-center justify-center">
                            <span
                                class="text-3xl font-bold text-gray-900"><?= round($averageTeamAvailability) ?>%</span>
                            <span class="text-sm text-gray-500">disponibilité</span>
                        </div>
                    </div>
                    <p class="text-sm text-gray-500 mt-2">Capacité moyenne de l'équipe</p>
                </div>
            </div>
        </div>

        <!-- Team Members Analysis -->
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">Analyse par membre de l'équipe</h2>
                <button class="text-sm px-3 py-1 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200">
                    <i class="fas fa-download mr-1"></i> Exporter
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Membre</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Jours pris</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Jours restants</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Taux d'utilisation</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Prochaine absence</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($membresEquipe as $membre): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div
                                        class="h-8 w-8 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-800 font-bold mr-3">
                                        <?= strtoupper(substr($membre['prenom'], 0, 1)) ?>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            <?= htmlspecialchars($membre['prenom'] . ' ' . $membre['nom']) ?>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?= isset($membre['joursPris']) ? $membre['joursPris'] : 18 ?> jours
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                    $joursRestants = isset($membre['joursRestants']) ? $membre['joursRestants'] : 7;
                                    $colorClass = $joursRestants > 10 ? 'bg-green-100 text-green-800' :
                                                ($joursRestants > 5 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800');
                                ?>
                                <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full <?= $colorClass ?>">
                                    <?= $joursRestants ?> jours
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                    $joursPris = isset($membre['joursPris']) ? $membre['joursPris'] : 18;
                                    $joursRestants = isset($membre['joursRestants']) ? $membre['joursRestants'] : 7;
                                    $total = $joursPris + $joursRestants;
                                    $tauxUtilisation = $total > 0 ? round(($joursPris / $total) * 100) : 0;
                                ?>
                                <div class="text-sm text-gray-900"><?= $tauxUtilisation ?>%</div>
                                <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div class="bg-indigo-600 h-2 rounded-full" style="width: <?= $tauxUtilisation ?>%">
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?= isset($membre['prochaineAbsence']) ? date('d/m/Y', strtotime($membre['prochaineAbsence'])) : 'Aucune planifiée' ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Critical Periods -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Team Availability -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Périodes critiques</h2>
                <div class="space-y-3">
                    <div class="p-3 bg-red-50 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <span class="text-sm font-medium text-gray-600">1 - 15 août 2024</span>
                                <div class="font-medium text-red-700 mt-1">Capacité réduite à 60%</div>
                            </div>
                            <div>
                                <span
                                    class="px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded">Critique</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-3 bg-yellow-50 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <span class="text-sm font-medium text-gray-600">24 - 31 décembre 2024</span>
                                <div class="font-medium text-yellow-700 mt-1">Capacité réduite à 75%</div>
                            </div>
                            <div>
                                <span
                                    class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded">Attention</span>
                            </div>
                        </div>
                    </div>
                    <div class="p-3 bg-yellow-50 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <span class="text-sm font-medium text-gray-600">12 - 16 mai 2024 (pont Ascension)</span>
                                <div class="font-medium text-yellow-700 mt-1">Capacité réduite à 70%</div>
                            </div>
                            <div>
                                <span
                                    class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded">Attention</span>
                            </div>
                        </div>
                    </div>
                </div>
                <a href="/planning-equipe" class="mt-4 block text-indigo-600 text-sm font-medium hover:text-indigo-800">
                    Voir le planning complet <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>

            <!-- Request Processing Time -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Temps de traitement des demandes</h2>
                <div class="chart-container">
                <canvas id="processingTimeChart" ></canvas>
                </div>
                <div class="mt-2 text-sm text-gray-500 text-center">
                    Temps moyen: <span class="font-medium text-indigo-600">1.2 jours</span>
                </div>
            </div>
        </div>

        <!-- Recommendation -->
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <div class="flex items-start space-x-4">
                <span class="bg-blue-100 text-blue-600 p-2 rounded-lg">
                    <i class="fas fa-lightbulb text-xl"></i>
                </span>
                <div>
                    <h2 class="text-lg font-semibold text-gray-800 mb-1">Recommandations</h2>
                    <p class="text-gray-600 mb-4">
                        En fonction de votre analyse, nous vous suggérons les actions suivantes pour optimiser la
                        gestion des congés :
                    </p>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>Encouragez Jean Dupont et Pierre Durand à prendre leurs congés restants avant la fin
                                de l'année pour éviter un pic en décembre.</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>Limitez les départs pendant la période du 1er au 15 août pour maintenir une capacité
                                d'équipe suffisante.</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                            <span>Accélérez le traitement des demandes en attente pour revenir au temps moyen habituel
                                de 1 jour.</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
    // Sidebar toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Chart: Monthly Requests
        const monthlyRequestsCtx = document.getElementById('monthlyRequestsChart').getContext('2d');
        new Chart(monthlyRequestsCtx, {
            type: 'bar',
            data: {
                labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov',
                    'Déc'
                ],
                datasets: [{
                    label: 'Demandes par mois',
                    data: <?= json_encode($demandesByMonth) ?>,
                    backgroundColor: 'rgba(79, 70, 229, 0.8)',
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        stacked: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        stacked: true,
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // Chart: Processing Time
        const processingTimeCtx = document.getElementById('processingTimeChart').getContext('2d');
        new Chart(processingTimeCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep'],
                datasets: [{
                    label: 'Temps moyen (jours)',
                    data: [0.8, 0.9, 1.1, 1.0, 1.2, 1.3, 1.5, 1.4, 1.2],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value + ' j';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    });
    </script>
</body>

</html>