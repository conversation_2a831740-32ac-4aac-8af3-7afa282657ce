<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Disponibilité de l'équipe - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_responsable.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Disponibilité de l'équipe</h1>
                    <p class="text-gray-600">Calendrier de présence de l'équipe</p>
                </div>
                <div class="flex space-x-2">
                    <a href="/responsable/team_members" class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg flex items-center">
                        <i class="fas fa-users mr-2"></i> Voir les membres
                    </a>
                </div>
            </div>
        </header>

        <div class="card mb-6">
            <div class="flex flex-wrap items-center justify-between mb-4">
                <?php
                $currentMonthDate = new DateTime($currentMonth);
                $prevMonth = clone $currentMonthDate;
                $prevMonth->modify('-1 month');
                $nextMonth = clone $currentMonthDate;
                $nextMonth->modify('+1 month');
                ?>
                <h2 class="text-xl font-semibold text-gray-800"><?= $currentMonthDate->format('F Y') ?></h2>
                <div class="flex space-x-2 mt-2 sm:mt-0">
                    <a href="/responsable/team_availability?month=<?= $prevMonth->format('Y-m') ?>" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-1 px-3 rounded-md">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <a href="/responsable/team_availability?month=<?= date('Y-m') ?>" class="bg-indigo-600 hover:bg-indigo-700 text-white py-1 px-3 rounded-md">
                        Aujourd'hui
                    </a>
                    <a href="/responsable/team_availability?month=<?= $nextMonth->format('Y-m') ?>" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-1 px-3 rounded-md">
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>

            <div class="overflow-x-auto">
                <div class="inline-block min-w-full">
                    <div class="grid grid-cols-7 gap-1">
                        <!-- Calendar headers -->
                        <div class="text-center py-2 font-medium text-gray-500 text-sm">Lun</div>
                        <div class="text-center py-2 font-medium text-gray-500 text-sm">Mar</div>
                        <div class="text-center py-2 font-medium text-gray-500 text-sm">Mer</div>
                        <div class="text-center py-2 font-medium text-gray-500 text-sm">Jeu</div>
                        <div class="text-center py-2 font-medium text-gray-500 text-sm">Ven</div>
                        <div class="text-center py-2 font-medium text-gray-500 text-sm">Sam</div>
                        <div class="text-center py-2 font-medium text-gray-500 text-sm">Dim</div>

                        <?php
                        // Determine the starting offset for the first day
                        $firstDayOfMonth = new DateTime($availabilityData['days'][0]['date']);
                        $firstDayOffset = (int)$firstDayOfMonth->format('N') - 1; // 1 (Monday) to 7 (Sunday), adjust to 0-6

                        // Add empty cells for offset
                        for ($i = 0; $i < $firstDayOffset; $i++): ?>
                            <div class="h-24 border border-gray-100 p-1 bg-gray-50"></div>
                        <?php endfor; ?>

                        <!-- Calendar days -->
                        <?php foreach ($availabilityData['days'] as $day):
                            $dayClass = "";
                            $availability = "";
                            $availabilityClass = "";

                            if ($day['isWeekend']) {
                                $dayClass = "bg-gray-50";
                            }

                            if ($day['isHoliday']) {
                                $dayClass = "bg-blue-50";
                                $availability = "Jour férié";
                                $availabilityClass = "text-blue-600";
                            } else {
                                $percentage = round(($day['totalMembers'] - $day['absentMembers']) / $day['totalMembers'] * 100);
                                if ($percentage > 75) {
                                    $availabilityClass = "text-green-600";
                                } elseif ($percentage > 50) {
                                    $availabilityClass = "text-yellow-600";
                                } else {
                                    $availabilityClass = "text-red-600";
                                }
                                $availability = ($day['totalMembers'] - $day['absentMembers']) . "/" . $day['totalMembers'] . " (" . $percentage . "%)";
                            }

                            $dateObj = new DateTime($day['date']);
                            $isToday = $dateObj->format('Y-m-d') === (new DateTime())->format('Y-m-d');
                            $dayOfMonthClass = $isToday ? "bg-indigo-600 text-white" : "bg-white text-gray-800";
                        ?>
                            <div class="h-24 border border-gray-200 p-1 <?= $dayClass ?>">
                                <div class="flex justify-between mb-1">
                                    <span class="rounded-full h-6 w-6 flex items-center justify-center <?= $dayOfMonthClass ?>">
                                        <?= $day['dayOfMonth'] ?>
                                    </span>
                                    <?php if (!$day['isWeekend'] && !$day['isHoliday']): ?>
                                        <button class="text-xs text-gray-500 hover:text-indigo-600"
                                                title="Voir les détails"
                                                onclick="showDayDetails('<?= $day['date'] ?>', '<?= $dateObj->format('d/m/Y') ?>', <?= $day['absentMembers'] ?>, <?= json_encode($day['absentMembersList'] ?? []) ?>)">
                                            <i class="fas fa-info-circle"></i>
                                        </button>
                                    <?php endif; ?>
                                </div>
                                <div class="text-center mt-2">
                                    <p class="text-xs <?= $availabilityClass ?> font-medium">
                                        <?= $availability ?>
                                    </p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Légende</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-green-100 border border-green-200 rounded mr-2"></div>
                    <span class="text-sm text-gray-600">Disponibilité > 75%</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-yellow-100 border border-yellow-200 rounded mr-2"></div>
                    <span class="text-sm text-gray-600">Disponibilité 50% - 75%</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-red-100 border border-red-200 rounded mr-2"></div>
                    <span class="text-sm text-gray-600">Disponibilité < 50% (Critique)</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-blue-50 border border-blue-200 rounded mr-2"></div>
                    <span class="text-sm text-gray-600">Jour férié</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-gray-50 border border-gray-200 rounded mr-2"></div>
                    <span class="text-sm text-gray-600">Weekend</span>
                </div>
                <div class="flex items-center">
                    <div class="w-5 h-5 rounded-full bg-indigo-600 text-white text-xs flex items-center justify-center mr-2">1</div>
                    <span class="text-sm text-gray-600">Aujourd'hui</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Day Details Modal -->
    <div id="dayDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900" id="dayDetailsTitle">Détails du jour</h3>
                <button onclick="closeDayModal()" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="dayDetailsContent" class="space-y-4">
                <!-- Content will be loaded dynamically -->
            </div>
            <div class="mt-6 border-t pt-4 flex justify-end">
                <button onclick="closeDayModal()" class="bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-md">
                    Fermer
                </button>
            </div>
        </div>
    </div>

    <script>
        function showDayDetails(date, formattedDate, absentCount, absentMembers) {
            const modal = document.getElementById('dayDetailsModal');
            const titleEl = document.getElementById('dayDetailsTitle');
            const contentDiv = document.getElementById('dayDetailsContent');

            titleEl.textContent = `Absences du ${formattedDate}`;

            let content = '';

            if (absentCount === 0) {
                content = `
                    <div class="text-center py-4">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                            <i class="fas fa-check text-green-600"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Équipe complète</h3>
                        <p class="text-gray-500">Tous les membres de l'équipe sont présents ce jour.</p>
                    </div>
                `;
            } else {
                content = `
                    <div class="mb-4">
                        <div class="flex items-center mb-2">
                            <div class="rounded-full bg-yellow-100 p-2 mr-3">
                                <i class="fas fa-user-clock text-yellow-600"></i>
                            </div>
                            <p class="font-medium">${absentCount} membre(s) absent(s)</p>
                        </div>
                    </div>
                    <div class="space-y-3">
                `;

                if (Array.isArray(absentMembers) && absentMembers.length > 0) {
                    absentMembers.forEach(member => {
                        content += `
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                                    <span class="text-indigo-800 font-medium">${member.prenom.charAt(0)}${member.nom.charAt(0)}</span>
                                </div>
                                <div>
                                    <p class="font-medium">${member.prenom} ${member.nom}</p>
                                    <p class="text-sm text-gray-500">${member.type_conge || 'Congé'}</p>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    content += `
                        <p class="text-gray-500 italic">Détails des absences non disponibles.</p>
                    `;
                }

                content += `</div>`;
            }

            contentDiv.innerHTML = content;
            modal.classList.remove('hidden');
        }

        function closeDayModal() {
            document.getElementById('dayDetailsModal').classList.add('hidden');
        }
    </script>
</body>
</html>
