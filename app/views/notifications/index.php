<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Notifications - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>

<body class="bg-gray-50">
    <?php
if (isset($_SESSION['role'])) {
    switch ($_SESSION['role']) {
        case 'admin':
            include_once APP_PATH . '/views/shared/sidebar_admin.php';
            break;
        case 'responsable':
            include_once APP_PATH . '/views/shared/sidebar_responsable.php';
            break;
        case 'planificateur':
            include_once APP_PATH . '/views/shared/sidebar_planificateur.php';
            break;
        case 'employe':
        default:
            include_once APP_PATH . '/views/shared/sidebar_employe.php';
            break;
    }
} else {
    // Default to employee sidebar if no role is set
    include_once APP_PATH . '/views/shared/sidebar_employe.php';
}
?>
    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Notifications</h1>
                    <p class="text-gray-600">Vos alertes et messages importants</p>
                </div>
                <?php if (!empty($notifications)): ?>
                <a href="/notifications/mark-all-as-read" class="text-purple-600 hover:text-purple-800">
                    <i class="fas fa-check-double"></i> Marquer tout comme lu
                </a>
                <?php endif; ?>
            </div>
        </header>

        <?php if (empty($notifications)): ?>
        <div class="card text-center">
            <div class="text-gray-400 mb-3">
                <i class="fas fa-bell-slash text-6xl"></i>
            </div>
            <h2 class="text-xl font-semibold text-gray-700 mb-2">Aucune notification</h2>
            <p class="text-gray-500">Vous n'avez pas de notifications ou d'alertes pour le moment.</p>
        </div>
        <?php else: ?>
        <div class="space-y-4">
            <?php foreach ($notifications as $notification): ?>
            <?php
                $isClickable = !empty($notification['demande_id']);
                $cardClass = $notification['is_read'] ? '' : 'border-l-4 border-purple-500';
                if ($isClickable) {
                    $cardClass .= ' cursor-pointer hover:bg-gray-50 transition-colors duration-200';
                }
            ?>
            <div class="card <?= $cardClass ?>"
                 <?= $isClickable ? 'onclick="handleNotificationClick(' . $notification['id'] . ', event)"' : '' ?>>
                <div class="flex items-start justify-between">
                    <div class="flex items-start space-x-3">
                        <?php if ($notification['type'] === 'success'): ?>
                        <div class="mt-1 bg-green-100 text-green-700 p-2 rounded-full">
                            <i class="fas fa-check"></i>
                        </div>
                        <?php elseif ($notification['type'] === 'danger' || $notification['type'] === 'error'): ?>
                        <div class="mt-1 bg-red-100 text-red-700 p-2 rounded-full">
                            <i class="fas fa-times"></i>
                        </div>
                        <?php elseif ($notification['type'] === 'warning'): ?>
                        <div class="mt-1 bg-yellow-100 text-yellow-700 p-2 rounded-full">
                            <i class="fas fa-exclamation"></i>
                        </div>
                        <?php else: ?>
                        <div class="mt-1 bg-blue-100 text-blue-700 p-2 rounded-full">
                            <i class="fas fa-info"></i>
                        </div>
                        <?php endif; ?>

                        <div class="flex-1">
                            <div class="flex items-center">
                                <?php if (!empty($notification['title'])): ?>
                                <p class="text-gray-800 font-bold <?= $notification['is_read'] ? '' : 'font-semibold' ?>">
                                    <?= htmlspecialchars($notification['title']) ?>
                                </p>
                                <?php endif; ?>
                                <?php if ($isClickable): ?>
                                <i class="fas fa-external-link-alt text-purple-500 ml-2 text-xs" title="Cliquer pour voir les détails"></i>
                                <?php endif; ?>
                            </div>
                            <p class="text-gray-800 <?= $notification['is_read'] ? '' : 'font-semibold' ?>">
                                <?= htmlspecialchars($notification['message']) ?>
                            </p>
                            <p class="text-sm text-gray-500 mt-1">
                                <?= isset($notification['created_at']) ? date('d/m/Y H:i', strtotime($notification['created_at'])) : '' ?>
                                <?php if ($isClickable): ?>
                                <span class="text-purple-500 ml-2">• Cliquer pour voir les détails</span>
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <?php if (!$notification['is_read']): ?>
                        <a href="/notifications/mark-as-read?id=<?= $notification['id'] ?>"
                            class="text-blue-600 hover:text-blue-800 text-sm" title="Marquer comme lu"
                            onclick="event.stopPropagation();">
                            <i class="fas fa-check"></i>
                        </a>
                        <?php endif; ?>
                        <a href="/notifications/delete?id=<?= $notification['id'] ?>"
                            class="text-red-600 hover:text-red-800 text-sm" title="Supprimer"
                            onclick="event.stopPropagation(); return confirm('Êtes-vous sûr de vouloir supprimer cette notification ?')">
                            <i class="fas fa-trash"></i>
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>

    <script>
        function handleNotificationClick(notificationId, event) {
            console.log('Notification click handler called with ID:', notificationId);

            // Prevent event bubbling if event is provided
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            // Redirect to notification click handler
            const url = '/notifications/click?id=' + notificationId;
            console.log('Redirecting to:', url);

            window.location.href = url;
        }

        // Add hover effect for clickable notifications
        document.addEventListener('DOMContentLoaded', function() {
            const clickableNotifications = document.querySelectorAll('.card.cursor-pointer');

            clickableNotifications.forEach(function(notification) {
                notification.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-1px)';
                    this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                });

                notification.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '';
                });
            });
        });
    </script>
</body>

</html>