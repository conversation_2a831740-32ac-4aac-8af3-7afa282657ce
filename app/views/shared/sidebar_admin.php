<?php
// This is a partial for the admin sidebar
?>

<!-- Mobile Header - Only visible on small screens -->
<header class="mobile-header">
    <div class="flex items-center">
        <button id="mobileMenuButton" class="mr-2 text-white focus:outline-none">
            <i class="fas fa-bars text-xl"></i>
        </button>
        <span class="text-lg font-bold">GestionConge</span>
    </div>
    <div class="flex items-center">
        <a href="/notifications" class="relative mr-4">
            <i class="fas fa-bell text-white text-lg"></i>
            <?php if (isset($notificationCount) && $notificationCount > 0): ?>
            <span
                class="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full"><?= $notificationCount ?></span>
            <?php endif; ?>
        </a>
        <a href="/profil" class="text-white">
            <i class="fas fa-user text-lg"></i>
        </a>
    </div>
</header>

<aside id="sidebar" class="sidebar">
    <div class="sidebar-header">
        <a href="/dashboard-admin" class="sidebar-logo">
            <i class="fas fa-calendar-check text-white text-xl mr-2"></i>
            <span class="logo-text">GestionConge</span>
        </a>
        <button id="toggleSidebar" class="hidden md:block">
            <i class="fas fa-bars"></i>
        </button>
        <button id="closeSidebar" class="md:hidden">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <div class="sidebar-nav">
        <nav>
            <a href="/dashboard-admin" class="menu-item">
                <i class="fas fa-home"></i>
                <span class="menu-text">Home page</span>
            </a>

            <div class="sidebar-category">Gestion Utilisateurs</div>
            <a href="/list-users" class="menu-item">
                <i class="fas fa-users"></i>
                <span class="menu-text">Gérer les utilisateurs</span>
            </a>

            <div class="sidebar-category">Historique</div>
            <a href="/all-demandes" class="menu-item">
                <i class="fas fa-list-alt"></i>
                <span class="menu-text">Historique global</span>
            </a>

            <div class="sidebar-category">Système</div>

            <!-- Paramètres du système - Collapsible Section -->
            <div class="menu-item-group">
                <a href="#" class="menu-item menu-item-toggle" id="systemSettingsToggle">
                    <i class="fas fa-cogs"></i>
                    <span class="menu-text">Paramètres du système</span>
                    <i class="fas fa-chevron-down toggle-icon"></i>
                </a>
                <div class="submenu" id="systemSettingsSubmenu">
                    <a href="/settings" class="menu-item">
                        <i class="fas fa-cog"></i>
                        <span class="menu-text">général</span>
                    </a>
                    <a href="/jours-feries-admin" class="submenu-item">
                        <i class="fas fa-calendar-day"></i>
                        <span class="menu-text">Jours Fériés</span>
                    </a>
                    <a href="/admin/leave_balances" class="submenu-item">
                        <i class="fas fa-balance-scale"></i>
                        <span class="menu-text">Soldes Congés</span>
                    </a>
                    <a href="/admin/department_leave_balances" class="submenu-item">
                        <i class="fas fa-building"></i>
                        <span class="menu-text">Soldes Département</span>
                    </a>
                </div>
            </div>
            <a href="/notifications" class="menu-item">
                <i class="fas fa-bell"></i>
                <span class="menu-text">Notifications</span>
                <?php if (isset($notificationCount) && $notificationCount > 0): ?>
                <span class="notification-badge"><?= $notificationCount ?></span>
                <?php endif; ?>
            </a>
            <a href="/profil" class="menu-item">
                <i class="fas fa-user"></i>
                <span class="menu-text">Mon profil</span>
            </a>
            <a href="/logout" class="menu-item text-red-300">
                <i class="fas fa-sign-out-alt"></i>
                <span class="menu-text">Déconnexion</span>
            </a>
        </nav>
    </div>
</aside>

<script>
// For desktop - toggle sidebar collapse
const toggleSidebarBtn = document.getElementById('toggleSidebar');
if (toggleSidebarBtn) {
    toggleSidebarBtn.addEventListener('click', function() {
        try {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.toggle('sidebar-collapsed');

                // Adjust main content margin
                const mainContent = document.getElementById('main-content');
                if (mainContent) {
                    if (sidebar.classList.contains('sidebar-collapsed')) {
                        mainContent.style.marginLeft = '60px';
                    } else {
                        mainContent.style.marginLeft = '240px';
                    }
                }
            }
        } catch (error) {
            console.error('Error toggling sidebar:', error);
        }
    });
}

// For mobile - open sidebar
const mobileMenuBtn = document.getElementById('mobileMenuButton');
if (mobileMenuBtn) {
    mobileMenuBtn.addEventListener('click', function() {
        try {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.add('sidebar-open');

                // Add overlay
                const overlay = document.createElement('div');
                overlay.id = 'sidebar-overlay';
                overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-40';
                document.body.appendChild(overlay);

                // Close sidebar when clicking overlay
                overlay.addEventListener('click', closeMobileSidebar);
            }
        } catch (error) {
            console.error('Error opening mobile sidebar:', error);
        }
    });
}

// For mobile - close sidebar
const closeSidebarBtn = document.getElementById('closeSidebar');
if (closeSidebarBtn) {
    closeSidebarBtn.addEventListener('click', closeMobileSidebar);
}

function closeMobileSidebar() {
    try {
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.classList.remove('sidebar-open');
        }

        // Remove overlay
        const overlay = document.getElementById('sidebar-overlay');
        if (overlay) {
            overlay.remove();
        }
    } catch (error) {
        console.error('Error closing mobile sidebar:', error);
    }
}

// Handle resize events to ensure proper layout
window.addEventListener('resize', function() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');

    if (window.innerWidth >= 768) {
        // Remove mobile-specific classes
        sidebar.classList.remove('sidebar-open');

        // Remove overlay
        const overlay = document.getElementById('sidebar-overlay');
        if (overlay) {
            overlay.remove();
        }

        // Reset desktop layout
        if (sidebar.classList.contains('sidebar-collapsed')) {
            if (mainContent) mainContent.style.marginLeft = '60px';
        } else {
            if (mainContent) mainContent.style.marginLeft = '240px';
        }
    } else {
        // Set mobile layout
        if (mainContent) mainContent.style.marginLeft = '0';
    }
});

// System Settings Submenu Toggle
const systemSettingsToggle = document.getElementById('systemSettingsToggle');
if (systemSettingsToggle) {
    systemSettingsToggle.addEventListener('click', function(e) {
        e.preventDefault();

        try {
            const submenu = document.getElementById('systemSettingsSubmenu');
            const toggleIcon = this.querySelector('.toggle-icon');

            if (submenu && toggleIcon) {
                submenu.classList.toggle('submenu-open');
                toggleIcon.classList.toggle('fa-chevron-down');
                toggleIcon.classList.toggle('fa-chevron-up');

                // Store the state in localStorage
                const isOpen = submenu.classList.contains('submenu-open');
                localStorage.setItem('systemSettingsOpen', isOpen);
            }
        } catch (error) {
            console.error('Error toggling system settings submenu:', error);
        }
    });
}

// Initialize on page load
window.addEventListener('DOMContentLoaded', function() {
    try {
        if (window.innerWidth < 768) {
            const mainContent = document.getElementById('main-content');
            if (mainContent) mainContent.style.marginLeft = '0';
        }

        // Restore submenu state from localStorage
        const systemSettingsOpen = localStorage.getItem('systemSettingsOpen') === 'true';
        if (systemSettingsOpen) {
            const submenu = document.getElementById('systemSettingsSubmenu');
            const systemSettingsToggleBtn = document.getElementById('systemSettingsToggle');

            if (submenu && systemSettingsToggleBtn) {
                const toggleIcon = systemSettingsToggleBtn.querySelector('.toggle-icon');

                if (toggleIcon) {
                    submenu.classList.add('submenu-open');
                    toggleIcon.classList.remove('fa-chevron-down');
                    toggleIcon.classList.add('fa-chevron-up');
                }
            }
        }
    } catch (error) {
        console.error('Error initializing admin sidebar:', error);
    }
});
</script>