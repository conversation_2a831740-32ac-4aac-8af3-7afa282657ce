<?php
// This is a partial for the employe sidebar
?>

<!-- Mobile Header - Only visible on small screens -->
<header class="mobile-header">
    <div class="flex items-center">
        <button id="mobileMenuButton" class="mr-2 text-white focus:outline-none">
            <i class="fas fa-bars text-xl"></i>
        </button>
        <span class="text-lg font-bold">GestionConge</span>
    </div>
    <div class="flex items-center">
        <a href="/notifications" class="relative mr-4">
            <i class="fas fa-bell text-white text-lg"></i>
            <?php if (isset($notificationCount) && $notificationCount > 0): ?>
            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-4 h-4 flex items-center justify-center rounded-full"><?= $notificationCount ?></span>
            <?php endif; ?>
        </a>
        <a href="/profil" class="text-white">
            <i class="fas fa-user text-lg"></i>
        </a>
    </div>
</header>

<aside id="sidebar" class="sidebar">
    <div class="sidebar-header">
        <a href="/mes-demandes" class="sidebar-logo">
            <i class="fas fa-calendar-check text-white text-xl mr-2"></i>
            <span class="logo-text">GestionConge</span>
        </a>
        <button id="toggleSidebar" class="hidden md:block">
            <i class="fas fa-bars"></i>
        </button>
        <button id="closeSidebar" class="md:hidden">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <div class="sidebar-nav">
        <nav>
            <!-- <a href="/dashboard-employe" class="menu-item">
                <i class="fas fa-home"></i>
                <span class="menu-text">Tableau de bord</span>
            </a> -->
            <a href="/nouvelle-demande" class="menu-item">
                <i class="fas fa-plus-circle"></i>
                <span class="menu-text">Nouvelle demande</span>
            </a>
            <a href="/mes-demandes" class="menu-item">
                <i class="fas fa-list"></i>
                <span class="menu-text">Mes demandes</span>
            </a>
            <!-- <a href="#" class="menu-item" onclick="openHolidaysModal(); return false;">
                <i class="fas fa-calendar-alt"></i>
                <span class="menu-text">Jours Fériés</span>
            </a> -->
            <!-- <a href="/statistiques" class="menu-item">
                <i class="fas fa-chart-bar"></i>
                <span class="menu-text">Statistiques</span>
            </a> -->
            <a href="/notifications" class="menu-item">
                <i class="fas fa-bell"></i>
                <span class="menu-text">Notifications</span>
                <?php if (isset($notificationCount) && $notificationCount > 0): ?>
                <span class="notification-badge"><?= $notificationCount ?></span>
                <?php endif; ?>
            </a>
            <a href="/profil" class="menu-item">
                <i class="fas fa-user"></i>
                <span class="menu-text">Mon profil</span>
            </a>
            <a href="/logout" class="menu-item">
                <i class="fas fa-sign-out-alt"></i>
                <span class="menu-text">Déconnexion</span>
            </a>
        </nav>
    </div>
</aside>

<!-- Holidays Modal -->
<div id="holidaysModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden flex items-center justify-center p-4 z-50">
    <div class="modal-card max-w-md w-full">
        <div class="bg-purple-600 px-4 py-3 flex items-center justify-between">
            <h3 class="text-lg font-medium text-white">Jours Fériés <span id="holidayYear"></span></h3>
            <button type="button" class="text-white hover:text-gray-200" onclick="closeHolidaysModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="p-6">
            <!-- Loading state -->
            <div id="holidaysLoading" class="text-center py-8">
                <i class="fas fa-spinner fa-spin text-purple-600 text-2xl mb-2"></i>
                <p class="text-gray-600">Chargement des jours fériés...</p>
            </div>

            <!-- Error state -->
            <div id="holidaysError" class="hidden text-center py-8">
                <i class="fas fa-exclamation-triangle text-red-500 text-2xl mb-2"></i>
                <p class="text-red-600 mb-4">Erreur lors du chargement des jours fériés</p>
                <button onclick="loadHolidays()" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                    Réessayer
                </button>
            </div>

            <!-- Content -->
            <div id="holidaysContent" class="hidden">
                <!-- Country tabs -->
                <div class="flex space-x-2 mb-4" id="countryTabs">
                    <!-- Tabs will be populated dynamically -->
                </div>

                <!-- Holidays list -->
                <div id="holidaysList" class="space-y-3 max-h-80 overflow-y-auto">
                    <!-- Holidays will be populated dynamically -->
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// For desktop - toggle sidebar collapse
document.getElementById('toggleSidebar').addEventListener('click', function() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('sidebar-collapsed');

    // Adjust main content margin
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
        if (sidebar.classList.contains('sidebar-collapsed')) {
            mainContent.style.marginLeft = '60px';
        } else {
            mainContent.style.marginLeft = '240px';
        }
    }
});

// For mobile - open sidebar
document.getElementById('mobileMenuButton').addEventListener('click', function() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.add('sidebar-open');

    // Add overlay
    const overlay = document.createElement('div');
    overlay.id = 'sidebar-overlay';
    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-40';
    document.body.appendChild(overlay);

    // Close sidebar when clicking overlay
    overlay.addEventListener('click', closeMobileSidebar);
});

// For mobile - close sidebar
document.getElementById('closeSidebar').addEventListener('click', closeMobileSidebar);

function closeMobileSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.remove('sidebar-open');

    // Remove overlay
    const overlay = document.getElementById('sidebar-overlay');
    if (overlay) {
        overlay.remove();
    }
}

// Handle resize events to ensure proper layout
window.addEventListener('resize', function() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');

    if (window.innerWidth >= 768) {
        // Remove mobile-specific classes
        sidebar.classList.remove('sidebar-open');

        // Remove overlay
        const overlay = document.getElementById('sidebar-overlay');
        if (overlay) {
            overlay.remove();
        }

        // Reset desktop layout
        if (sidebar.classList.contains('sidebar-collapsed')) {
            if (mainContent) mainContent.style.marginLeft = '60px';
        } else {
            if (mainContent) mainContent.style.marginLeft = '240px';
        }
    } else {
        // Set mobile layout
        if (mainContent) mainContent.style.marginLeft = '0';
    }
});

// Initialize on page load
window.addEventListener('DOMContentLoaded', function() {
    if (window.innerWidth < 768) {
        const mainContent = document.getElementById('main-content');
        if (mainContent) mainContent.style.marginLeft = '0';
    }
});

// Holidays Modal Functions
let currentHolidaysData = null;
let currentSelectedCountry = null;

function openHolidaysModal() {
    document.getElementById('holidaysModal').classList.remove('hidden');
    loadHolidays();
}

function closeHolidaysModal() {
    document.getElementById('holidaysModal').classList.add('hidden');
}

function loadHolidays(year = null) {
    if (!year) {
        year = new Date().getFullYear();
    }

    // Update year in title
    document.getElementById('holidayYear').textContent = year;

    // Show loading state
    showHolidaysState('loading');

    // Fetch holidays from API
    fetch(`/api/holidays?year=${year}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                currentHolidaysData = data.holidays;
                displayHolidays(data.holidays);
            } else {
                throw new Error(data.error || 'Unknown error');
            }
        })
        .catch(error => {
            console.error('Error loading holidays:', error);
            showHolidaysState('error');
        });
}

function showHolidaysState(state) {
    const loading = document.getElementById('holidaysLoading');
    const error = document.getElementById('holidaysError');
    const content = document.getElementById('holidaysContent');

    // Hide all states
    loading.classList.add('hidden');
    error.classList.add('hidden');
    content.classList.add('hidden');

    // Show requested state
    switch(state) {
        case 'loading':
            loading.classList.remove('hidden');
            break;
        case 'error':
            error.classList.remove('hidden');
            break;
        case 'content':
            content.classList.remove('hidden');
            break;
    }
}

function displayHolidays(holidaysData) {
    const countryTabs = document.getElementById('countryTabs');
    const holidaysList = document.getElementById('holidaysList');

    // Clear existing content
    countryTabs.innerHTML = '';
    holidaysList.innerHTML = '';

    // Get available countries
    const countries = Object.keys(holidaysData);

    if (countries.length === 0) {
        holidaysList.innerHTML = '<p class="text-gray-500 text-center py-4">Aucun jour férié trouvé</p>';
        showHolidaysState('content');
        return;
    }

    // Create country tabs
    countries.forEach((country, index) => {
        const button = document.createElement('button');
        button.textContent = country;
        button.className = `px-3 py-1 text-sm font-medium rounded-md focus:outline-none ${
            index === 0 ? 'bg-purple-100 text-purple-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
        }`;
        button.onclick = () => showCountryHolidays(country);
        countryTabs.appendChild(button);
    });

    // Show first country by default
    currentSelectedCountry = countries[0];
    showCountryHolidays(countries[0]);
    showHolidaysState('content');
}

function showCountryHolidays(country) {
    if (!currentHolidaysData || !currentHolidaysData[country]) {
        return;
    }

    currentSelectedCountry = country;

    // Update tab styles
    const tabs = document.querySelectorAll('#countryTabs button');
    tabs.forEach(tab => {
        if (tab.textContent === country) {
            tab.className = 'px-3 py-1 text-sm font-medium rounded-md bg-purple-100 text-purple-700 focus:outline-none';
        } else {
            tab.className = 'px-3 py-1 text-sm font-medium rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 focus:outline-none';
        }
    });

    // Display holidays for selected country
    const holidaysList = document.getElementById('holidaysList');
    const holidays = currentHolidaysData[country];

    holidaysList.innerHTML = '';

    holidays.forEach(holiday => {
        const holidayDiv = document.createElement('div');
        holidayDiv.className = 'text-sm py-2 border-b border-gray-100';
        holidayDiv.innerHTML = `
            <p class="font-medium">${holiday.nom}</p>
            <p class="text-gray-500">${holiday.date_formatted}</p>
        `;
        holidaysList.appendChild(holidayDiv);
    });
}

// Close modal when clicking outside
document.getElementById('holidaysModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeHolidaysModal();
    }
});
</script>