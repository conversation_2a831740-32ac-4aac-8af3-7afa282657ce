<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Statistiques Employé - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <!-- Chart.js for statistics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50">
    <!-- Sidebar-->
    <?php include_once APP_PATH . '/views/shared/sidebar_employe.php'; ?>


    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-4xl font-bold text-gray-800">📊 Mes statistiques de congés</h1>
                    <p class="text-3xl text-gray-600">Suivi de vos congés et historique des demandes</p>
                </div>
                <div>
                    <select id="yearFilter"
                        class="border border-gray-300 rounded-md px-3 py-1.5 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:border-transparent">
                        <?php
                            $currentYear = date('Y');
                            $prevYear = $currentYear - 1;
                            $nextYear = $currentYear + 1;
                        ?>
                        <option value="<?= $nextYear ?>"><?= $nextYear ?></option>
                        <option value="<?= $currentYear ?>" selected><?= $currentYear ?></option>
                        <option value="<?= $prevYear ?>"><?= $prevYear ?></option>
                    </select>
                </div>
            </div>
        </header>

        <!-- Stats Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-6 gap-4 mb-6">
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-sm font-semibold text-gray-800">Congés payés restants</h2>
                    <span class="text-blue-500 ml-4"><i class="fas fa-calendar-alt"></i></span>
                </div>
                <!-- <div class="text-3xl font-bold text-gray-900"><?= $congesRestants ?></div> -->
                <p class="text-2xl font-bold text-blue-600">13 / 25 jours</p>
                <div class="w-full bg-gray-200 h-2 rounded mt-2">
                    <div class="bg-blue-600 h-2 rounded" style="width:52%;"></div>
                </div>
                <!-- <div class="mt-1 text-sm text-gray-500">jours restants</div> -->
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-sm font-semibold text-gray-800">Congés exceptionnels</h2>
                    <span class="text-green-600 ml-4"><i class="fas fa-check-circle"></i></span>
                </div>
                <!-- <p class="text-2xl font-bold text-green-600">2 / 5 jours</p> -->
                <!-- <div class="w-full bg-gray-200 h-2 rounded mt-2">
                    <div class="bg-green-600 h-2 rounded" style="width:40%;"></div>
                </div> -->
                <div class="text-3xl font-bold text-green-600"><?= $congesPris ?></div>
                <div class="mt-1 text-sm text-gray-500">jours utilisés</div>
            </div>

            <div class="bg-white p-4 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-sm font-semibold text-gray-800">Congés sans solde utilisés</h2>
                    <span class="text-yellow-600 ml-4"><i class="fas fa-thumbs-up"></i></span>
                </div>
                <p class="text-2xl font-bold text-yellow-600">4 jours</p>
                <div class="w-full bg-gray-200 h-2 rounded mt-2">
                    <div class="bg-yellow-600 h-2 rounded" style="width:80%;"></div>
                </div>
                <!-- <div class="text-3xl font-bold text-gray-900"><?= $demandesApprouvees ?><s/div> -->
                <!-- <div class="mt-1 text-sm text-green-500"><?= $pourcentageApprouve ?>% d'approbation</div> -->
            </div>
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-sm font-semibold text-gray-800">En attente</h2>
                    <span class="text-yellow-500 ml-4"><i class="fas fa-hourglass-half text-yellow-500 text-xl"></i></span>
                </div>
                <div class="text-3xl font-bold text-yellow-500">3</div>
                <!-- <div class="text-3xl font-bold text-gray-900"><?= $demandesEnAttente ?></div> -->
                <div class="mt-1 text-sm text-gray-800">demandes en cours</div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-sm font-semibold text-gray-800">Acceptées</h2>
                    <span class="text-yellow-500 ml-4"><i class="fas fa-check-circle text-green-500 text-3xl mr-4"></i></span>
                </div>
                <div class="text-3xl font-bold text-green-500">15</div>
                <!-- <div class="text-3xl font-bold text-gray-900"><?= $demandesEnAttente ?></div> -->
            </div>
            
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-sm font-semibold text-gray-800">Refusées</h2>
                    <span class="text-yellow-500 ml-4"><i class="fas fa-times-circle text-red-500 text-3xl mr-4"></i></span>
                </div>
                <div class="text-3xl font-bold text-red-500">2</div>
                <!-- <div class="text-3xl font-bold text-gray-900"><?= $demandesEnAttente ?></div> -->
            </div>
        </div>
        <!-- Charts -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <!-- Monthly Usage -->
            <div class="bg-white p-8 rounded-lg shadow h-[400px]">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Évolution mensuelle</h2>
                <div class="h-[320px]">
                    <canvas id="chartMonthly" class="w-full h-full"></canvas>
                </div>
            </div>

            <!-- Leave Distribution -->
            <div class="bg-white p-8 rounded-lg shadow h-[400px]">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Répartition par type de congé</h2>
                <div class="h-[320px]">
                    <canvas id="chartType" class="w-full h-full"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Données factices
        const dataType = {
            labels: ['Payé', 'Exceptionnel', 'Sans solde'],
            datasets: [{
                data: [12, 3, 4],
                backgroundColor: ['#2563eb', '#16a34a', '#ca8a04']
            }]
        };
        new Chart(document.getElementById('chartType'), {
            type: 'doughnut',
            data: dataType,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });

        const dataMonthly = {
            labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
            datasets: [{
                label: 'Jours pris',
                data: [2, 1, 0, 3, 2, 1, 0, 0, 1, 2, 1, 1],
                backgroundColor: 'rgba(37,99,235,0.5)',
                borderColor: '#2563eb',
                borderWidth: 2
            }]
        };
        new Chart(document.getElementById('chartMonthly'), {
            type: 'bar',
            data: dataMonthly,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });
    </script>
</body>

</html>