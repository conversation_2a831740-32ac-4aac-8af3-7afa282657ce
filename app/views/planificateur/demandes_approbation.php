<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Demandes à approuver - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_planificateur.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Demandes à approuver</h1>
                    <p class="text-gray-600">Suivi et validation des demandes de congés</p>
                </div>
                <a href="/planning" class="text-teal-600 hover:text-teal-800 flex items-center">
                    <i class="fas fa-calendar-alt mr-1"></i> Voir le planning
                </a>
            </div>
        </header>

        <!-- Status Legend -->
        <div class="bg-white rounded-lg shadow p-4 mb-6">
            <h3 class="text-sm font-medium text-gray-700 mb-3">Légende des statuts</h3>
            <div class="flex flex-wrap gap-4">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-yellow-400 rounded-full mr-2"></div>
                    <span class="text-sm text-gray-600">En attente du responsable</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                    <span class="text-sm text-gray-600">Prêt pour votre validation</span>
                </div>
            </div>
        </div>

        <?php if (isset($success)): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded">
                <p><?= $success ?></p>
            </div>
        <?php endif; ?>

        <?php if (empty($demandes)): ?>
            <div class="card text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                    <i class="fas fa-check text-green-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune demande en attente</h3>
                <p class="text-gray-500">Toutes les demandes de congés ont été traitées. Revenez plus tard pour vérifier si de nouvelles demandes ont été soumises.</p>
            </div>
        <?php else: ?>
            <div class="space-y-4">
                <?php foreach ($demandes as $demande): ?>
                    <div class="card relative overflow-hidden">
                        <!-- Status indicator bar -->
                        <div class="absolute top-0 bottom-0 left-0 w-1 bg-<?= $demande['status_color'] ?>-500"></div>

                        <div class="flex flex-col md:flex-row md:justify-between md:items-center">
                            <div class="mb-4 md:mb-0">
                                <div class="flex items-center mb-2">
                                    <h3 class="text-lg font-semibold text-gray-800 mr-3">
                                        <?= htmlspecialchars($demande['prenom'] . ' ' . $demande['nom']) ?>
                                    </h3>
                                    <!-- Status badge -->
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?= $demande['status_color'] ?>-100 text-<?= $demande['status_color'] ?>-800">
                                        <div class="w-2 h-2 bg-<?= $demande['status_color'] ?>-400 rounded-full mr-1"></div>
                                        <?= htmlspecialchars($demande['status_display']) ?>
                                    </span>
                                </div>

                                <div class="text-sm text-gray-500">
                                    <span class="inline-block mr-3">
                                        <i class="fas fa-hashtag mr-1"></i> <?= !empty($demande['reference_demande']) ? htmlspecialchars($demande['reference_demande']) : '<span class="text-gray-400">-</span>' ?>
                                    </span>
                                    <span class="inline-block mr-3">
                                        <i class="fas fa-building mr-1"></i> <?= htmlspecialchars($demande['departement'] ?? 'Non spécifié') ?>
                                    </span>
                                    <span class="inline-block mr-3">
                                        <i class="fas fa-calendar-alt mr-1"></i> <?= date('d/m/Y', strtotime($demande['date_debut'])) ?> - <?= date('d/m/Y', strtotime($demande['date_fin'])) ?>
                                    </span>
                                    <span class="inline-block">
                                        <i class="fas fa-clock mr-1"></i> <?= $demande['nbJours'] ?> jour<?= $demande['nbJours'] > 1 ? 's' : '' ?>
                                    </span>
                                </div>

                                <!-- Responsable info for requests waiting for manager approval -->
                                <?php if ($demande['statut'] === 'en_attente_responsable'): ?>
                                    <div class="text-sm text-gray-500 mt-1">
                                        <i class="fas fa-user-tie mr-1"></i> Responsable: <?= htmlspecialchars($demande['responsable_full_name']) ?>
                                    </div>
                                <?php endif; ?>

                                <div class="mt-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-teal-100 text-teal-800">
                                        <?= htmlspecialchars($demande['type_formatted']) ?>
                                    </span>
                                </div>
                            </div>

                            <div class="flex flex-wrap gap-2">
                                <!-- Details button - always visible -->
                                <button type="button"
                                        class="details-btn inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500"
                                        data-employee="<?= htmlspecialchars($demande['prenom'] . ' ' . $demande['nom'], ENT_QUOTES, 'UTF-8') ?>"
                                        data-type="<?= htmlspecialchars($demande['type_formatted'], ENT_QUOTES, 'UTF-8') ?>"
                                        data-date-debut="<?= htmlspecialchars(date('d/m/Y', strtotime($demande['date_debut'])), ENT_QUOTES, 'UTF-8') ?>"
                                        data-date-fin="<?= htmlspecialchars(date('d/m/Y', strtotime($demande['date_fin'])), ENT_QUOTES, 'UTF-8') ?>"
                                        data-duree="<?= htmlspecialchars($demande['nbJours'], ENT_QUOTES, 'UTF-8') ?>"
                                        data-motif="<?= htmlspecialchars($demande['motif'] ?? 'Non spécifié', ENT_QUOTES, 'UTF-8') ?>"
                                        data-demande-id="<?= htmlspecialchars($demande['id'], ENT_QUOTES, 'UTF-8') ?>"
                                        data-reference="<?= htmlspecialchars($demande['reference_demande'] ?? '', ENT_QUOTES, 'UTF-8') ?>"
                                        data-status="<?= htmlspecialchars($demande['statut'], ENT_QUOTES, 'UTF-8') ?>"
                                        data-status-display="<?= htmlspecialchars($demande['status_display'], ENT_QUOTES, 'UTF-8') ?>"
                                        data-responsable="<?= htmlspecialchars($demande['responsable_full_name'], ENT_QUOTES, 'UTF-8') ?>"
                                        data-actionable="<?= $demande['is_actionable_by_planificateur'] ? 'true' : 'false' ?>">
                                    <i class="fas fa-eye mr-1"></i> Détails
                                </button>

                                <!-- Action buttons - only visible when actionable by planificateur -->
                                <?php if ($demande['is_actionable_by_planificateur']): ?>
                                    <a href="/planificateur/approuverDemande?id=<?= $demande['id'] ?>"
                                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                       onclick="return confirm('Êtes-vous sûr de vouloir approuver cette demande ?')">
                                        <i class="fas fa-check mr-1"></i> Approuver
                                    </a>
                                    <a href="/planificateur/rejeterDemande?id=<?= $demande['id'] ?>"
                                       class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                        <i class="fas fa-times mr-1"></i> Rejeter
                                    </a>
                                <?php else: ?>
                                    <!-- Disabled buttons with tooltip for non-actionable requests -->
                                    <div class="relative group">
                                        <button type="button"
                                                class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md shadow-sm text-gray-400 bg-gray-100 cursor-not-allowed"
                                                disabled>
                                            <i class="fas fa-check mr-1"></i> Approuver
                                        </button>
                                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-800 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                                            En attente de l'approbation du responsable
                                        </div>
                                    </div>
                                    <div class="relative group">
                                        <button type="button"
                                                class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md shadow-sm text-gray-400 bg-gray-100 cursor-not-allowed"
                                                disabled>
                                            <i class="fas fa-times mr-1"></i> Rejeter
                                        </button>
                                        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-800 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                                            En attente de l'approbation du responsable
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Details Modal -->
    <div id="detailsModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Détails de la demande</h3>
                    <button type="button" onclick="closeModal()" class="text-gray-400 hover:text-gray-500">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="px-6 py-4">
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Référence</p>
                    <p class="text-base font-medium text-gray-900" id="modal-reference">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Employé</p>
                    <p class="text-base font-medium text-gray-900" id="modal-employee">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Statut</p>
                    <p class="text-base font-medium text-gray-900" id="modal-status">-</p>
                </div>
                <div class="mb-4" id="modal-responsable-section" style="display: none;">
                    <p class="text-sm font-medium text-gray-500">Responsable assigné</p>
                    <p class="text-base font-medium text-gray-900" id="modal-responsable">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Type de congé</p>
                    <p class="text-base font-medium text-gray-900" id="modal-type">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Période</p>
                    <p class="text-base font-medium text-gray-900" id="modal-period">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Durée</p>
                    <p class="text-base font-medium text-gray-900" id="modal-duration">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm font-medium text-gray-500">Motif</p>
                    <p class="text-base font-medium text-gray-900" id="modal-motif">-</p>
                </div>
            </div>
            <div class="bg-gray-50 px-6 py-3 flex justify-between">
                <div id="modal-action-buttons" class="flex space-x-2">
                    <!-- Action buttons will be dynamically shown/hidden based on status -->
                    <a href="#" id="modal-approve-link" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500" style="display: none;">
                        <i class="fas fa-check mr-1"></i> Approuver
                    </a>
                    <a href="#" id="modal-reject-link" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" style="display: none;">
                        <i class="fas fa-times mr-1"></i> Rejeter
                    </a>
                    <div id="modal-waiting-message" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600" style="display: none;">
                        <i class="fas fa-clock mr-2 text-yellow-500"></i>
                        En attente de l'approbation du responsable
                    </div>
                </div>
                <button type="button" onclick="closeModal()" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                    Fermer
                </button>
            </div>
        </div>
    </div>

    <script>
        // Event delegation for details buttons
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners to all details buttons
            document.querySelectorAll('.details-btn').forEach(function(button) {
                button.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Get data from data attributes
                    const employee = this.dataset.employee || '';
                    const type = this.dataset.type || '';
                    const dateDebut = this.dataset.dateDebut || '';
                    const dateFin = this.dataset.dateFin || '';
                    const duree = this.dataset.duree || '';
                    const motif = this.dataset.motif || 'Non spécifié';
                    const demandeId = this.dataset.demandeId || '';
                    const reference = this.dataset.reference || '';

                    showDetails(employee, type, dateDebut, dateFin, duree, motif, demandeId, reference);
                });
            });
        });

        function showDetails(employee, type, dateDebut, dateFin, duree, motif, demandeId, reference) {
            console.log('showDetails called with:', {employee, type, dateDebut, dateFin, duree, motif, demandeId, reference});

            // Get the button that was clicked to extract additional data
            const clickedButton = event.target.closest('.details-btn');
            const status = clickedButton.dataset.status;
            const statusDisplay = clickedButton.dataset.statusDisplay;
            const responsable = clickedButton.dataset.responsable;
            const isActionable = clickedButton.dataset.actionable === 'true';

            try {
                document.getElementById('modal-employee').textContent = employee;
                document.getElementById('modal-type').textContent = type;
                document.getElementById('modal-period').textContent = dateDebut + (dateDebut !== dateFin ? ' - ' + dateFin : '');
                document.getElementById('modal-duration').textContent = duree + ' jour' + (parseInt(duree) > 1 ? 's' : '');
                document.getElementById('modal-motif').textContent = motif || 'Aucun motif spécifié';
                document.getElementById('modal-status').textContent = statusDisplay;

                // Update reference if element exists
                const referenceElement = document.getElementById('modal-reference');
                if (referenceElement) {
                    referenceElement.textContent = reference || '-';
                }

                // Show/hide responsable section based on status
                const responsableSection = document.getElementById('modal-responsable-section');
                const responsableElement = document.getElementById('modal-responsable');
                if (status === 'en_attente_responsable') {
                    responsableSection.style.display = 'block';
                    responsableElement.textContent = responsable;
                } else {
                    responsableSection.style.display = 'none';
                }

                // Set the links for approve and reject
                const approveUrl = '/planificateur/approuverDemande?id=' + demandeId;
                const rejectUrl = '/planificateur/rejeterDemande?id=' + demandeId;

                console.log('Setting modal links:', {approveUrl, rejectUrl});

                const approveLink = document.getElementById('modal-approve-link');
                const rejectLink = document.getElementById('modal-reject-link');
                const waitingMessage = document.getElementById('modal-waiting-message');

                if (approveLink) approveLink.href = approveUrl;
                if (rejectLink) rejectLink.href = rejectUrl;

                // Show/hide action buttons based on actionable status
                if (isActionable) {
                    approveLink.style.display = 'inline-flex';
                    rejectLink.style.display = 'inline-flex';
                    waitingMessage.style.display = 'none';
                } else {
                    approveLink.style.display = 'none';
                    rejectLink.style.display = 'none';
                    waitingMessage.style.display = 'inline-flex';
                }

                // Show the modal
                document.getElementById('detailsModal').classList.remove('hidden');
            } catch (error) {
                console.error('Error in showDetails:', error);
                alert('Erreur lors de l\'affichage des détails. Veuillez réessayer.');
            }
        }

        function closeModal() {
            document.getElementById('detailsModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('detailsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !document.getElementById('detailsModal').classList.contains('hidden')) {
                closeModal();
            }
        });
    </script>
</body>
</html>
