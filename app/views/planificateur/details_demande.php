<?php
// View for displaying leave request details for planificateur
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Détails de la demande - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_planificateur.php'; ?>

    <div class="md:ml-[240px] p-4 md:p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 class="text-xl sm:text-2xl font-bold text-gray-800">Détails de la demande</h1>
                    <p class="text-sm sm:text-base text-gray-600">Informations complètes sur la demande de congé</p>
                </div>
                <a href="/planificateur/mes-demandes" class="text-purple-600 hover:text-purple-800 flex items-center gap-2">
                    <i class="fas fa-arrow-left"></i> Retour à mes demandes
                </a>
            </div>
        </header>

        <?php if (isset($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded">
                <p><?= $error ?></p>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded">
                <p><?= $success ?></p>
            </div>
        <?php endif; ?>

        <?php if (isset($demande)): ?>
            <div class="card">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h2 class="text-lg font-semibold text-gray-800 mb-4">Informations générales</h2>
                            <div class="space-y-4">
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Référence</p>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <?= !empty($demande['reference_demande']) ? htmlspecialchars($demande['reference_demande']) : '<span class="text-gray-400">-</span>' ?>
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Type de congé</p>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <?php
                                            $demandeModel = new DemandeModel();
                                            echo htmlspecialchars($demandeModel->formatLeaveType($demande['type']));
                                        ?>
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Période</p>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <?= date('d/m/Y', strtotime($demande['date_debut'])) ?>
                                        <?php if ($demande['date_debut'] !== $demande['date_fin']): ?>
                                            au <?= date('d/m/Y', strtotime($demande['date_fin'])) ?>
                                        <?php endif; ?>
                                        <?php if ($demande['demi_journee']): ?>
                                            (<?= $demande['demi_type'] === 'matin' ? 'Matin' : 'Après-midi' ?>)
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Durée</p>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <?php
                                            $start = new DateTime($demande['date_debut']);
                                            $end = new DateTime($demande['date_fin']);
                                            $interval = $start->diff($end);
                                            $days = $interval->days + 1;

                                            // Adjust for half days
                                            if ($demande['demi_journee']) {
                                                $days = $days - 0.5;
                                            }

                                            echo $days . ' jour' . ($days > 1 ? 's' : '');
                                        ?>
                                    </p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Statut</p>
                                    <p class="mt-1">
                                        <?php if (strtolower($demande['statut']) === 'en cours'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                En attente
                                            </span>
                                        <?php elseif (strtolower($demande['statut']) === 'acceptée'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Approuvée
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                Rejetée
                                            </span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h2 class="text-lg font-semibold text-gray-800 mb-4">Détails supplémentaires</h2>
                            <div class="space-y-4">
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Date de la demande</p>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <?= date('d/m/Y à H:i', strtotime($demande['date_demande'])) ?>
                                    </p>
                                </div>
                                <?php if (!empty($demande['date_decision'])): ?>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Date de décision</p>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <?= date('d/m/Y à H:i', strtotime($demande['date_decision'])) ?>
                                    </p>
                                </div>
                                <?php endif; ?>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Motif</p>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <?= !empty($demande['motif']) ? htmlspecialchars($demande['motif']) : 'Non spécifié' ?>
                                    </p>
                                </div>
                                <?php if (!empty($demande['motif_rejet'])): ?>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Motif de rejet</p>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <?= htmlspecialchars($demande['motif_rejet']) ?>
                                    </p>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($demande['justificatif'])): ?>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Justificatif</p>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <a href="/uploads/justificatifs/<?= htmlspecialchars($demande['justificatif']) ?>" target="_blank" class="text-purple-600 hover:text-purple-800">
                                            <i class="fas fa-file-alt mr-1"></i> Voir le justificatif
                                        </a>
                                    </p>
                                </div>
                                <?php endif; ?>
                                <?php if (!empty($demande['valideur_nom'])): ?>
                                <div>
                                    <p class="text-sm font-medium text-gray-500">Validé par</p>
                                    <p class="mt-1 text-sm text-gray-900">
                                        <?= htmlspecialchars($demande['valideur_prenom'] . ' ' . $demande['valideur_nom']) ?>
                                    </p>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (strtolower($demande['statut']) === 'en cours'): ?>
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
                    <a href="#" onclick="handleCancellation(event, '/planificateur/annuler-demande?id=<?= $demande['id'] ?>')" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition flex items-center gap-2">
                        <i class="fas fa-times"></i> Annuler la demande
                    </a>
                </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="card">
                <p class="text-gray-500 text-center py-8">Demande non trouvée.</p>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Modern confirmation for request cancellation
        async function handleCancellation(event, url) {
            event.preventDefault();

            try {
                const confirmed = await confirmWarning(
                    'Êtes-vous sûr de vouloir annuler cette demande de congé ?<br><br>' +
                    '<div class="bg-yellow-50 p-3 rounded-lg mt-3">' +
                    '<p class="text-sm text-yellow-800 font-medium">⚠️ Cette action est irréversible</p>' +
                    '<p class="text-sm text-yellow-700">Une fois annulée, vous devrez créer une nouvelle demande si nécessaire.</p>' +
                    '</div>',
                    'Annuler la demande'
                );

                if (confirmed) {
                    window.location.href = url;
                }
            } catch (error) {
                console.error('Error in cancellation confirmation:', error);
            }
        }
    </script>
</body>
</html>
