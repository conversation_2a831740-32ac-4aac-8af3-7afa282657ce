<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Gestion des jours fériés - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>

<body class="bg-gray-50">
    <?php include_once __DIR__ . '/../shared/sidebar_planificateur.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Jo<PERSON> fériés</h1>
                    <p class="text-gray-600">Consultation des jours non travaillés</p>
                </div>
            </div>
        </header>

        <?php if (isset($success) && $success): ?>
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded">
            <p>Le jour férié a été ajouté avec succès.</p>
        </div>
        <?php endif; ?>

        <?php if (isset($deleted) && $deleted): ?>
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 rounded">
            <p>Le jour férié a été supprimé avec succès.</p>
        </div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded">
            <p><?= $error ?></p>
        </div>
        <?php endif; ?>

        <div class="bg-white rounded-lg shadow-sm mb-6 p-4 flex flex-wrap items-center justify-between">
            <div class="w-full sm:w-auto mb-2 sm:mb-0 mr-4">
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Rechercher</label>
                <input type="text" id="search" placeholder="Nom du jour férié ou pays..."
                    class="w-full sm:w-64 text-sm border-gray-300 rounded-md focus:ring-teal-500 focus:border-teal-500">
            </div>
            <div class="mr-4 mb-2 sm:mb-0">
                <label for="pays" class="block text-sm font-medium text-gray-700 mb-1">Pays</label>
                <select id="pays" name="pays"
                    class="text-sm border-gray-300 rounded-md focus:ring-teal-500 focus:border-teal-500">
                    <option value="">Tous les pays</option>
                    <?php foreach ($allPays as $paysOption): ?>
                    <option value="<?= htmlspecialchars($paysOption) ?>"
                        <?= ($pays == $paysOption) ? 'selected' : '' ?>><?= htmlspecialchars($paysOption) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div>
                <label for="year" class="block text-sm font-medium text-gray-700 mb-1">Année</label>
                <select id="year" name="year"
                    class="text-sm border-gray-300 rounded-md focus:ring-teal-500 focus:border-teal-500">
                    <option value="2024" <?= ($year == '2024') ? 'selected' : '' ?>>2024</option>
                    <option value="2025" <?= ($year == '2025') ? 'selected' : '' ?>>2025</option>
                    <option value="2026" <?= ($year == '2026') ? 'selected' : '' ?>>2026</option>
                </select>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Nom</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Pays</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($holidays)): ?>
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">Aucun jour férié trouvé</td>
                        </tr>
                        <?php else: ?>
                        <?php foreach ($holidays as $holiday): ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($holiday['name']) ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?= date('d/m/Y', strtotime($holiday['date'])) ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php if ($holiday['is_recurring']): ?>
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    Récurrent
                                </span>
                                <?php else: ?>
                                <span
                                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    Unique
                                </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?= htmlspecialchars($holiday['pays'] ?? 'France') ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="text-gray-500"><i class="fas fa-eye"></i> Consultation uniquement</span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <script src="/assets/js/jours_feries_planificateur.js">
    </script>
</body>

</html>