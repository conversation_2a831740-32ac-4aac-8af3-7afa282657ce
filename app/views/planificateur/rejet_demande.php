<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Rejeter une demande - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_planificateur.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Rejeter une demande</h1>
                    <p class="text-gray-600">Veuillez indiquer le motif du rejet</p>
                </div>
                <a href="/planificateur/demandes_approbation" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center">
                    <i class="fas fa-arrow-left mr-2"></i> Retour
                </a>
            </div>
        </header>

        <?php if (isset($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded">
                <p><?= $error ?></p>
            </div>
        <?php endif; ?>

        <div class="bg-white rounded-lg shadow-sm p-6">
            <?php if (isset($demande)): ?>
                <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Détails de la demande</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm font-medium text-gray-500">Employé</p>
                            <p class="text-sm text-gray-900"><?= htmlspecialchars($demande['prenom'] . ' ' . $demande['nom']) ?></p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Type de congé</p>
                            <p class="text-sm text-gray-900"><?= htmlspecialchars($demande['type']) ?></p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Période</p>
                            <p class="text-sm text-gray-900">
                                <?= date('d/m/Y', strtotime($demande['date_debut'])) ?>
                                <?php if ($demande['date_debut'] != $demande['date_fin']): ?>
                                    - <?= date('d/m/Y', strtotime($demande['date_fin'])) ?>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-500">Durée</p>
                            <p class="text-sm text-gray-900"><?= $demande['nbJours'] ?? (floor((strtotime($demande['date_fin']) - strtotime($demande['date_debut'])) / (60 * 60 * 24)) + 1) ?> jour(s)</p>
                        </div>
                        <?php if (!empty($demande['motif'])): ?>
                        <div class="md:col-span-2">
                            <p class="text-sm font-medium text-gray-500">Motif de la demande</p>
                            <p class="text-sm text-gray-900"><?= htmlspecialchars($demande['motif']) ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
            
            <form action="/planificateur/rejeterDemande?id=<?= $id ?>" method="POST" id="rejectForm">
                <div class="mb-4">
                    <label for="motif_rejet" class="block text-sm font-medium text-gray-700 mb-1">Motif du rejet <span class="text-red-500">*</span></label>
                    <textarea id="motif_rejet" name="motif_rejet" rows="4" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="Veuillez indiquer pourquoi vous rejetez cette demande de congé..." required></textarea>
                    <p class="mt-2 text-sm text-gray-500">Cette information sera communiquée à l'employé dans une notification.</p>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Motifs courants</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <button type="button" onclick="setRejectReason('Période de forte activité dans le service')" class="text-left px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">Période de forte activité</button>
                        <button type="button" onclick="setRejectReason('Effectif insuffisant pendant la période demandée')" class="text-left px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">Effectif insuffisant</button>
                        <button type="button" onclick="setRejectReason('Demande trop tardive')" class="text-left px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">Demande trop tardive</button>
                        <button type="button" onclick="setRejectReason('Solde de congés insuffisant')" class="text-left px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">Solde insuffisant</button>
                    </div>
                </div>

                <div class="border-t pt-5">
                    <div class="flex justify-end">
                        <a href="/planificateur/demandes_approbation" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-2">
                            Annuler
                        </a>
                        <button type="submit" id="submitButton" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            Confirmer le rejet
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        function setRejectReason(reason) {
            const textarea = document.getElementById('motif_rejet');
            textarea.value = reason;
            textarea.focus();
        }

        // Add form submission handling
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('rejectForm');
            const submitBtn = document.getElementById('submitButton');

            console.log('Form initialized:', form);
            console.log('Submit button initialized:', submitBtn);

            form.addEventListener('submit', function(e) {
                console.log('Form submit event triggered');

                // Prevent multiple submissions
                if (submitBtn.disabled) {
                    console.log('Button already disabled, preventing submission');
                    e.preventDefault();
                    return false;
                }

                // Validate form
                const motifRejet = document.getElementById('motif_rejet').value.trim();
                console.log('Motif value:', motifRejet);

                if (!motifRejet) {
                    console.log('Empty motif, preventing submission');
                    e.preventDefault();
                    alert('Veuillez indiquer un motif de rejet');
                    return false;
                }

                // Disable button to prevent double submission
                console.log('Disabling submit button');
                submitBtn.disabled = true;
                submitBtn.innerHTML = 'Traitement en cours...';

                // Log submission
                console.log('Form submitted with motif: ' + motifRejet);

                // Add a hidden field with the current timestamp to prevent caching issues
                const timestampField = document.createElement('input');
                timestampField.type = 'hidden';
                timestampField.name = 'timestamp';
                timestampField.value = Date.now();
                form.appendChild(timestampField);

                console.log('Added timestamp field:', timestampField.value);

                // Allow form to submit
                return true;
            });
        });
    </script>
</body>
</html>
