<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Tableau de bord Planificateur - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>

<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_planificateur.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Espace Planificateur 📆</h1>
                    <p class="text-gray-600">Bienvenue,
                        <?= htmlspecialchars($_SESSION['prenom'] . ' ' . $_SESSION['nom']) ?>, Supervisez les plannings
                        et anticipez les absences.</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="/profil" class="flex items-center">
                        <div
                            class="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center text-teal-800 font-bold">
                            <?= strtoupper(substr($_SESSION['prenom'], 0, 1)) ?>
                        </div>
                    </a>
                </div>
            </div>
        </header>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="card p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-500 mr-4">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">Demandes en attente</p>
                        <h3 class="text-2xl font-bold"><?= $pendingRequests ?></h3>
                    </div>
                </div>
            </div>
            <div class="card p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-500 mr-4">
                        <i class="fas fa-check text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">Demandes approuvées</p>
                        <h3 class="text-2xl font-bold"><?= $approvedRequests ?></h3>
                    </div>
                </div>
            </div>
            <div class="card p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100 text-red-500 mr-4">
                        <i class="fas fa-times text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">Demandes rejetées</p>
                        <h3 class="text-2xl font-bold"><?= $rejectedRequests ?></h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Team Availability -->
            <div class="card p-6 lg:col-span-2">
                <h2 class="text-lg font-semibold mb-4">Disponibilité de l'équipe</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Disponibles</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Absents</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Taux</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($teamAvailability as $day): ?>
                                <?php
                                    $date = new DateTime($day['date']);
                                    $formattedDate = $date->format('d/m/Y');
                                    $total = $day['available'] + $day['unavailable'];
                                    $availabilityRate = $total > 0 ? round(($day['available'] / $total) * 100) : 0;
                                ?>
                                <tr>
                                    <td class="px-4 py-3 whitespace-nowrap"><?= $formattedDate ?></td>
                                    <td class="px-4 py-3 whitespace-nowrap"><?= $day['available'] ?></td>
                                    <td class="px-4 py-3 whitespace-nowrap"><?= $day['unavailable'] ?></td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-24 bg-gray-200 rounded-full h-2.5">
                                                <div class="bg-teal-600 h-2.5 rounded-full" style="width: <?= $availabilityRate ?>%"></div>
                                            </div>
                                            <span class="ml-2 text-sm text-gray-600"><?= $availabilityRate ?>%</span>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div class="mt-4 text-right">
                    <a href="/planning" class="text-teal-600 hover:text-teal-800 text-sm font-medium">
                        Voir le planning complet <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>

            <!-- Upcoming Holidays -->
            <div class="card p-6">
                <h2 class="text-lg font-semibold mb-4">Prochains jours fériés</h2>
                <div class="space-y-4">
                    <?php if (empty($nextHolidays)): ?>
                        <p class="text-gray-500 text-sm">Aucun jour férié à venir</p>
                    <?php else: ?>
                        <?php foreach ($nextHolidays as $holiday): ?>
                            <?php
                                $holidayDate = new DateTime($holiday['date']);
                                $formattedDate = $holidayDate->format('d/m/Y');
                                $daysUntil = $holidayDate->diff(new DateTime())->days;
                            ?>
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <div class="p-2 rounded-full bg-yellow-100 text-yellow-500 mr-3">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                                <div>
                                    <h4 class="font-medium"><?= htmlspecialchars($holiday['name']) ?></h4>
                                    <div class="flex items-center text-sm text-gray-500">
                                        <span><?= $formattedDate ?></span>
                                        <span class="mx-2">•</span>
                                        <span><?= $daysUntil ?> jours</span>
                                        <?php if (isset($holiday['pays'])): ?>
                                            <span class="mx-2">•</span>
                                            <span><?= htmlspecialchars($holiday['pays']) ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <div class="mt-4 text-right">
                    <a href="/jours-feries" class="text-teal-600 hover:text-teal-800 text-sm font-medium">
                        Gérer les jours fériés <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Upcoming Absences -->
        <div class="card p-6 mt-6">
            <h2 class="text-lg font-semibold mb-4">Absences à venir</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employé</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($upcomingAbsences)): ?>
                            <tr>
                                <td colspan="5" class="px-4 py-4 text-center text-gray-500">Aucune absence à venir</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($upcomingAbsences as $absence): ?>
                                <?php
                                    $startDate = new DateTime($absence['date_debut']);
                                    $endDate = new DateTime($absence['date_fin']);
                                    $formattedStart = $startDate->format('d/m/Y');
                                    $formattedEnd = $endDate->format('d/m/Y');
                                    $period = $formattedStart;
                                    if ($formattedStart !== $formattedEnd) {
                                        $period .= ' au ' . $formattedEnd;
                                    }
                                ?>
                                <tr>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= htmlspecialchars($absence['nom_complet']) ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= htmlspecialchars($absence['type_formatted']) ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= $period ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= $absence['duree'] ?> jour<?= $absence['duree'] > 1 ? 's' : '' ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            <?= $absence['statut'] === 'acceptée' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' ?>">
                                            <?= htmlspecialchars($absence['statut_formatted']) ?>
                                        </span>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            <div class="mt-4 text-right">
                <a href="/absences-a-venir" class="text-teal-600 hover:text-teal-800 text-sm font-medium">
                    Voir toutes les absences <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
    </div>
</body>

</html>