<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Statistiques Planificateur - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <!-- Chart.js for statistics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>

<body class="bg-gray-50">
    <!-- Sidebar-->
    <?php include_once APP_PATH . '/views/shared/sidebar_planificateur.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Statistiques de capacité</h1>
                    <p class="text-gray-600">Analyse et prévision des capacités de l'organisation</p>
                </div>
                <div class="flex space-x-2">
                    <button
                        class="px-3 py-1.5 bg-white border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50">
                        <i class="fas fa-download mr-1"></i> Exporter
                    </button>
                    <select id="yearFilter"
                        class="border border-gray-300 rounded-md px-3 py-1.5 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-teal-600 focus:border-transparent">
                        <?php
                            $currentYear = date('Y');
                            $prevYear = $currentYear - 1;
                            $nextYear = $currentYear + 1;
                        ?>
                        <option value="<?= $nextYear ?>" <?= $statsData['year'] == $nextYear ? 'selected' : '' ?>><?= $nextYear ?></option>
                        <option value="<?= $currentYear ?>" <?= $statsData['year'] == $currentYear ? 'selected' : '' ?>><?= $currentYear ?></option>
                        <option value="<?= $prevYear ?>" <?= $statsData['year'] == $prevYear ? 'selected' : '' ?>><?= $prevYear ?></option>
                    </select>
                </div>
            </div>
        </header>

        <!-- Stats Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-sm font-semibold text-gray-800">Demandes totales</h2>
                    <span class="text-teal-500"><i class="fas fa-file-alt"></i></span>
                </div>
                <div class="text-3xl font-bold text-gray-900">
                    <?php
                        $totalDemandes = array_sum(array_column($statsData['monthlyStats'], 'requests'));
                        echo $totalDemandes;
                    ?>
                </div>
                <div class="mt-1 text-sm text-gray-500">demandes traitées</div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-sm font-semibold text-gray-800">Demandes approuvées</h2>
                    <span class="text-green-500"><i class="fas fa-check-circle"></i></span>
                </div>
                <div class="text-3xl font-bold text-gray-900">
                    <?php
                        $totalApprovedDays = array_sum(array_column($statsData['monthlyStats'], 'approvedDays'));
                        echo $totalApprovedDays;
                    ?>
                </div>
                <div class="mt-1 text-sm text-green-500">
                    <?php
                        $pourcentageApprouve = $totalDemandes > 0 ? round(($totalApprovedDays / $totalDemandes) * 100) : 0;
                        echo $pourcentageApprouve;
                    ?>% d'approbation
                </div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-sm font-semibold text-gray-800">Capacité moyenne</h2>
                    <span class="text-blue-500"><i class="fas fa-users"></i></span>
                </div>
                <div class="text-3xl font-bold text-gray-900">
                    <?php
                        // Calculate average capacity
                        $totalMonths = count($statsData['monthlyStats']);
                        $totalCapacity = 0;

                        foreach ($statsData['monthlyStats'] as $month) {
                            $workingDays = 22; // Average working days per month
                            $totalEmployees = $statsData['totalUsers'];
                            $totalPossibleDays = $workingDays * $totalEmployees;
                            $approvedDays = $month['approvedDays'] ?? 0;
                            $capacity = $totalPossibleDays > 0 ? 100 - (($approvedDays / $totalPossibleDays) * 100) : 100;
                            $totalCapacity += max(0, min(100, $capacity));
                        }

                        $capaciteMoyenne = $totalMonths > 0 ? round($totalCapacity / $totalMonths) : 0;
                        echo $capaciteMoyenne;
                    ?>%
                </div>
                <div class="mt-1 text-sm text-gray-500">sur l'année en cours</div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-sm font-semibold text-gray-800">Jour le plus occupé</h2>
                    <span class="text-yellow-500"><i class="fas fa-calendar-day"></i></span>
                </div>
                <div class="text-3xl font-bold text-gray-900">
                    <?php
                        // Find the busiest day (day with lowest capacity)
                        $busiestDay = null;
                        $lowestCapacity = 100;

                        foreach ($statsData['monthlyStats'] as $month) {
                            $monthNum = $month['month_num'];
                            $workingDays = 22; // Average working days per month
                            $totalEmployees = $statsData['totalUsers'];
                            $totalPossibleDays = $workingDays * $totalEmployees;
                            $approvedDays = $month['approvedDays'] ?? 0;
                            $capacity = $totalPossibleDays > 0 ? 100 - (($approvedDays / $totalPossibleDays) * 100) : 100;
                            $capacity = max(0, min(100, $capacity));

                            if ($capacity < $lowestCapacity) {
                                $lowestCapacity = $capacity;
                                $busiestDay = $month['month'];
                            }
                        }

                        echo $busiestDay ? $busiestDay : 'N/A';
                    ?>
                </div>
                <div class="mt-1 text-sm text-red-500">
                    <?php if ($lowestCapacity < 100): ?>
                        Seulement <?= round($lowestCapacity) ?>% disponibles
                    <?php else: ?>
                        Données insuffisantes
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Capacity Forecast -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Prévision de capacité (6 mois)</h2>
                <div class="chart-container">
                    <canvas id="capacityForecastChart"></canvas>
                </div>
            </div>

            <!-- Department Comparison -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Capacité par département</h2>
                <div class="chart-container">
                    <canvas id="departmentCapacityChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Capacity Calendar View -->
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">Vue calendrier des capacités</h2>
                <div class="flex space-x-2">
                    <button
                        class="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded hover:bg-gray-200">Juin</button>
                    <button
                        class="px-3 py-1 text-xs font-medium text-white bg-teal-600 rounded hover:bg-teal-700">Juillet</button>
                    <button
                        class="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded hover:bg-gray-200">Août</button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full border-collapse">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="p-2 border text-xs font-medium text-gray-500">Lun</th>
                            <th class="p-2 border text-xs font-medium text-gray-500">Mar</th>
                            <th class="p-2 border text-xs font-medium text-gray-500">Mer</th>
                            <th class="p-2 border text-xs font-medium text-gray-500">Jeu</th>
                            <th class="p-2 border text-xs font-medium text-gray-500">Ven</th>
                            <th class="p-2 border text-xs font-medium text-gray-500">Sam</th>
                            <th class="p-2 border text-xs font-medium text-gray-500">Dim</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="p-0 border">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">1</span>
                                    <span class="text-xs font-medium text-green-700 mt-auto">95%</span>
                                    <div class="w-full bg-green-100 h-1.5 mt-1" title="95% disponible"></div>
                                </div>
                            </td>
                            <td class="p-0 border">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">2</span>
                                    <span class="text-xs font-medium text-green-700 mt-auto">92%</span>
                                    <div class="w-full bg-green-100 h-1.5 mt-1" title="92% disponible"></div>
                                </div>
                            </td>
                            <td class="p-0 border">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">3</span>
                                    <span class="text-xs font-medium text-green-700 mt-auto">90%</span>
                                    <div class="w-full bg-green-100 h-1.5 mt-1" title="90% disponible"></div>
                                </div>
                            </td>
                            <td class="p-0 border">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">4</span>
                                    <span class="text-xs font-medium text-green-700 mt-auto">88%</span>
                                    <div class="w-full bg-green-100 h-1.5 mt-1" title="88% disponible"></div>
                                </div>
                            </td>
                            <td class="p-0 border">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">5</span>
                                    <span class="text-xs font-medium text-yellow-700 mt-auto">75%</span>
                                    <div class="w-full bg-yellow-100 h-1.5 mt-1" title="75% disponible"></div>
                                </div>
                            </td>
                            <td class="p-0 border bg-gray-50">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">6</span>
                                </div>
                            </td>
                            <td class="p-0 border bg-gray-50">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">7</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="p-0 border">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">8</span>
                                    <span class="text-xs font-medium text-green-700 mt-auto">85%</span>
                                    <div class="w-full bg-green-100 h-1.5 mt-1" title="85% disponible"></div>
                                </div>
                            </td>
                            <td class="p-0 border">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">9</span>
                                    <span class="text-xs font-medium text-green-700 mt-auto">82%</span>
                                    <div class="w-full bg-green-100 h-1.5 mt-1" title="82% disponible"></div>
                                </div>
                            </td>
                            <td class="p-0 border">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">10</span>
                                    <span class="text-xs font-medium text-yellow-700 mt-auto">78%</span>
                                    <div class="w-full bg-yellow-100 h-1.5 mt-1" title="78% disponible"></div>
                                </div>
                            </td>
                            <td class="p-0 border">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">11</span>
                                    <span class="text-xs font-medium text-yellow-700 mt-auto">75%</span>
                                    <div class="w-full bg-yellow-100 h-1.5 mt-1" title="75% disponible"></div>
                                </div>
                            </td>
                            <td class="p-0 border">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">12</span>
                                    <span class="text-xs font-medium text-yellow-700 mt-auto">72%</span>
                                    <div class="w-full bg-yellow-100 h-1.5 mt-1" title="72% disponible"></div>
                                </div>
                            </td>
                            <td class="p-0 border bg-gray-50">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">13</span>
                                </div>
                            </td>
                            <td class="p-0 border bg-gray-50">
                                <div class="h-16 flex flex-col p-1">
                                    <span class="text-xs text-gray-500">14</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="mt-4">
                <div class="flex space-x-4 text-sm">
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-green-500 mr-1"></div>
                        <span>≥ 80% disponible</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-yellow-500 mr-1"></div>
                        <span>60-80% disponible</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-red-500 mr-1"></div>
                        <span>
                            < 60% disponible</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Critical Days -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Most Occupied Days -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Jours les plus occupés</h2>
                <div class="space-y-4">
                    <?php
                        // Find the 5 months with lowest capacity
                        $months = $statsData['monthlyStats'];

                        // Calculate capacity for each month
                        foreach ($months as &$month) {
                            $workingDays = 22; // Average working days per month
                            $totalEmployees = $statsData['totalUsers'];
                            $totalPossibleDays = $workingDays * $totalEmployees;
                            $approvedDays = $month['approvedDays'] ?? 0;
                            $capacity = $totalPossibleDays > 0 ? 100 - (($approvedDays / $totalPossibleDays) * 100) : 100;
                            $month['capacity'] = max(0, min(100, $capacity));
                            $month['absents'] = $approvedDays / 22; // Approximate number of people absent per day
                        }

                        // Sort by capacity (ascending)
                        usort($months, function($a, $b) {
                            return $a['capacity'] <=> $b['capacity'];
                        });

                        // Take the first 5 months
                        $busiestMonths = array_slice($months, 0, 5);

                        foreach ($busiestMonths as $index => $month):
                    ?>
                    <div class="flex items-center">
                        <span
                            class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center font-medium text-gray-700 mr-3">
                            <?= $index + 1 ?>
                        </span>
                        <div class="flex-1">
                            <div class="flex justify-between mb-1">
                                <span class="text-sm font-medium text-gray-700">
                                    <?= htmlspecialchars($month['month']) ?>
                                </span>
                                <span class="text-sm font-medium text-red-600">
                                    <?= round($month['absents']) ?> absents/jour
                                </span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <?php $occupationRate = min(100, 100 - $month['capacity']); ?>
                                <div class="bg-red-600 h-2 rounded-full" style="width: <?= $occupationRate ?>%"></div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <?php if (empty($busiestMonths)): ?>
                    <div class="text-center text-gray-500 py-4">
                        Aucune donnée disponible
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Leave distribution by department -->
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Congés par département</h2>
                <div class="space-y-4">
                    <?php
                        // Find the maximum average days for scaling
                        $maxAvgDays = 0;
                        foreach ($statsData['departmentStats'] as $dept) {
                            $maxAvgDays = max($maxAvgDays, $dept['avg_days']);
                        }

                        // Display department stats
                        foreach ($statsData['departmentStats'] as $dept):
                            $percentage = $maxAvgDays > 0 ? ($dept['avg_days'] / $maxAvgDays) * 100 : 0;
                    ?>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm font-medium"><?= htmlspecialchars($dept['departement']) ?></span>
                            <span class="text-sm text-gray-500"><?= number_format($dept['avg_days'] ?? 0, 1) ?> jours / employé</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-teal-600 h-2 rounded-full" style="width: <?= $percentage ?>%"></div>
                        </div>
                    </div>
                    <?php endforeach; ?>

                    <?php if (empty($statsData['departmentStats'])): ?>
                    <div class="text-center text-gray-500 py-4">
                        Aucune donnée disponible
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Optimization Suggestions -->
        <div class="bg-white p-6 rounded-lg shadow-sm">
            <div class="flex items-start space-x-4">
                <span class="bg-teal-100 text-teal-600 p-2 rounded-lg">
                    <i class="fas fa-lightbulb text-xl"></i>
                </span>
                <div>
                    <h2 class="text-lg font-semibold text-gray-800 mb-1">Recommandations d'optimisation</h2>
                    <p class="text-gray-600 mb-4">
                        En fonction de l'analyse des données, voici quelques recommandations pour optimiser la
                        planification des congés :
                    </p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="font-medium text-gray-800 mb-2">Court terme</h3>
                            <ul class="space-y-2">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-teal-500 mt-1 mr-2"></i>
                                    <span>Limitez les approbations de congés pour la période du 1er au 15 août.</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-teal-500 mt-1 mr-2"></i>
                                    <span>Encouragez les demandes de congés pour la deuxième quinzaine de septembre
                                        (capacité élevée).</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-teal-500 mt-1 mr-2"></i>
                                    <span>Priorisez les approbations pour le département Finance (utilisation faible des
                                        congés).</span>
                                </li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-800 mb-2">Long terme</h3>
                            <ul class="space-y-2">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-teal-500 mt-1 mr-2"></i>
                                    <span>Mettez en place une politique de congés alternés pour les périodes
                                        estivales.</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-teal-500 mt-1 mr-2"></i>
                                    <span>Établissez des quotas par équipe pour éviter les pics de congés
                                        simultanés.</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-teal-500 mt-1 mr-2"></i>
                                    <span>Permettez plus de télétravail pendant les périodes à faible capacité plutôt
                                        que des congés.</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Prepare data for charts
        const departmentLabels = <?= json_encode(array_column($statsData['departmentStats'], 'departement')) ?>;
        const departmentData = <?= json_encode(array_column($statsData['departmentStats'], 'avg_days')) ?>;

        // Prepare monthly data
        const monthlyLabels = <?= json_encode(array_column($statsData['monthlyStats'], 'month')) ?>;

        // Calculate capacity data (percentage of available days)
        const capacityData = [];
        <?php foreach ($statsData['monthlyStats'] as $month): ?>
            <?php
                // Calculate capacity as percentage of days available
                // This is a simplified calculation - in a real app you would use actual working days
                $workingDays = 22; // Average working days per month
                $totalEmployees = $statsData['totalUsers'];
                $totalPossibleDays = $workingDays * $totalEmployees;
                $approvedDays = $month['approvedDays'] ?? 0;
                $capacity = $totalPossibleDays > 0 ? 100 - (($approvedDays / $totalPossibleDays) * 100) : 100;
                // Ensure capacity is between 0 and 100
                $capacity = max(0, min(100, $capacity));
            ?>
            capacityData.push(<?= round($capacity) ?>);
        <?php endforeach; ?>
    </script>
    <script src="/assets/js/statistiques_planificateur.js"></script>
</body>

</html>