<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Absences à venir - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>

<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_planificateur.php'; ?>

    <!-- Modal for absence details -->
    <div id="absenceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4 overflow-hidden">
            <div class="flex justify-between items-center p-4 border-b">
                <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">Détails de l'absence</h3>
                <button type="button" class="text-gray-400 hover:text-gray-500" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6" id="modalContent">
                <div class="flex flex-col space-y-4">
                    <div class="flex flex-col md:flex-row md:space-x-4">
                        <div class="w-full md:w-1/2">
                            <p class="text-sm font-medium text-gray-500">Employé</p>
                            <p class="text-base font-medium text-gray-900" id="modalEmployee">-</p>
                        </div>
                        <div class="w-full md:w-1/2">
                            <p class="text-sm font-medium text-gray-500">Type d'absence</p>
                            <p class="text-base font-medium text-gray-900" id="modalType">-</p>
                        </div>
                    </div>

                    <div class="flex flex-col md:flex-row md:space-x-4">
                        <div class="w-full md:w-1/2">
                            <p class="text-sm font-medium text-gray-500">Date de début</p>
                            <p class="text-base font-medium text-gray-900" id="modalStartDate">-</p>
                        </div>
                        <div class="w-full md:w-1/2">
                            <p class="text-sm font-medium text-gray-500">Date de fin</p>
                            <p class="text-base font-medium text-gray-900" id="modalEndDate">-</p>
                        </div>
                    </div>

                    <div class="flex flex-col md:flex-row md:space-x-4">
                        <div class="w-full md:w-1/2">
                            <p class="text-sm font-medium text-gray-500">Durée</p>
                            <p class="text-base font-medium text-gray-900" id="modalDuration">-</p>
                        </div>
                        <div class="w-full md:w-1/2">
                            <p class="text-sm font-medium text-gray-500">Statut</p>
                            <div id="modalStatus">-</div>
                        </div>
                    </div>

                    <div>
                        <p class="text-sm font-medium text-gray-500">Motif</p>
                        <p class="text-base font-medium text-gray-900" id="modalReason">-</p>
                    </div>

                    <div id="modalApprovalSection" class="hidden">
                        <p class="text-sm font-medium text-gray-500">Validé par</p>
                        <p class="text-base font-medium text-gray-900" id="modalApprovedBy">-</p>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 flex justify-end">
                <button type="button" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-300" onclick="closeModal()">
                    Fermer
                </button>
            </div>
        </div>
    </div>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Absences à venir</h1>
                    <p class="text-gray-600">Consultez et gérez les absences planifiées</p>
                </div>
                <div class="flex space-x-2">
                    <button
                        class="px-3 py-1.5 bg-white border border-gray-300 rounded-md text-gray-700 text-sm hover:bg-gray-50">
                        <i class="fas fa-download mr-1"></i> Exporter
                    </button>
                </div>
            </div>
        </header>

        <!-- Filters -->
        <div class="card p-4 mb-6">
            <form action="/absences-a-venir" method="get" class="flex flex-wrap gap-4">
                <div>
                    <label for="departement" class="block text-sm font-medium text-gray-700 mb-1">Département</label>
                    <select id="departement" name="departement"
                        class="border border-gray-300 rounded-md px-3 py-1.5 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-teal-600 focus:border-transparent">
                        <option value="all">Tous les départements</option>
                        <?php foreach ($departments as $dept): ?>
                            <option value="<?= htmlspecialchars($dept) ?>" <?= $selectedDept === $dept ? 'selected' : '' ?>>
                                <?= htmlspecialchars($dept) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div>
                    <label for="date_debut" class="block text-sm font-medium text-gray-700 mb-1">Date début</label>
                    <input type="date" id="date_debut" name="date_debut" value="<?= htmlspecialchars($startDate) ?>"
                        class="border border-gray-300 rounded-md px-3 py-1.5 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-teal-600 focus:border-transparent">
                </div>
                <div>
                    <label for="date_fin" class="block text-sm font-medium text-gray-700 mb-1">Date fin</label>
                    <input type="date" id="date_fin" name="date_fin" value="<?= htmlspecialchars($endDate) ?>"
                        class="border border-gray-300 rounded-md px-3 py-1.5 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-teal-600 focus:border-transparent">
                </div>
                <div>
                    <label for="statut" class="block text-sm font-medium text-gray-700 mb-1">Statut</label>
                    <select id="statut" name="statut"
                        class="border border-gray-300 rounded-md px-3 py-1.5 text-gray-700 bg-white focus:outline-none focus:ring-2 focus:ring-teal-600 focus:border-transparent">
                        <option value="all">Tous les statuts</option>
                        <option value="acceptée" <?= $selectedStatus === 'acceptée' ? 'selected' : '' ?>>Approuvée</option>
                        <option value="en cours" <?= $selectedStatus === 'en cours' ? 'selected' : '' ?>>En attente</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit"
                        class="bg-teal-600 text-white px-4 py-1.5 rounded-md text-sm hover:bg-teal-700">
                        <i class="fas fa-filter mr-1"></i> Filtrer
                    </button>
                </div>
            </form>
        </div>

        <!-- Upcoming Absences Table -->
        <div class="card p-6">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">Absences à venir avec historique des approbations</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employé</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Référence</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Historique</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($absences)): ?>
                            <tr>
                                <td colspan="8" class="px-4 py-4 text-center text-gray-500">Aucune absence à venir</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($absences as $absence): ?>
                                <tr class="hover:bg-gray-50">
                                    <!-- Employee -->
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-8 w-8 rounded-full bg-indigo-100 flex items-center justify-center">
                                                <span class="text-indigo-800 font-medium text-sm">
                                                    <?= strtoupper(substr($absence['user_prenom'], 0, 1) . substr($absence['user_nom'], 0, 1)) ?>
                                                </span>
                                            </div>
                                            <div class="ml-3">
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?= htmlspecialchars($absence['nom_complet']) ?>
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    <?= htmlspecialchars($absence['departement']) ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>

                                    <!-- Reference -->
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= !empty($absence['reference_demande']) ? htmlspecialchars($absence['reference_demande']) : '-' ?>
                                        </div>
                                    </td>

                                    <!-- Type -->
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                            <?= htmlspecialchars($absence['type_formatted']) ?>
                                        </span>
                                    </td>

                                    <!-- Period -->
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= $absence['date_debut_formatted'] ?>
                                            <?php if ($absence['date_debut_formatted'] !== $absence['date_fin_formatted']): ?>
                                                <br><span class="text-gray-500">au</span> <?= $absence['date_fin_formatted'] ?>
                                            <?php endif; ?>
                                        </div>
                                    </td>

                                    <!-- Duration -->
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <?= $absence['duree'] ?> jour<?= $absence['duree'] > 1 ? 's' : '' ?>
                                        </div>
                                    </td>

                                    <!-- Status -->
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-<?= $absence['status_color'] ?>-100 text-<?= $absence['status_color'] ?>-800">
                                            <?= htmlspecialchars($absence['statut_formatted']) ?>
                                        </span>
                                    </td>

                                    <!-- Approval History -->
                                    <td class="px-4 py-3">
                                        <div class="text-xs space-y-1">
                                            <?php if (!empty($absence['approval_history']['responsable_approval'])): ?>
                                                <div class="flex items-center">
                                                    <?php if (isset($absence['approval_history']['responsable_approval']['pending'])): ?>
                                                        <i class="fas fa-clock text-yellow-500 mr-1"></i>
                                                        <span class="text-yellow-700">Resp: En attente</span>
                                                    <?php else: ?>
                                                        <i class="fas fa-check text-green-500 mr-1"></i>
                                                        <span class="text-green-700">Resp: Approuvé</span>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (!empty($absence['approval_history']['planificateur_approval'])): ?>
                                                <div class="flex items-center">
                                                    <?php if (isset($absence['approval_history']['planificateur_approval']['pending'])): ?>
                                                        <i class="fas fa-clock text-yellow-500 mr-1"></i>
                                                        <span class="text-yellow-700">Plan: En attente</span>
                                                    <?php else: ?>
                                                        <i class="fas fa-check text-green-500 mr-1"></i>
                                                        <span class="text-green-700">Plan: Approuvé</span>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (!empty($absence['approval_history']['final_decision'])): ?>
                                                <div class="flex items-center">
                                                    <?php if ($absence['approval_history']['final_decision']['status'] === 'approved'): ?>
                                                        <i class="fas fa-check-circle text-green-500 mr-1"></i>
                                                        <span class="text-green-700">Finalisé</span>
                                                    <?php elseif ($absence['approval_history']['final_decision']['status'] === 'rejected'): ?>
                                                        <i class="fas fa-times-circle text-red-500 mr-1"></i>
                                                        <span class="text-red-700">Rejeté</span>
                                                    <?php endif; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </td>

                                    <!-- Actions -->
                                    <td class="px-4 py-3 whitespace-nowrap text-sm">
                                        <button type="button"
                                                onclick="showAbsenceDetails(<?= $absence['id'] ?>)"
                                                class="text-teal-600 hover:text-teal-800 mr-2"
                                                title="Voir les détails complets">
                                            <i class="fas fa-eye"></i>
                                        </button>

                                        <?php if (in_array($absence['statut'], ['en_attente_responsable', 'en_attente_planificateur'])): ?>
                                            <button type="button"
                                                    onclick="showApprovalHistory(<?= $absence['id'] ?>)"
                                                    class="text-blue-600 hover:text-blue-800 mr-2"
                                                    title="Voir l'historique d'approbation">
                                                <i class="fas fa-history"></i>
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>


    </div>

    <!-- Enhanced Details Modal -->
    <div id="absenceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Détails de la demande de congé</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div id="modalContent" class="space-y-6">
                <!-- Basic Information -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-medium text-gray-800 mb-3">Informations générales</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">Référence</p>
                            <p id="modalReference" class="font-medium text-gray-900">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Employé</p>
                            <p id="modalEmployee" class="font-medium text-gray-900">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Type de congé</p>
                            <p id="modalType" class="font-medium text-gray-900">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Statut</p>
                            <p id="modalStatus" class="font-medium text-gray-900">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Date de début</p>
                            <p id="modalStartDate" class="font-medium text-gray-900">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Date de fin</p>
                            <p id="modalEndDate" class="font-medium text-gray-900">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Durée</p>
                            <p id="modalDuration" class="font-medium text-gray-900">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Date de soumission</p>
                            <p id="modalSubmissionDate" class="font-medium text-gray-900">-</p>
                        </div>
                    </div>
                </div>

                <!-- Request Reason -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-medium text-gray-800 mb-2">Motif de la demande</h4>
                    <p id="modalReason" class="text-gray-700">-</p>
                </div>

                <!-- Approval History -->
                <div id="modalApprovalHistory" class="bg-blue-50 p-4 rounded-lg">
                    <h4 class="font-medium text-blue-800 mb-3 flex items-center">
                        <i class="fas fa-history mr-2"></i>
                        Historique des approbations
                    </h4>
                    <div id="approvalTimeline" class="space-y-3">
                        <!-- Timeline will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Rejection Details (if applicable) -->
                <div id="modalRejectionDetails" class="bg-red-50 border border-red-200 p-4 rounded-lg" style="display: none;">
                    <h4 class="font-medium text-red-800 mb-3 flex items-center">
                        <i class="fas fa-times-circle mr-2"></i>
                        Détails du rejet
                    </h4>
                    <div class="space-y-2">
                        <div>
                            <p class="text-sm text-red-600">Rejeté par</p>
                            <p id="modalRejectedBy" class="font-medium text-red-800">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-red-600">Date de rejet</p>
                            <p id="modalRejectionDate" class="font-medium text-red-800">-</p>
                        </div>
                        <div>
                            <p class="text-sm text-red-600">Motif du rejet</p>
                            <p id="modalRejectionReason" class="text-red-700 bg-red-100 p-2 rounded">-</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 flex justify-end">
                <button onclick="closeModal()" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                    Fermer
                </button>
            </div>
        </div>
    </div>

    <!-- Approval History Modal -->
    <div id="approvalHistoryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-lg">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Historique d'approbation</h3>
                <button onclick="closeApprovalHistoryModal()" class="text-gray-400 hover:text-gray-500">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div id="historyContent" class="space-y-4">
                <!-- Content will be populated by JavaScript -->
            </div>

            <div class="mt-6 flex justify-end">
                <button onclick="closeApprovalHistoryModal()" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50">
                    Fermer
                </button>
            </div>
        </div>
    </div>

    <script>
        // Enhanced function to show absence details with approval history
        function showAbsenceDetails(absenceId) {
            // Show loading state
            document.getElementById('modalEmployee').textContent = 'Chargement...';
            document.getElementById('modalType').textContent = 'Chargement...';
            document.getElementById('modalStartDate').textContent = 'Chargement...';
            document.getElementById('modalEndDate').textContent = 'Chargement...';
            document.getElementById('modalDuration').textContent = 'Chargement...';
            document.getElementById('modalStatus').innerHTML = 'Chargement...';
            document.getElementById('modalReason').textContent = 'Chargement...';
            document.getElementById('modalReference').textContent = 'Chargement...';
            document.getElementById('modalSubmissionDate').textContent = 'Chargement...';
            document.getElementById('approvalTimeline').innerHTML = '<p class="text-gray-500">Chargement de l\'historique...</p>';

            // Hide rejection details initially
            document.getElementById('modalRejectionDetails').style.display = 'none';

            // Show the modal
            document.getElementById('absenceModal').classList.remove('hidden');

            // Find the absence data from the current page data
            const absenceData = findAbsenceById(absenceId);
            if (absenceData) {
                populateModalWithData(absenceData);
            } else {
                // Fallback to API call if data not found in page
                fetchAbsenceDetails(absenceId);
            }
        }

        // Function to find absence data by ID from the current page data
        function findAbsenceById(absenceId) {
            // This would need to be populated with the PHP data
            // For now, we'll use the API fallback
            return null;
        }

        // Function to populate modal with absence data
        function populateModalWithData(data) {
            // Basic information
            document.getElementById('modalEmployee').textContent = data.nom_complet || '-';
            document.getElementById('modalType').textContent = data.type_formatted || '-';
            document.getElementById('modalStartDate').textContent = data.date_debut_formatted || '-';
            document.getElementById('modalEndDate').textContent = data.date_fin_formatted || '-';
            document.getElementById('modalDuration').textContent = `${data.duree} jour${data.duree > 1 ? 's' : ''}`;
            document.getElementById('modalReference').textContent = data.reference_demande || '-';
            document.getElementById('modalSubmissionDate').textContent = data.date_demande_formatted || '-';
            document.getElementById('modalReason').textContent = data.motif || 'Aucun motif spécifié';

            // Status with color coding
            const statusElement = document.getElementById('modalStatus');
            statusElement.innerHTML = `<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${data.status_color}-100 text-${data.status_color}-800">${data.statut_formatted}</span>`;

            // Populate approval timeline
            populateApprovalTimeline(data.approval_history);

            // Show rejection details if applicable
            if (data.statut === 'refusee' && data.rejection_details) {
                document.getElementById('modalRejectedBy').textContent = `${data.rejection_details.rejected_by_name} (${data.rejection_details.rejected_by_role})`;
                document.getElementById('modalRejectionDate').textContent = data.rejection_details.rejection_date;
                document.getElementById('modalRejectionReason').textContent = data.rejection_details.rejection_reason;
                document.getElementById('modalRejectionDetails').style.display = 'block';
            }
        }

        // Function to populate approval timeline
        function populateApprovalTimeline(approvalHistory) {
            const timeline = document.getElementById('approvalTimeline');
            timeline.innerHTML = '';

            if (!approvalHistory) {
                timeline.innerHTML = '<p class="text-gray-500">Aucun historique disponible</p>';
                return;
            }

            // Submission
            if (approvalHistory.submission) {
                timeline.appendChild(createTimelineItem(
                    'fas fa-paper-plane',
                    'blue',
                    approvalHistory.submission.action,
                    approvalHistory.submission.by,
                    approvalHistory.submission.date
                ));
            }

            // Responsable approval
            if (approvalHistory.responsable_approval) {
                const isPending = approvalHistory.responsable_approval.pending;
                timeline.appendChild(createTimelineItem(
                    isPending ? 'fas fa-clock' : 'fas fa-check',
                    isPending ? 'yellow' : 'green',
                    approvalHistory.responsable_approval.action,
                    approvalHistory.responsable_approval.by,
                    approvalHistory.responsable_approval.date,
                    isPending
                ));
            }

            // Planificateur approval
            if (approvalHistory.planificateur_approval) {
                const isPending = approvalHistory.planificateur_approval.pending;
                timeline.appendChild(createTimelineItem(
                    isPending ? 'fas fa-clock' : 'fas fa-check',
                    isPending ? 'yellow' : 'green',
                    approvalHistory.planificateur_approval.action,
                    approvalHistory.planificateur_approval.by,
                    approvalHistory.planificateur_approval.date,
                    isPending
                ));
            }

            // Final decision
            if (approvalHistory.final_decision) {
                const isRejected = approvalHistory.final_decision.status === 'rejected';
                timeline.appendChild(createTimelineItem(
                    isRejected ? 'fas fa-times-circle' : 'fas fa-check-circle',
                    isRejected ? 'red' : 'green',
                    approvalHistory.final_decision.action,
                    approvalHistory.final_decision.by,
                    approvalHistory.final_decision.date
                ));
            }
        }

        // Helper function to create timeline items
        function createTimelineItem(icon, color, action, by, date, isPending = false) {
            const item = document.createElement('div');
            item.className = 'flex items-start space-x-3';

            const opacity = isPending ? 'opacity-60' : '';
            item.innerHTML = `
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-${color}-100 rounded-full flex items-center justify-center ${opacity}">
                        <i class="${icon} text-${color}-600 text-sm"></i>
                    </div>
                </div>
                <div class="flex-1 ${opacity}">
                    <p class="text-sm font-medium text-gray-900">${action}</p>
                    <p class="text-sm text-gray-500">${by}</p>
                    <p class="text-xs text-gray-400">${date}</p>
                </div>
            `;

            return item;
        }

        // Fallback function to fetch absence details via API
        function fetchAbsenceDetails(absenceId) {
            const apiUrl = window.location.origin + `/api/absences/${absenceId}`;

            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Erreur lors de la récupération des détails (${response.status})`);
                    }
                    return response.json();
                })
                .then(data => {
                    populateModalWithData(data);
                })
                .catch(error => {
                    console.error('Error fetching absence details:', error);
                    document.getElementById('modalContent').innerHTML = `
                        <div class="text-center py-6">
                            <div class="text-red-500 mb-2"><i class="fas fa-exclamation-circle text-xl"></i></div>
                            <p class="text-gray-700">Une erreur est survenue lors de la récupération des détails.</p>
                            <p class="text-gray-500 text-sm mt-2">Veuillez réessayer plus tard.</p>
                        </div>
                    `;
                });
        }

        // Function to show approval history modal
        function showApprovalHistory(absenceId) {
            // This function can be used to show a simplified approval history
            // For now, we'll just show the main details modal
            showAbsenceDetails(absenceId);
        }

        // Function to close the main modal
        function closeModal() {
            document.getElementById('absenceModal').classList.add('hidden');
        }

        // Function to close the approval history modal
        function closeApprovalHistoryModal() {
            document.getElementById('approvalHistoryModal').classList.add('hidden');
        }

        // Enhanced modal functionality with click-outside support
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('absenceModal');
            const modalContent = modal.querySelector('.bg-white');
            const historyModal = document.getElementById('approvalHistoryModal');
            const historyModalContent = historyModal.querySelector('.bg-white');

            // Click-outside functionality for main modal
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // Prevent modal from closing when clicking inside the modal content
            modalContent.addEventListener('click', function(e) {
                e.stopPropagation();
            });

            // Click-outside functionality for history modal
            historyModal.addEventListener('click', function(e) {
                if (e.target === historyModal) {
                    closeApprovalHistoryModal();
                }
            });

            // Prevent history modal from closing when clicking inside
            historyModalContent.addEventListener('click', function(e) {
                e.stopPropagation();
            });

            // Escape key functionality
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    if (!historyModal.classList.contains('hidden')) {
                        closeApprovalHistoryModal();
                    } else if (!modal.classList.contains('hidden')) {
                        closeModal();
                    }
                }
            });
        });
    </script>
</body>

</html>
