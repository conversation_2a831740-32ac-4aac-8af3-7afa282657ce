<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Absences à venir - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_planificateur.php'; ?>
    
    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Absences à venir</h1>
                    <p class="text-gray-600">Vue d'ensemble des prochaines absences</p>
                </div>
                <div class="flex space-x-2">
                    <button class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                        <i class="fas fa-print mr-1"></i> Imprimer
                    </button>
                    <button class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                        <i class="fas fa-download mr-1"></i> Exporter
                    </button>
                </div>
            </div>
        </header>
        
        <div class="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="card p-4">
                <div class="flex items-center mb-2">
                    <div class="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                    <h3 class="font-medium text-gray-800">Congés approuvés</h3>
                </div>
                <p class="text-sm text-gray-500">Congés validés et planifiés</p>
            </div>
            <div class="card p-4">
                <div class="flex items-center mb-2">
                    <div class="h-3 w-3 rounded-full bg-yellow-500 mr-2"></div>
                    <h3 class="font-medium text-gray-800">Demandes en attente</h3>
                </div>
                <p class="text-sm text-gray-500">Demandes non encore validées</p>
            </div>
            <div class="card p-4">
                <div class="flex items-center mb-2">
                    <div class="h-3 w-3 rounded-full bg-purple-500 mr-2"></div>
                    <h3 class="font-medium text-gray-800">Jours fériés</h3>
                </div>
                <p class="text-sm text-gray-500">Prochains jours fériés</p>
            </div>
        </div>
        
        <!-- Absences à venir -->
        <div class="card mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">Prochaines absences</h2>
                <a href="/planning" class="text-sm text-teal-600 hover:text-teal-800">Voir le planning complet</a>
            </div>
            
            <?php if (empty($upcomingAbsences)): ?>
                <div class="text-center py-8">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                        <i class="fas fa-calendar-check text-green-600 text-xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Aucune absence prévue</h3>
                    <p class="text-gray-500">Il n'y a pas d'absences prévues dans les prochains jours.</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employé</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($upcomingAbsences as $absence): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($absence['nom_complet']) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?= htmlspecialchars($absence['type_formatted']) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($absence['date_debut'] === $absence['date_fin']): ?>
                                            <div class="text-sm text-gray-900"><?= date('d/m/Y', strtotime($absence['date_debut'])) ?></div>
                                        <?php else: ?>
                                            <div class="text-sm text-gray-900">
                                                <?= date('d/m/Y', strtotime($absence['date_debut'])) ?> - 
                                                <?= date('d/m/Y', strtotime($absence['date_fin'])) ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?= $absence['duree'] ?> jour<?= $absence['duree'] > 1 ? 's' : '' ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($absence['statut'] === 'acceptée'): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                <?= $absence['statut_formatted'] ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                <?= $absence['statut_formatted'] ?>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Jours fériés à venir -->
        <div class="card">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-800">Prochains jours fériés</h2>
                <a href="/jours-feries" class="text-sm text-teal-600 hover:text-teal-800">Voir tous les jours fériés</a>
            </div>
            
            <?php if (empty($upcomingHolidays)): ?>
                <div class="text-center py-8">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-purple-100 mb-4">
                        <i class="fas fa-calendar text-purple-600 text-xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Aucun jour férié à venir</h3>
                    <p class="text-gray-500">Il n'y a pas de jours fériés prévus prochainement.</p>
                </div>
            <?php else: ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nom</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pays</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($upcomingHolidays as $holiday): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($holiday['name']) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?= date('d/m/Y', strtotime($holiday['date'])) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900"><?= htmlspecialchars($holiday['pays']) ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($holiday['is_recurring']): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Récurrent
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                Unique
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script>
        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mainContent = document.getElementById('main-content');
            
            // Add any additional JavaScript functionality here
        });
    </script>
</body>
</html>
