<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <title>Planning - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
</head>

<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_planificateur.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Planning Global</h1>
                    <p class="text-gray-600">Vue d'ensemble de tous les congés</p>
                </div>
                <div class="flex space-x-2">
                    <button
                        class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                        <i class="fas fa-print mr-1"></i> Imprimer
                    </button>
                    <button
                        class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                        <i class="fas fa-download mr-1"></i> Exporter
                    </button>
                </div>
            </div>
        </header>

        <div class="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="card p-4">
                <div class="flex items-center mb-2">
                    <div class="h-3 w-3 rounded-full bg-green-500 mr-2"></div>
                    <h3 class="font-medium text-gray-800">Congés</h3>
                </div>
                <p class="text-sm text-gray-500">Congés payés, RTT, etc.</p>
            </div>
            <div class="card p-4">
                <div class="flex items-center mb-2">
                    <div class="h-3 w-3 rounded-full bg-blue-500 mr-2"></div>
                    <h3 class="font-medium text-gray-800">Formations</h3>
                </div>
                <p class="text-sm text-gray-500">Formations, séminaires</p>
            </div>
            <div class="card p-4">
                <div class="flex items-center mb-2">
                    <div class="h-3 w-3 rounded-full bg-red-500 mr-2"></div>
                    <h3 class="font-medium text-gray-800">Absences maladie</h3>
                </div>
                <p class="text-sm text-gray-500">Arrêts maladie, accidents</p>
            </div>
            <div class="card p-4">
                <div class="flex items-center mb-2">
                    <div class="h-3 w-3 rounded-full bg-yellow-500 mr-2"></div>
                    <h3 class="font-medium text-gray-800">Jours fériés</h3>
                </div>
                <p class="text-sm text-gray-500">Fériés et événements spéciaux</p>
            </div>
        </div>

        <div class="card">
            <div class="flex flex-wrap items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-800">Calendrier</h2>
                <div class="flex space-x-2 mt-2 sm:mt-0">
                    <form id="filterForm" method="get" class="flex space-x-2">
                        <select id="departmentFilter" name="department"
                            class="text-sm border-gray-300 rounded-md focus:ring-teal-500 focus:border-teal-500">
                            <?php foreach ($planningData['departments'] as $value => $label): ?>
                                <option value="<?= $value ?>" <?= $value === $planningData['currentDepartment'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <input type="month" id="monthPicker" name="month"
                            value="<?= htmlspecialchars($planningData['currentMonth']) ?>"
                            class="text-sm border-gray-300 rounded-md focus:ring-teal-500 focus:border-teal-500">
                        <button type="submit"
                            class="bg-teal-600 text-white px-3 py-1 rounded-md text-sm hover:bg-teal-700">
                            <i class="fas fa-filter mr-1"></i> Filtrer
                        </button>
                    </form>
                </div>
            </div>

            <div id="calendar" class="min-h-[600px]"></div>
        </div>
    </div>

    <!-- Event Details Modal -->
    <div id="eventModal"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 hidden flex items-center justify-center p-4 z-50">
        <div class="modal-card max-w-md w-full">
            <div class="bg-teal-600 px-4 py-3 flex items-center justify-between">
                <h3 class="text-lg font-medium text-white" id="modalTitle">Détails de l'événement</h3>
                <button type="button" class="text-white hover:text-gray-200" onclick="closeEventModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <p class="text-sm text-gray-500">Employé</p>
                    <p class="font-medium" id="modalEmployee">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm text-gray-500">Type</p>
                    <p class="font-medium" id="modalType">-</p>
                </div>
                <div class="mb-4">
                    <p class="text-sm text-gray-500">Période</p>
                    <p class="font-medium" id="modalPeriod">-</p>
                </div>
                <div class="mt-5 text-right">
                    <button type="button"
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700"
                        onclick="closeEventModal()">
                        Fermer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
    // Sidebar toggle functionality
    document.addEventListener('DOMContentLoaded', function() {
        const mainContent = document.getElementById('main-content');

        // Filter form submission - prevent page reload
        document.getElementById('filterForm').addEventListener('submit', function(e) {
            e.preventDefault(); // Prevent default form submission

            try {
                // Get form data
                const formData = new FormData(this);
                const params = new URLSearchParams();

                // Build URL parameters
                for (let [key, value] of formData.entries()) {
                    if (value) {
                        params.append(key, value);
                    }
                }

                // Update URL without page reload
                const url = '/planning' + (params.toString() ? '?' + params.toString() : '');
                history.pushState({}, '', url);

                // Fetch filtered data via AJAX
                fetchFilteredPlanning(formData);
            } catch (error) {
                console.error('Error in filter form submission:', error);
                // Fallback to normal form submission
                this.submit();
            }
        });

        // Function to fetch filtered planning data via AJAX
        function fetchFilteredPlanning(formData) {
            try {
                formData.append('ajax', '1');

                fetch('/planning', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.text();
                })
                .then(html => {
                    // Update calendar content
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newCalendar = doc.querySelector('#calendar-container');
                    const currentCalendar = document.querySelector('#calendar-container');

                    if (newCalendar && currentCalendar) {
                        currentCalendar.innerHTML = newCalendar.innerHTML;
                    }
                })
                .catch(error => {
                    console.error('Error fetching filtered planning:', error);
                    // Fallback to page reload
                    document.getElementById('filterForm').submit();
                });
            } catch (error) {
                console.error('Error in fetchFilteredPlanning:', error);
                // Fallback to page reload
                document.getElementById('filterForm').submit();
            }
        }

        // Initialize FullCalendar
        const calendarEl = document.getElementById('calendar');
        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'fr',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,listWeek'
            },
            buttonText: {
                today: 'Aujourd\'hui',
                month: 'Mois',
                week: 'Semaine',
                list: 'Liste'
            },
            events: getEvents(),
            eventClick: function(info) {
                showEventDetails(info.event);
            },
            eventTextColor: '#fff',
            businessHours: {
                daysOfWeek: [1, 2, 3, 4, 5], // Monday - Friday
                startTime: '09:00',
                endTime: '18:00',
            },
            firstDay: 1, // Monday as first day
            height: 'auto',
        });

        calendar.render();

        // Helper function to get event data
        function getEvents() {
            // This would normally be an API call to get events
            const events = <?= json_encode($planningData['events']) ?>;
            const holidays = <?= json_encode($planningData['holidays']) ?>;

            // Transform events to FullCalendar format
            const formattedEvents = [];

            // Add regular events
            events.forEach(event => {
                let color;
                switch (event.type) {
                    case 'vacation':
                        color = '#10b981'; // green-500
                        break;
                    case 'training':
                        color = '#3b82f6'; // blue-500
                        break;
                    case 'sick':
                        color = '#ef4444'; // red-500
                        break;
                    default:
                        color = '#6b7280'; // gray-500
                }

                formattedEvents.push({
                    id: event.id,
                    title: event.title,
                    start: event.start,
                    end: new Date(new Date(event.end).getTime() +
                    86400000), // Add one day because FullCalendar's end date is exclusive
                    backgroundColor: color,
                    borderColor: color,
                    textColor: '#ffffff',
                    extendedProps: {
                        type: event.type,
                        userId: event.userId
                    }
                });
            });

            // Add holidays
            holidays.forEach(holiday => {
                formattedEvents.push({
                    id: 'holiday-' + holiday.id,
                    title: holiday.title,
                    start: holiday.date,
                    allDay: true,
                    backgroundColor: '#eab308', // yellow-500
                    borderColor: '#eab308',
                    textColor: '#ffffff',
                    extendedProps: {
                        type: 'holiday'
                    }
                });
            });

            return formattedEvents;
        }
    });

    // Modal functionality for event details
    function showEventDetails(event) {
        const type = event.extendedProps.type;
        let typeText;

        switch (type) {
            case 'vacation':
                typeText = 'Congé';
                break;
            case 'training':
                typeText = 'Formation';
                break;
            case 'sick':
                typeText = 'Maladie';
                break;
            case 'holiday':
                typeText = 'Jour férié';
                break;
            default:
                typeText = 'Autre';
        }

        // Get employee name from event title
        let employee = '';
        if (type !== 'holiday') {
            // Extract employee name from title (format is usually "Type - Employee Name")
            const titleParts = event.title.split(' - ');
            if (titleParts.length > 1) {
                employee = titleParts[1];
            }
        } else {
            employee = 'Tous les employés';
        }

        // Format dates
        let period = '';
        const startDate = new Date(event.start);
        const startFormatted = startDate.toLocaleDateString('fr-FR');

        if (event.end) {
            // Subtract one day from the end date because FullCalendar's end date is exclusive
            const endDate = new Date(new Date(event.end).getTime() - 86400000);
            const endFormatted = endDate.toLocaleDateString('fr-FR');
            period = `${startFormatted} au ${endFormatted}`;
        } else {
            period = startFormatted;
        }

        // Update modal content
        document.getElementById('modalTitle').textContent = event.title;
        document.getElementById('modalEmployee').textContent = employee;
        document.getElementById('modalType').textContent = typeText;
        document.getElementById('modalPeriod').textContent = period;

        // Show modal
        document.getElementById('eventModal').classList.remove('hidden');
    }

    function closeEventModal() {
        document.getElementById('eventModal').classList.add('hidden');
    }

    // Enhanced modal functionality with click-outside support
    document.addEventListener('DOMContentLoaded', function() {
        const modal = document.getElementById('eventModal');

        // Click-outside functionality is now handled by the global modal manager
        // The modal manager will automatically detect this modal and add the functionality

        // Escape key functionality (also handled by modal manager, but keeping for compatibility)
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                closeEventModal();
            }
        });
    });
    </script>
</body>

</html>