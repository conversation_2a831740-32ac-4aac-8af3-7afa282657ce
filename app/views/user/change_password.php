<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Changer de mot de passe - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
    <?php include_once APP_PATH . '/views/shared/sidebar_employe.php'; ?>

    <div class="ml-[240px] p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Changer de mot de passe</h1>
                    <p class="text-gray-600">Sécurisez votre compte avec un nouveau mot de passe</p>
                </div>
                <a href="/profil" class="text-purple-600 hover:text-purple-800 flex items-center gap-2">
                    <i class="fas fa-arrow-left"></i> Retour au profil
                </a>
            </div>
        </header>

        <div class="max-w-md mx-auto">
            <div class="bg-white p-6 rounded-lg shadow-sm">
                <?php if (isset($error)): ?>
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
                        <p><?= htmlspecialchars($error) ?></p>
                    </div>
                <?php endif; ?>

                <?php if (isset($success)): ?>
                    <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6">
                        <p><?= htmlspecialchars($success) ?></p>
                    </div>
                <?php endif; ?>

                <form action="/change-password" method="POST">
                    <div class="mb-6">
                        <label for="current_password" class="block text-sm font-medium text-gray-700 mb-1">Mot de passe actuel *</label>
                        <input type="password" id="current_password" name="current_password" class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" required>
                    </div>

                    <div class="mb-6">
                        <label for="new_password" class="block text-sm font-medium text-gray-700 mb-1">Nouveau mot de passe *</label>
                        <input type="password" id="new_password" name="new_password" class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" required>

                        <!-- Password Requirements -->
                        <div class="mt-2 text-xs">
                            <p class="text-gray-600 mb-2">Le mot de passe doit contenir :</p>
                            <ul class="space-y-1">
                                <li id="length-req" class="flex items-center text-red-500">
                                    <i class="fas fa-times mr-2"></i>
                                    <span>Au moins 8 caractères</span>
                                </li>
                                <li id="letter-req" class="flex items-center text-red-500">
                                    <i class="fas fa-times mr-2"></i>
                                    <span>Lettres (majuscules et minuscules)</span>
                                </li>
                                <li id="number-req" class="flex items-center text-red-500">
                                    <i class="fas fa-times mr-2"></i>
                                    <span>Au moins un chiffre</span>
                                </li>
                                <li id="special-req" class="flex items-center text-red-500">
                                    <i class="fas fa-times mr-2"></i>
                                    <span>Au moins un caractère spécial</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="mb-6">
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">Confirmez le nouveau mot de passe *</label>
                        <input type="password" id="confirm_password" name="confirm_password" class="w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" required>
                        <div id="password-match-message" class="mt-1 text-xs hidden">
                            <span class="text-green-600">
                                <i class="fas fa-check mr-1"></i>
                                Les mots de passe correspondent
                            </span>
                        </div>
                    </div>

                    <div class="flex justify-end gap-4">
                        <a href="/profil" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">Annuler</a>
                        <button type="submit" id="submit-btn" class="px-4 py-2 bg-gray-400 text-white rounded-md cursor-not-allowed" disabled>Changer le mot de passe</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const newPassword = document.getElementById('new_password');
            const confirmPassword = document.getElementById('confirm_password');
            const submitBtn = document.getElementById('submit-btn');
            const passwordMatchMessage = document.getElementById('password-match-message');

            // Password requirement elements
            const lengthReq = document.getElementById('length-req');
            const letterReq = document.getElementById('letter-req');
            const numberReq = document.getElementById('number-req');
            const specialReq = document.getElementById('special-req');

            function validatePasswordRequirements(password) {
                const requirements = {
                    length: password.length >= 8,
                    letter: /[a-zA-Z]/.test(password) && /[a-z]/.test(password) && /[A-Z]/.test(password),
                    number: /\d/.test(password),
                    special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
                };

                // Update visual feedback for each requirement individually
                updateRequirement(lengthReq, requirements.length);
                updateRequirement(letterReq, requirements.letter);
                updateRequirement(numberReq, requirements.number);
                updateRequirement(specialReq, requirements.special);

                // Update password field border only when ALL requirements are met
                const allRequirementsMet = Object.values(requirements).every(req => req);
                if (allRequirementsMet) {
                    newPassword.classList.remove('border-red-500');
                    newPassword.classList.add('border-green-500');
                } else {
                    newPassword.classList.remove('border-green-500');
                    newPassword.classList.add('border-red-500');
                }

                return allRequirementsMet;
            }

            function updateRequirement(element, isValid) {
                const icon = element.querySelector('i');
                if (isValid) {
                    element.classList.remove('text-red-500');
                    element.classList.add('text-green-500');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-check');
                } else {
                    element.classList.remove('text-green-500');
                    element.classList.add('text-red-500');
                    icon.classList.remove('fa-check');
                    icon.classList.add('fa-times');
                }
            }

            function validatePasswordMatch() {
                const passwordsMatch = newPassword.value === confirmPassword.value && confirmPassword.value !== '';

                if (passwordsMatch) {
                    confirmPassword.classList.remove('border-red-500');
                    confirmPassword.classList.add('border-green-500');
                    passwordMatchMessage.classList.remove('hidden');
                } else if (confirmPassword.value !== '') {
                    confirmPassword.classList.remove('border-green-500');
                    confirmPassword.classList.add('border-red-500');
                    passwordMatchMessage.classList.add('hidden');
                } else {
                    confirmPassword.classList.remove('border-red-500', 'border-green-500');
                    passwordMatchMessage.classList.add('hidden');
                }

                return passwordsMatch;
            }

            function updateSubmitButton() {
                const passwordValid = validatePasswordRequirements(newPassword.value);
                const passwordsMatch = validatePasswordMatch();

                if (passwordValid && passwordsMatch) {
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('bg-gray-400', 'cursor-not-allowed');
                    submitBtn.classList.add('bg-purple-600', 'hover:bg-purple-700');
                } else {
                    submitBtn.disabled = true;
                    submitBtn.classList.remove('bg-purple-600', 'hover:bg-purple-700');
                    submitBtn.classList.add('bg-gray-400', 'cursor-not-allowed');
                }
            }

            // Event listeners
            newPassword.addEventListener('input', function() {
                validatePasswordRequirements(this.value);
                updateSubmitButton();
            });

            confirmPassword.addEventListener('input', function() {
                validatePasswordMatch();
                updateSubmitButton();
            });

            // Initial validation
            updateSubmitButton();
        });
    </script>
</body>
</html>
