<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon profil - Gestion des Congés</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/style.css">
</head>
<body class="bg-gray-50">
 <?php
if (isset($_SESSION['role'])) {
    switch ($_SESSION['role']) {
        case 'admin':
            include_once APP_PATH . '/views/shared/sidebar_admin.php';
            break;
        case 'responsable':
            include_once APP_PATH . '/views/shared/sidebar_responsable.php';
            break;
        case 'planificateur':
            include_once APP_PATH . '/views/shared/sidebar_planificateur.php';
            break;
        case 'employe':
        default:
            include_once APP_PATH . '/views/shared/sidebar_employe.php';
            break;
    }
} else {
    // Default to employee sidebar if no role is set
    include_once APP_PATH . '/views/shared/sidebar_employe.php';
}
?>

    <div class="md:ml-[240px] p-4 md:p-6 transition-all duration-200" id="main-content">
        <header class="mb-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 class="text-xl sm:text-2xl font-bold text-gray-800">Mon Profil</h1>
                </div>
                <div class="user-actions flex flex-wrap gap-2 w-full sm:w-auto">
                    <button class="btn btn-secondary px-3 sm:px-4 py-2 bg-gray-200 rounded-md hover:bg-gray-300 text-gray-700 text-sm sm:text-base whitespace-nowrap flex-1 sm:flex-none" onclick="openModal('change-password-modal')">
                        <i class="fas fa-key"></i> Changer mot de passe
                    </button>
                    <?php if (Auth::hasPermission('edit_profile_information')): ?>
                    <button class="btn btn-primary px-3 sm:px-4 py-2 bg-blue-500 rounded-md hover:bg-blue-600 text-white text-sm sm:text-base whitespace-nowrap flex-1 sm:flex-none" onclick="openModal('edit-profile-modal')">
                        <i class="fas fa-edit"></i> Modifier profil
                    </button>
                    <?php endif; ?>
                </div>
            </div>
        </header>

        <?php if (isset($error)): ?>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6">
                <p><?= htmlspecialchars($error) ?></p>
            </div>
        <?php endif; ?>

        <?php if (isset($success)): ?>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6">
                <p><?= htmlspecialchars($success) ?></p>
            </div>
        <?php endif; ?>

        <div class="profile-sections grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            <!-- Section 1: Informations personnelles -->
            <div class="profile-section bg-white p-6 rounded-lg shadow-sm <?php echo !Auth::hasPermission('edit_profile_information') ? 'border-l-4 border-gray-400' : ''; ?>">
                <div class="section-header flex justify-between items-center mb-4 pb-2 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800">
                        <i class="fas fa-user"></i> Informations personnelles
                        <?php if (!Auth::hasPermission('edit_profile_information')): ?>
                        <span class="text-xs text-gray-500 ml-2"><i class="fas fa-lock"></i> </span>
                        <?php endif; ?>
                    </h2>
                </div>
                <div class="profile-photo flex flex-col items-center mb-6">
                    <img src="<?= !empty($user['photo_profil']) ? htmlspecialchars($user['photo_profil']) : '/assets/images/default-profile.png' ?>" alt="Photo de profil" id="profile-pic" class="w-[120px] h-[120px] rounded-full object-cover mb-2 border-3 border-gray-200">
                    <?php if (Auth::hasPermission('edit_profile_information')): ?>
                    <button class="upload-btn bg-gray-200 text-gray-700 border-none px-2 py-1 rounded-md text-sm cursor-pointer" onclick="openModal('upload-photo-modal')">
                        <i class="fas fa-camera"></i> Changer photo
                    </button>
                    <?php else: ?>
                    <div class="text-xs text-gray-500 mt-2">
                        <i class="fas fa-lock"></i>
                    </div>
                    <?php endif; ?>
                </div>
                <div class="profile-info grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                    <div class="info-item">
                        <div class="info-label text-gray-600 text-sm font-medium mb-1">Nom complet</div>
                        <div class="info-value font-semibold"><?= htmlspecialchars($user['nom'] . ' ' . $user['prenom']) ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label text-gray-600 text-sm font-medium mb-1">Matricule RH</div>
                        <div class="info-value font-semibold"><?= htmlspecialchars($user['matricule_rh']) ?></div>
                    </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label text-gray-600 text-sm font-medium mb-1">Email </div>
                        <div class="info-value font-semibold break-words overflow-hidden"><?= htmlspecialchars($user['email'] ?? '') ?></div>
                </div>
            </div>

            <!-- Section 2: Informations professionnelles -->
            <div class="profile-section bg-white p-6 rounded-lg shadow-sm <?php echo !Auth::hasPermission('edit_profile_information') ? 'border-l-4 border-gray-400' : ''; ?>">
                <div class="section-header flex justify-between items-center mb-4 pb-2 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800">
                        <i class="fas fa-briefcase"></i> Informations professionnelles
                        <?php if (!Auth::hasPermission('edit_profile_information')): ?>
                        <span class="text-xs text-gray-500 ml-2"><i class="fas fa-lock"></i> </span>
                        <?php endif; ?>
                    </h2>
                </div>
                <div class="profile-info grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                    <div class="info-item">
                        <div class="info-label text-gray-600 text-sm font-medium mb-1">Poste occupé</div>
                        <div class="info-value font-semibold"><?= htmlspecialchars($user['poste']) ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label text-gray-600 text-sm font-medium mb-1">Département</div>
                        <div class="info-value font-semibold"><?= htmlspecialchars($user['departement']) ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label text-gray-600 text-sm font-medium mb-1">Activité</div>
                        <div class="info-value font-semibold"><?= htmlspecialchars($user['activite'] ?: 'Non spécifiée') ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label text-gray-600 text-sm font-medium mb-1">Solde congé payé</div>
                        <div class="info-value font-semibold">
                            <?php if (isset($user['solde_conge_paye']) && $user['solde_conge_paye'] !== 'N/A'): ?>
                                <?= htmlspecialchars($user['solde_conge_paye']) ?> jour<?= $user['solde_conge_paye'] > 1 ? 's' : '' ?>
                            <?php else: ?>
                                <span class="text-gray-500">Non disponible</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Section 3: Documents RH -->
            <div class="profile-section bg-white p-6 rounded-lg shadow-sm">
                <div class="section-header flex justify-between items-center mb-4 pb-2 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800"><i class="fas fa-file-alt"></i> Documents RH</h2>
                </div>
                <div class="documents-list">
                    <div class="document-item flex justify-between items-center py-3 border-b border-gray-200">
                        <div class="document-info">
                            <div class="document-name font-medium">Attestation de travail</div>
                        </div>
                        <div class="document-actions">
                            <button class="btn btn-primary text-sm bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md" onclick="openDocumentModal('work-certificate')">
                                Demander
                            </button>
                        </div>
                    </div>
                    <div class="document-item flex justify-between items-center py-3 border-b border-gray-200">
                        <div class="document-info">
                            <div class="document-name font-medium">Fiche de paie</div>
                        </div>
                        <div class="document-actions">
                            <button class="btn btn-primary text-sm bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md" onclick="openDocumentModal('payslip')">
                                Demander
                            </button>
                        </div>
                    </div>
                    <div class="document-item flex justify-between items-center py-3">
                        <div class="document-info">
                            <div class="document-name font-medium">Attestation de salaire</div>
                        </div>
                        <div class="document-actions">
                            <button class="btn btn-primary text-sm bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded-md" onclick="openDocumentModal('salary-certificate')">
                                Demander
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <?php if (Auth::hasPermission('edit_profile_information')): ?>
    <!-- Upload Photo Modal -->
    <div class="modal hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="upload-photo-modal">
        <div class="modal-content bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="modal-header p-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Changer la photo de profil</h3>
                <button class="close-btn text-gray-500 hover:text-gray-800 text-2xl" onclick="closeModal('upload-photo-modal')">&times;</button>
            </div>
            <form id="photo-upload-form" action="/user/uploadPhoto" method="POST" enctype="multipart/form-data">
                <div class="modal-body p-4">
                    <div id="photo-upload-error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 hidden"></div>
                    <div class="form-group mb-4">
                        <label for="photo-upload" class="block text-sm font-medium text-gray-700 mb-1">Sélectionner une image</label>
                        <input type="file" id="photo-upload" name="photo" class="w-full border border-gray-300 rounded p-2" accept="image/jpeg,image/png,image/gif" required>
                        <p class="text-xs text-gray-500 mt-1">Formats acceptés: JPG, PNG, GIF. Taille max: 5 Mo</p>
                    </div>
                    <div class="preview-area text-center my-4">
                        <img id="photo-preview" src="<?= !empty($user['photo_profil']) ? htmlspecialchars($user['photo_profil']) : '/assets/images/default-profile.png' ?>" class="w-[150px] h-[150px] rounded-full object-cover mx-auto border-3 border-gray-200">
                    </div>
                </div>
                <div class="modal-footer p-4 border-t border-gray-200 flex justify-end space-x-2">
                    <button type="button" class="btn btn-secondary bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300" onclick="closeModal('upload-photo-modal')">Annuler</button>
                    <button type="submit" class="btn btn-primary bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Profile Modal -->
    <div class="modal hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="edit-profile-modal">
        <div class="modal-content bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="modal-header p-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Modifier le profil</h3>
                <button class="close-btn text-gray-500 hover:text-gray-800 text-2xl" onclick="closeModal('edit-profile-modal')">&times;</button>
            </div>
            <form id="edit-profile-form" action="/user/update" method="POST">
                <div class="modal-body p-4">
                    <div id="edit-profile-error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 hidden"></div>

                    <div class="form-group mb-4">
                        <label for="edit-nom" class="block text-sm font-medium text-gray-700 mb-1">Nom</label>
                        <input type="text" id="edit-nom" name="nom" class="w-full border border-gray-300 rounded p-2" value="<?= htmlspecialchars($user['nom']) ?>" required>
                    </div>

                    <div class="form-group mb-4">
                        <label for="edit-prenom" class="block text-sm font-medium text-gray-700 mb-1">Prénom</label>
                        <input type="text" id="edit-prenom" name="prenom" class="w-full border border-gray-300 rounded p-2" value="<?= htmlspecialchars($user['prenom']) ?>" required>
                    </div>

                    <div class="form-group mb-4">
                        <label for="edit-email" class="block text-sm font-medium text-gray-700 mb-1">Email professionnel</label>
                        <input type="email" id="edit-email" name="email" class="w-full border border-gray-300 rounded p-2" value="<?= htmlspecialchars($user['email']) ?>" required>
                    </div>

                    <div class="form-group mb-4">
                        <label for="edit-phone" class="block text-sm font-medium text-gray-700 mb-1">Téléphone</label>
                        <input type="tel" id="edit-phone" name="telephone" class="w-full border border-gray-300 rounded p-2" value="<?= htmlspecialchars($user['telephone']) ?>">
                    </div>
                </div>
                <div class="modal-footer p-4 border-t border-gray-200 flex justify-end space-x-2">
                    <button type="button" class="btn btn-secondary bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300" onclick="closeModal('edit-profile-modal')">Annuler</button>
                    <button type="submit" class="btn btn-primary bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>
    <?php endif; ?>

    <!-- Change Password Modal -->
    <div class="modal hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="change-password-modal">
        <div class="modal-content bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="modal-header p-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Changer le mot de passe</h3>
                <button class="close-btn text-gray-500 hover:text-gray-800 text-2xl" onclick="closeModal('change-password-modal')">&times;</button>
            </div>
            <form id="password-change-form" action="/user/changePassword" method="POST">
                <div class="modal-body p-4">
                    <div id="password-change-error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 hidden"></div>
                    <div id="password-change-success" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4 hidden"></div>

                    <div class="form-group mb-4">
                        <label for="current-password" class="block text-sm font-medium text-gray-700 mb-1">Mot de passe actuel</label>
                        <input type="password" id="current-password" name="current_password" class="w-full border border-gray-300 rounded p-2" required>
                    </div>
                    <div class="form-group mb-4">
                        <label for="new-password" class="block text-sm font-medium text-gray-700 mb-1">Nouveau mot de passe</label>
                        <input type="password" id="new-password" name="new_password" class="w-full border border-gray-300 rounded p-2" required>

                        <!-- Password Requirements -->
                        <div class="mt-2 text-xs">
                            <p class="text-gray-600 mb-2">Le mot de passe doit contenir :</p>
                            <ul class="space-y-1">
                                <li id="modal-length-req" class="flex items-center text-red-500">
                                    <i class="fas fa-times mr-2"></i>
                                    <span>Au moins 8 caractères</span>
                                </li>
                                <li id="modal-letter-req" class="flex items-center text-red-500">
                                    <i class="fas fa-times mr-2"></i>
                                    <span>Lettres (majuscules et minuscules)</span>
                                </li>
                                <li id="modal-number-req" class="flex items-center text-red-500">
                                    <i class="fas fa-times mr-2"></i>
                                    <span>Au moins un chiffre</span>
                                </li>
                                <li id="modal-special-req" class="flex items-center text-red-500">
                                    <i class="fas fa-times mr-2"></i>
                                    <span>Au moins un caractère spécial</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="form-group mb-4">
                        <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-1">Confirmer le nouveau mot de passe</label>
                        <input type="password" id="confirm-password" name="confirm_password" class="w-full border border-gray-300 rounded p-2" required>
                        <div id="modal-password-match-message" class="mt-1 text-xs hidden">
                            <span class="text-green-600">
                                <i class="fas fa-check mr-1"></i>
                                Les mots de passe correspondent
                            </span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer p-4 border-t border-gray-200 flex justify-end space-x-2">
                    <button type="button" class="btn btn-secondary bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300" onclick="closeModal('change-password-modal')">Annuler</button>
                    <button type="submit" id="modal-submit-btn" class="btn btn-primary bg-gray-400 text-white px-4 py-2 rounded cursor-not-allowed" disabled>Changer</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Document Request Modal (Generic) -->
    <div class="modal hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="document-request-modal">
        <div class="modal-content bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="modal-header p-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800" id="document-modal-title">Demande de document</h3>
                <button class="close-btn text-gray-500 hover:text-gray-800 text-2xl" onclick="closeModal('document-request-modal')">&times;</button>
            </div>
            <div class="modal-body p-4">
                <div class="request-summary bg-gray-100 p-4 rounded mb-4">
                    <h4 class="text-md font-semibold text-gray-800 mb-2">Résumé de la demande</h4>
                    <p class="mb-1"><strong>Type de document:</strong> <span id="document-type-summary"></span></p>
                    <p><strong>Date de demande:</strong> <span id="document-date-summary"></span></p>
                </div>
            </div>
            <div class="modal-footer p-4 border-t border-gray-200 flex justify-end space-x-2">
                <button class="btn btn-secondary bg-gray-200 text-gray-700 px-4 py-2 rounded hover:bg-gray-300" onclick="closeModal('document-request-modal')">Annuler</button>
                <button class="btn btn-primary bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600" onclick="submitDocumentRequest()">Confirmer la demande</button>
            </div>
        </div>
    </div>

    <!-- Success Modal -->
    <div class="modal hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="success-modal">
        <div class="modal-content bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="modal-header p-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-green-600 flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    Demande envoyée
                </h3>
                <button class="close-btn text-gray-500 hover:text-gray-800 text-2xl" onclick="closeModal('success-modal')">&times;</button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center">
                    <div class="bg-green-100 border border-green-300 rounded-lg p-4 mb-4">
                        <i class="fas fa-check-circle text-green-500 text-4xl mb-3"></i>
                        <p class="text-gray-700" id="success-message"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-4 border-t border-gray-200 flex justify-center">
                <button class="btn btn-primary bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600" onclick="closeModal('success-modal')">OK</button>
            </div>
        </div>
    </div>

    <!-- Error Modal -->
    <div class="modal hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="error-modal">
        <div class="modal-content bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="modal-header p-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-red-600 flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Erreur
                </h3>
                <button class="close-btn text-gray-500 hover:text-gray-800 text-2xl" onclick="closeModal('error-modal')">&times;</button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center">
                    <div class="bg-red-100 border border-red-300 rounded-lg p-4 mb-4">
                        <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-3"></i>
                        <p class="text-gray-700" id="error-message"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer p-4 border-t border-gray-200 flex justify-center">
                <button class="btn btn-primary bg-red-500 text-white px-6 py-2 rounded hover:bg-red-600" onclick="closeModal('error-modal')">OK</button>
            </div>
        </div>
    </div>



    <script src="/assets/js/profile.js">
    </script>
</body>
</html>
