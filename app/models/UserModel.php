<?php
// User Model

class UserModel extends Model {

    public function getUserByLogin($login) {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE login = ?");
        $stmt->execute([$login]);
        return $stmt->fetch();
    }

    public function getUserByEmail($email) {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetch();
    }

    /**
     * Get user by email or login (for backward compatibility during transition)
     * Prioritizes email lookup first, then falls back to login
     */
    public function getUserByEmailOrLogin($identifier) {
        // First try to get user by email (primary method)
        $user = $this->getUserByEmail($identifier);

        // If not found, try login field for backward compatibility
        // This supports existing users who might still use old login identifiers
        if (!$user) {
            $user = $this->getUserByLogin($identifier);
        }

        return $user;
    }

    /**
     * Check if email already exists in the database
     * Used for validation during user creation/editing
     */
    public function emailExists($email, $excludeUserId = null) {
        $query = "SELECT id FROM users WHERE email = ?";
        $params = [$email];

        if ($excludeUserId) {
            $query .= " AND id != ?";
            $params[] = $excludeUserId;
        }

        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        return $stmt->fetch() !== false;
    }

    /**
     * Check if login already exists in the database
     * Used for validation during user creation/editing
     */
    public function loginExists($login, $excludeUserId = null) {
        $query = "SELECT id FROM users WHERE login = ?";
        $params = [$login];

        if ($excludeUserId) {
            $query .= " AND id != ?";
            $params[] = $excludeUserId;
        }

        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        return $stmt->fetch() !== false;
    }

    public function getUserById($id) {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }

    public function getUserByMatricule($matricule) {
        $stmt = $this->db->prepare("SELECT * FROM users WHERE matricule_rh = ?");
        $stmt->execute([$matricule]);
        return $stmt->fetch();
    }

    /**
     * Get complete user profile data with specific formatting
     *
     * @param int $userId - The user ID to fetch
     * @return array|bool - User data array or false on failure
     */
    public function getUserProfile($userId) {
        try {
            $stmt = $this->db->prepare("
                SELECT
                    id, matricule_rh, login, email, nom, prenom,
                    role, departement, photo_profil, date_entree,
                    date_creation, telephone, poste, activite
                FROM users
                WHERE id = ? AND actif = 1
            ");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            if ($user) {
                // Format dates for display
                if (!empty($user['date_entree'])) {
                    $date = new DateTime($user['date_entree']);
                    $user['date_entree_formatted'] = $date->format('d/m/Y');
                } else {
                    $user['date_entree_formatted'] = 'Non définie';
                }

                if (!empty($user['date_creation'])) {
                    $date = new DateTime($user['date_creation']);
                    $user['date_creation_formatted'] = $date->format('d/m/Y');
                }

                // Set default values for potentially null fields
                $user['telephone'] = $user['telephone'] ?? 'Non renseigné';
                $user['poste'] = $user['poste'] ?? 'Non défini';
                $user['matricule'] = $user['matricule_rh']; // For template consistency

                // Ensure photo_profil is properly set
                if (empty($user['photo_profil'])) {
                    $user['photo_profil'] = '/assets/images/default-profile.png';
                } else {
                    // Make sure the path starts with /assets/ for consistency
                    if (strpos($user['photo_profil'], '/uploads/') === 0) {
                        $user['photo_profil'] = '/assets' . $user['photo_profil'];
                        error_log('Corrected photo path: ' . $user['photo_profil']);
                    }
                }
            }

            return $user;
        } catch (PDOException $e) {
            // Log the error
            error_log('Database error in getUserProfile: ' . $e->getMessage());
            return false;
        }
    }


    public function updateUser($id, $data) {
        // Build the update query dynamically based on the fields to update
        $fields = [];
        $values = [];

        foreach ($data as $key => $value) {
            $fields[] = "$key = ?";
            $values[] = $value;
        }

        $values[] = $id; // Add ID for the WHERE clause

        $query = "UPDATE users SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $this->db->prepare($query);

        return $stmt->execute($values);
    }

    public function changePassword($id, $newPassword) {
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        $stmt = $this->db->prepare("UPDATE users SET password = ? WHERE id = ?");
        return $stmt->execute([$hashedPassword, $id]);
    }

    public function createUser($userData) {
        // Hash password if it exists
        if (isset($userData['password'])) {
            $userData['password'] = password_hash($userData['password'], PASSWORD_DEFAULT);
        }

        // Build the insert query dynamically
        $fields = implode(', ', array_keys($userData));
        $placeholders = implode(', ', array_fill(0, count($userData), '?'));
        $query = "INSERT INTO users ($fields) VALUES ($placeholders)";
        $stmt = $this->db->prepare($query);

        return $stmt->execute(array_values($userData));
    }

    /**
     * Get all active users from the database with optional search and filter
     * Search prioritizes email, then name, then login for better user experience
     *
     * @param string $search - Optional search term
     * @param string $role - Optional role filter
     * @param string $department - Optional department filter
     * @return array - Array of user data
     */
    public function getAllUsers($search = '', $role = '', $department = '') {
        try {
            $query = "
                SELECT
                    id, login, email, nom, prenom, role,
                    date_creation, departement, photo_profil
                FROM users
                WHERE actif = 1
            ";

            $params = [];

            // Add search condition if provided (prioritize email in search)
            if (!empty($search)) {
                $query .= " AND (
                    email LIKE ? OR
                    nom LIKE ? OR
                    prenom LIKE ? OR
                    login LIKE ?
                )";
                $searchTerm = "%$search%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            // Add role filter if provided
            if (!empty($role)) {
                $query .= " AND role = ?";
                $params[] = $role;
            }

            // Add department filter if provided
            if (!empty($department)) {
                $query .= " AND departement = ?";
                $params[] = $department;
            }

            // Order by email match first if searching, then by name
            if (!empty($search)) {
                $query .= " ORDER BY
                    CASE WHEN email LIKE ? THEN 1 ELSE 2 END,
                    nom, prenom";
                $params[] = "%$search%";
            } else {
                $query .= " ORDER BY nom, prenom";
            }

            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            // Log the error
            error_log('Database error in getAllUsers: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Delete a user (soft delete by setting actif to 0)
     *
     * @param int $id - The user ID to delete
     * @return bool - Success or failure
     */
    public function deleteUser($id) {
        try {
            $stmt = $this->db->prepare("UPDATE users SET actif = 0 WHERE id = ?");
            return $stmt->execute([$id]);
        } catch (PDOException $e) {
            // Log the error
            error_log('Database error in deleteUser: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get total count of active users
     *
     * @return int - Total number of active users
     */
    public function getTotalUsersCount() {
        try {
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM users WHERE actif = 1");
            $stmt->execute();
            return $stmt->fetch()['count'];
        } catch (PDOException $e) {
            error_log('Database error in getTotalUsersCount: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get all departments from users table
     *
     * @return array - List of unique departments
     */
    public function getAllDepartments() {
        try {
            $stmt = $this->db->prepare("
                SELECT DISTINCT departement
                FROM users
                WHERE departement IS NOT NULL AND departement != ''
                ORDER BY departement ASC
            ");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (PDOException $e) {
            error_log('Database error in getAllDepartments: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get team members for a specific responsable
     *
     * @param int $responsableId - The ID of the responsable
     * @return array - List of team members
     */
    public function getTeamMembers($responsableId) {
        try {
            // Get all employees managed by this responsable using the manager_id relationship
            $stmt = $this->db->prepare("
                SELECT
                    id, login, email, nom, prenom, role,
                    departement, photo_profil, date_entree
                FROM users
                WHERE manager_id = ? AND role = 'employe' AND actif = 1
                ORDER BY nom, prenom
            ");
            $stmt->execute([$responsableId]);
            $teamMembers = $stmt->fetchAll();

            // Calculate remaining days and days taken for each team member
            foreach ($teamMembers as &$member) {
                // Get total leave days for the current year
                $stmt = $this->db->prepare("
                    SELECT SUM(DATEDIFF(date_fin, date_debut) + 1) as total_days
                    FROM demandes_conges
                    WHERE user_id = ?
                    AND statut = 'acceptée'
                    AND YEAR(date_debut) = YEAR(CURDATE())
                ");
                $stmt->execute([$member['id']]);
                $result = $stmt->fetch();

                // Default annual leave allowance (can be made configurable)
                $annualAllowance = 25;

                // Calculate days taken and remaining
                $joursPris = $result['total_days'] ? intval($result['total_days']) : 0;
                $joursRestants = $annualAllowance - $joursPris;

                $member['joursRestants'] = max(0, $joursRestants);
                $member['joursPris'] = $joursPris;

                // Get upcoming leave if any
                $stmt = $this->db->prepare("
                    SELECT date_debut
                    FROM demandes_conges
                    WHERE user_id = ? AND date_debut >= CURDATE() AND statut = 'acceptée'
                    ORDER BY date_debut ASC
                    LIMIT 1
                ");
                $stmt->execute([$member['id']]);
                $prochaineAbsence = $stmt->fetch(PDO::FETCH_COLUMN);

                $member['prochaineAbsence'] = $prochaineAbsence ?: null;
            }

            return $teamMembers;
        } catch (PDOException $e) {
            error_log('Database error in getTeamMembers: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get team members with detailed information for the team members page
     *
     * @param int $responsableId - The ID of the responsable
     * @param string $departmentFilter - Filter by department or 'all'
     * @param string $statusFilter - Filter by status ('active', 'inactive', 'all')
     * @return array - List of team members with detailed information
     */
    public function getTeamMembersWithDetails($responsableId, $departmentFilter = 'all', $statusFilter = 'all') {
        try {
            // Start building the query
            $query = "
                SELECT
                    u.id, u.login, u.email, u.nom, u.prenom, u.role,
                    u.departement, u.photo_profil, u.date_entree, u.actif,
                    u.telephone, u.poste,
                    (SELECT COUNT(*) FROM demandes_conges
                     WHERE user_id = u.id AND statut = 'acceptee') as total_conges_pris
                FROM users u
                WHERE u.manager_id = ? AND u.role = 'employe'
            ";

            $params = [$responsableId];

            // Add department filter if not 'all'
            if ($departmentFilter !== 'all') {
                $query .= " AND u.departement = ?";
                $params[] = $departmentFilter;
            }

            // Add status filter if not 'all'
            if ($statusFilter === 'active') {
                $query .= " AND u.actif = 1";
            } elseif ($statusFilter === 'inactive') {
                $query .= " AND u.actif = 0";
            }

            $query .= " ORDER BY u.nom, u.prenom";

            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            $teamMembers = $stmt->fetchAll();

            // Get additional data for each team member
            $leaveBalanceModel = new LeaveBalanceModel();

            foreach ($teamMembers as &$member) {
                // Initialize leave balance model and ensure user has current balances
                $leaveBalanceModel->initializeUserLeaveBalances($member['id']);

                // Apply monthly accruals to ensure balances are up-to-date
                $leaveBalanceModel->applyMonthlyAccrualForUser($member['id']);

                // Get current leave balance information
                $leaveBalances = $leaveBalanceModel->getUserLeaveBalances($member['id']);

                // Focus on paid leave as the primary metric for managers
                $paidLeaveBalance = $leaveBalances['payé'] ?? ['remaining' => 0, 'used' => 0, 'total' => 19];

                // Calculate display values based on base allocation (19 days) without monthly accruals
                $baseAllocation = 19; // Standard base allocation
                $usedDays = (int)$paidLeaveBalance['used'];

                // Set the remaining days based on base allocation minus used days
                $member['joursRestants'] = max(0, $baseAllocation - $usedDays);

                // Set the used days (primarily paid leave)
                $member['joursPris'] = $usedDays;

                // Get next absence date if any
                $stmt = $this->db->prepare("
                    SELECT date_debut FROM demandes_conges
                    WHERE user_id = ? AND date_debut >= CURDATE() AND statut = 'acceptee'
                    ORDER BY date_debut ASC
                    LIMIT 1
                ");
                $stmt->execute([$member['id']]);
                $prochaineAbsence = $stmt->fetch(PDO::FETCH_COLUMN);

                $member['prochaineAbsence'] = $prochaineAbsence ?: null;

                // Get pending requests count
                $stmt = $this->db->prepare("
                    SELECT COUNT(*) FROM demandes_conges
                    WHERE user_id = ? AND (statut = 'en_attente_responsable' OR statut = 'en_attente_planificateur')
                ");
                $stmt->execute([$member['id']]);
                $member['pending_requests'] = $stmt->fetchColumn();

                // Get current absence status
                $today = date('Y-m-d');
                $stmt = $this->db->prepare("
                    SELECT COUNT(*) FROM demandes_conges
                    WHERE user_id = ? AND ? BETWEEN date_debut AND date_fin AND statut = 'acceptee'
                ");
                $stmt->execute([$member['id'], $today]);
                $member['is_absent_today'] = ($stmt->fetchColumn() > 0);

                // Format date for display
                if (!empty($member['date_entree'])) {
                    $member['date_entree_formatted'] = date('d/m/Y', strtotime($member['date_entree']));
                } else {
                    $member['date_entree_formatted'] = 'Non définie';
                }

                // Store full leave balances for detailed view
                $member['leaveBalances'] = $leaveBalances;
            }

            return $teamMembers;

        } catch (PDOException $e) {
            error_log('Database error in getTeamMembersWithDetails: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Check if a user is managed by a specific responsable
     *
     * @param int $userId - The ID of the user to check
     * @param int $responsableId - The ID of the responsable
     * @return bool - True if the user is managed by the responsable, false otherwise
     */
    public function isUserManagedByResponsable($userId, $responsableId) {
        try {
            // Check if the user is directly managed by this responsable using the manager_id relationship
            $stmt = $this->db->prepare("
                SELECT COUNT(*)
                FROM users
                WHERE id = ? AND manager_id = ? AND role = 'employe'
            ");
            $stmt->execute([$userId, $responsableId]);
            $count = $stmt->fetchColumn();

            return $count > 0;
        } catch (PDOException $e) {
            error_log('Database error in isUserManagedByResponsable: ' . $e->getMessage());
            return false;
        }
    }
}
