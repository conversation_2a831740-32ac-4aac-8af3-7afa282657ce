<?php
// Model for handling document-related database operations

class DocumentModel extends Model {

    // Get all internal documents
    public function getAllDocuments() {
        $stmt = $this->db->prepare("
            SELECT * FROM documents
            WHERE actif = 1
            ORDER BY ordre ASC, nom ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    // Get a specific document by ID
    public function getDocumentById($id) {
        $stmt = $this->db->prepare("
            SELECT * FROM documents
            WHERE id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }

    // Create a new document
    public function createDocument($nom, $lien, $description = null, $ordre = 0, $categorie = 'general') {
        $stmt = $this->db->prepare("
            INSERT INTO documents (nom, lien, description, ordre, categorie, date_creation, actif)
            VALUES (?, ?, ?, ?, ?, NOW(), 1)
        ");
        return $stmt->execute([$nom, $lien, $description, $ordre, $categorie]);
    }

    // Update an existing document
    public function updateDocument($id, $nom, $lien, $description = null, $ordre = 0, $categorie = 'general', $actif = 1) {
        $stmt = $this->db->prepare("
            UPDATE documents
            SET nom = ?, lien = ?, description = ?, ordre = ?, categorie = ?, actif = ?
            WHERE id = ?
        ");
        return $stmt->execute([$nom, $lien, $description, $ordre, $categorie, $actif, $id]);
    }

    // Delete a document (soft delete by setting actif to 0)
    public function deleteDocument($id) {
        $stmt = $this->db->prepare("
            UPDATE documents
            SET actif = 0
            WHERE id = ?
        ");
        return $stmt->execute([$id]);
    }

    // Hard delete a document (remove from database)
    public function hardDeleteDocument($id) {
        $stmt = $this->db->prepare("
            DELETE FROM documents
            WHERE id = ?
        ");
        return $stmt->execute([$id]);
    }

    // Get documents by category
    public function getDocumentsByCategory($categorie) {
        $stmt = $this->db->prepare("
            SELECT * FROM documents
            WHERE categorie = ? AND actif = 1
            ORDER BY ordre ASC, nom ASC
        ");
        $stmt->execute([$categorie]);
        return $stmt->fetchAll();
    }

    // Get documents for employee dashboard (internal documents)
    public function getInternalDocuments() {
        // Return all active documents from the database
        return $this->getAllDocuments();
    }
}