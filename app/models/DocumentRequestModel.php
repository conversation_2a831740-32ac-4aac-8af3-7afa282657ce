<?php

class DocumentRequestModel extends Model {
    
    public function __construct() {
        parent::__construct();
    }

    // Create a new document request
    public function createDocumentRequest($userId, $type, $documentName, $comments = null) {
        $stmt = $this->db->prepare("
            INSERT INTO document_requests (user_id, type, document_name, comments, status, date_requested)
            VALUES (?, ?, ?, ?, 'pending', NOW())
        ");
        return $stmt->execute([$userId, $type, $documentName, $comments]);
    }

    // Get all document requests for a user
    public function getUserDocumentRequests($userId, $limit = null) {
        $sql = "
            SELECT dr.*, u.nom, u.prenom, u.matricule_rh
            FROM document_requests dr
            JOIN users u ON dr.user_id = u.id
            WHERE dr.user_id = ?
            ORDER BY dr.date_requested DESC
        ";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Get all document requests (for HR/Admin)
    public function getAllDocumentRequests($status = null, $limit = null, $offset = 0) {
        $sql = "
            SELECT dr.*, u.nom, u.prenom, u.matricule_rh, u.departement
            FROM document_requests dr
            JOIN users u ON dr.user_id = u.id
        ";
        
        $params = [];
        if ($status) {
            $sql .= " WHERE dr.status = ?";
            $params[] = $status;
        }
        
        $sql .= " ORDER BY dr.date_requested DESC";
        
        if ($limit) {
            $sql .= " LIMIT " . intval($limit) . " OFFSET " . intval($offset);
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Update document request status
    public function updateRequestStatus($requestId, $status, $processedBy = null, $comments = null) {
        $sql = "
            UPDATE document_requests 
            SET status = ?, processed_by = ?, processing_comments = ?, date_processed = NOW()
            WHERE id = ?
        ";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$status, $processedBy, $comments, $requestId]);
    }

    // Get document request by ID
    public function getDocumentRequestById($requestId) {
        $stmt = $this->db->prepare("
            SELECT dr.*, u.nom, u.prenom, u.matricule_rh, u.departement, u.email,
                   p.nom as processor_nom, p.prenom as processor_prenom
            FROM document_requests dr
            JOIN users u ON dr.user_id = u.id
            LEFT JOIN users p ON dr.processed_by = p.id
            WHERE dr.id = ?
        ");
        $stmt->execute([$requestId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Get statistics for document requests
    public function getDocumentRequestStats($userId = null) {
        $sql = "
            SELECT 
                COUNT(*) as total_requests,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_requests,
                SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_requests
            FROM document_requests
        ";
        
        $params = [];
        if ($userId) {
            $sql .= " WHERE user_id = ?";
            $params[] = $userId;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Format document type for display
    public function formatDocumentType($type) {
        $types = [
            'attestation_travail' => 'Attestation de travail',
            'fiche_paie' => 'Fiche de paie',
            'attestation_salaire' => 'Attestation de salaire'
        ];
        
        return $types[$type] ?? ucfirst(str_replace('_', ' ', $type));
    }

    // Format status for display
    public function formatStatus($status) {
        $statuses = [
            'pending' => 'En attente',
            'processing' => 'En cours de traitement',
            'completed' => 'Terminé',
            'rejected' => 'Rejeté'
        ];
        
        return $statuses[$status] ?? ucfirst($status);
    }
}
