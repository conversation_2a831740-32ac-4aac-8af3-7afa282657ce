<?php
// Model for handling notification-related database operations

class NotificationModel extends Model {

    // Get all notifications for a user
    public function getNotificationsForUser($userId = null) {
        // If no user ID is provided, use the session user ID
        if ($userId === null && isset($_SESSION['user_id'])) {
            $userId = $_SESSION['user_id'];
        }

        $stmt = $this->db->prepare("
            SELECT * FROM notifications
            WHERE user_id = ?
            ORDER BY created_at DESC
        ");
        $stmt->execute([$userId]);
        return $stmt->fetchAll();
    }

    // Get unread notification count
    public function getUnreadCount($userId) {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count
            FROM notifications
            WHERE user_id = ? AND is_read = 0
        ");
        $stmt->execute([$userId]);
        return $stmt->fetch()['count'];
    }

    // Mark notification as read
    public function markAsRead($id, $userId) {
        $stmt = $this->db->prepare("
            UPDATE notifications
            SET is_read = 1
            WHERE id = ? AND user_id = ?
        ");
        return $stmt->execute([$id, $userId]);
    }

    // Mark all notifications as read
    public function markAllAsRead($userId) {
        $stmt = $this->db->prepare("
            UPDATE notifications
            SET is_read = 1
            WHERE user_id = ?
        ");
        return $stmt->execute([$userId]);
    }

    // Delete notification
    public function deleteNotification($id, $userId) {
        $stmt = $this->db->prepare("
            DELETE FROM notifications
            WHERE id = ? AND user_id = ?
        ");
        return $stmt->execute([$id, $userId]);
    }

    // Create a new notification
    public function createNotification($userId, $type = 'info', $title = null, $message = '', $demandeId = null) {
        try {
            error_log("Creating notification: User=$userId, Type=$type, Title=$title, Message=$message, DemandeId=$demandeId");

            $stmt = $this->db->prepare("
                INSERT INTO notifications (user_id, demande_id, type, title, message, created_at, is_read)
                VALUES (?, ?, ?, ?, ?, NOW(), 0)
            ");
            $result = $stmt->execute([$userId, $demandeId, $type, $title, $message]);

            if ($result) {
                $notificationId = $this->db->lastInsertId();
                error_log("Notification created successfully with ID: $notificationId");
            } else {
                error_log("Failed to create notification");
            }

            return $result;
        } catch (PDOException $e) {
            error_log("Error creating notification: " . $e->getMessage());
            return false;
        }
    }

    // Get a specific notification by ID with user security check
    public function getNotificationById($notificationId, $userId) {
        try {
            $stmt = $this->db->prepare("
                SELECT * FROM notifications
                WHERE id = ? AND user_id = ?
            ");
            $stmt->execute([$notificationId, $userId]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Error getting notification by ID: " . $e->getMessage());
            return false;
        }
    }

    // Delete old notifications (maintenance)
    public function deleteOldNotifications($days = 30) {
        $stmt = $this->db->prepare("
            DELETE FROM notifications
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        return $stmt->execute([$days]);
    }

    // Create sample notifications for testing
    public function createSampleNotifications($userId) {
        try {
            // First, check if there are already notifications for this user
            $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM notifications WHERE user_id = ?");
            $stmt->execute([$userId]);
            $result = $stmt->fetch();

            if ($result && $result['count'] > 0) {
                // User already has notifications, don't create more
                return true;
            }

            // Create sample notifications of different types
            $sampleData = [
                [
                    'title' => 'Bienvenue sur Gestion Congés',
                    'message' => 'Bienvenue dans votre espace personnel de gestion des congés.',
                    'type' => 'info'
                ]
            ];

            $successCount = 0;
            foreach ($sampleData as $data) {
                // Use the createNotification method instead of direct SQL
                if ($this->createNotification($userId, $data['type'], $data['title'], $data['message'])) {
                    $successCount++;
                }
            }

            return $successCount > 0;
        } catch (PDOException $e) {
            // Log the error or handle it as needed
            error_log('Error creating sample notifications: ' . $e->getMessage());
            return false;
        }
    }
}
