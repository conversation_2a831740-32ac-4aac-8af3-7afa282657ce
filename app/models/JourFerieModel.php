<?php
// Model for handling jours_feries-related database operations

class JourFerieModel extends Model {

    // Get all holidays
    public function getAllJoursFeries() {
        $stmt = $this->db->prepare("
            SELECT * FROM jours_feries
            ORDER BY date ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll();
    }

    // Get holidays for a specific year
    public function getJoursFeriesByYear($year, $pays = null) {
        try {
            $startDate = $year . '-01-01';
            $endDate = $year . '-12-31';

            $sql = "SELECT * FROM jours_feries WHERE (date BETWEEN ? AND ?)";
            $params = [$startDate, $endDate];

            // Add recurring holidays
            $sql .= " OR (est_recurrent = 1)";

            if ($pays) {
                $sql .= " AND pays = ?";
                $params[] = $pays;
            }

            $sql .= " ORDER BY date ASC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $holidays = $stmt->fetchAll();

            // Process recurring holidays
            $result = [];
            foreach ($holidays as $holiday) {
                if ($holiday['est_recurrent'] == 1 && substr($holiday['date'], 0, 4) != $year) {
                    // For recurring holidays, update the year to the requested year
                    $date = new DateTime($holiday['date']);
                    $month = $date->format('m');
                    $day = $date->format('d');
                    $newDate = $year . '-' . $month . '-' . $day;

                    $holiday['date'] = $newDate;
                }

                $result[] = $holiday;
            }

            // Sort by date
            usort($result, function($a, $b) {
                return strcmp($a['date'], $b['date']);
            });

            return $result;
        } catch (PDOException $e) {
            error_log('Database error in getJoursFeriesByYear: ' . $e->getMessage());
            return [];
        }
    }

    // Get holidays for a specific year with search
    public function searchJoursFeries($year, $search = '', $pays = null) {
        $startDate = $year . '-01-01';
        $endDate = $year . '-12-31';

        $sql = "SELECT * FROM jours_feries WHERE date BETWEEN ? AND ?";
        $params = [$startDate, $endDate];

        if (!empty($search)) {
            $sql .= " AND nom LIKE ?";
            $params[] = "%$search%";
        }

        if ($pays) {
            $sql .= " AND pays = ?";
            $params[] = $pays;
        }

        $sql .= " ORDER BY date ASC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    // Get a specific holiday by ID
    public function getJourFerieById($id) {
        $stmt = $this->db->prepare("
            SELECT * FROM jours_feries
            WHERE id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }

    // Create a new holiday
    public function createJourFerie($nom, $date, $estRecurrent = false, $pays = 'France') {
        $stmt = $this->db->prepare("
            INSERT INTO jours_feries (nom, date, est_recurrent, date_creation, pays)
            VALUES (?, ?, ?, NOW(), ?)
        ");
        return $stmt->execute([$nom, $date, $estRecurrent ? 1 : 0, $pays]);
    }

    // Update an existing holiday
    public function updateJourFerie($id, $nom, $date, $estRecurrent = false, $pays = null) {
        if ($pays) {
            $stmt = $this->db->prepare("
                UPDATE jours_feries
                SET nom = ?, date = ?, est_recurrent = ?, pays = ?
                WHERE id = ?
            ");
            return $stmt->execute([$nom, $date, $estRecurrent ? 1 : 0, $pays, $id]);
        } else {
            $stmt = $this->db->prepare("
                UPDATE jours_feries
                SET nom = ?, date = ?, est_recurrent = ?
                WHERE id = ?
            ");
            return $stmt->execute([$nom, $date, $estRecurrent ? 1 : 0, $id]);
        }
    }

    // Delete a holiday
    public function deleteJourFerie($id) {
        $stmt = $this->db->prepare("
            DELETE FROM jours_feries
            WHERE id = ?
        ");
        return $stmt->execute([$id]);
    }

    // Get upcoming holidays
    public function getUpcomingJoursFeries($limit = 5) {
        $today = date('Y-m-d');

        $stmt = $this->db->prepare("
            SELECT * FROM jours_feries
            WHERE date >= ?
            ORDER BY date ASC
            LIMIT " . intval($limit) . "
        ");
        $stmt->execute([$today]);
        return $stmt->fetchAll();
    }



    // Get holidays for a specific month
    public function getJoursFeriesByMonth($month, $year = null, $pays = null) {
        try {
            if ($year === null) {
                $year = date('Y');
            }

            // Format month to ensure it's two digits
            $month = str_pad($month, 2, '0', STR_PAD_LEFT);

            $startDate = $year . '-' . $month . '-01';
            $endDate = date('Y-m-t', strtotime($startDate));

            $sql = "SELECT * FROM jours_feries WHERE (date BETWEEN ? AND ?)";
            $params = [$startDate, $endDate];

            // Add recurring holidays that fall in this month
            $sql .= " OR (est_recurrent = 1 AND MONTH(date) = ?)";
            $params[] = $month;

            if ($pays) {
                $sql .= " AND pays = ?";
                $params[] = $pays;
            }

            $sql .= " ORDER BY date ASC";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            $holidays = $stmt->fetchAll();

            // Process recurring holidays
            $result = [];
            foreach ($holidays as $holiday) {
                if ($holiday['est_recurrent'] == 1 && substr($holiday['date'], 0, 4) != $year) {
                    // For recurring holidays, update the year to the requested year
                    $date = new DateTime($holiday['date']);
                    $month = $date->format('m');
                    $day = $date->format('d');
                    $newDate = $year . '-' . $month . '-' . $day;

                    $holiday['date'] = $newDate;
                }

                $result[] = $holiday;
            }

            // Sort by date
            usort($result, function($a, $b) {
                return strcmp($a['date'], $b['date']);
            });

            return $result;
        } catch (PDOException $e) {
            error_log('Database error in getJoursFeriesByMonth: ' . $e->getMessage());
            return [];
        }
    }

    // Get all available countries
    public function getAllPays() {
        $stmt = $this->db->prepare("
            SELECT DISTINCT pays FROM jours_feries
            ORDER BY pays ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
}
