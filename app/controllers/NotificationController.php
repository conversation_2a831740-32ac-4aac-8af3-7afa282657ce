<?php
// Notification Controller

class NotificationController extends Controller {
    private $notificationModel;

    public function __construct() {
        // Load model
        $this->notificationModel = new NotificationModel();
    }

    public function index() {
        // Get user ID from session, if not available, use 1 as default
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 1;

        // Get notifications from database
        $notifications = $this->notificationModel->getNotificationsForUser($userId);

        // If no notifications found, create sample ones in the database
        if (empty($notifications)) {
            // Create sample notifications in the database
            $created = $this->notificationModel->createSampleNotifications($userId);

            // Fetch the newly created notifications
            if ($created) {
                $notifications = $this->notificationModel->getNotificationsForUser($userId);
            }

            // If still empty (in case of database error), use empty array
            if (empty($notifications)) {
                $notifications = [];
            }
        }

        $this->view('notifications/index', ['notifications' => $notifications]);
    }

    public function markAsRead() {
        $id = $_GET['id'] ?? 0;

        if (!empty($id)) {
            // Get user ID from session, if not available, use 1 as default
            $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 1;

            // Mark notification as read in database
            $this->notificationModel->markAsRead($id, $userId);
        }

        $this->redirect('/notifications');
    }

    public function markAllAsRead() {
        // Get user ID from session, if not available, use 1 as default
        $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 1;

        // Mark all notifications as read in database
        $this->notificationModel->markAllAsRead($userId);

        $this->redirect('/notifications');
    }

    public function delete() {
        $id = $_GET['id'] ?? 0;

        if (!empty($id)) {
            // Get user ID from session, if not available, use 1 as default
            $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 1;

            // Delete notification from database
            $this->notificationModel->deleteNotification($id, $userId);
        }

        $this->redirect('/notifications');
    }

    /**
     * Handle notification click and redirect to appropriate demande details page
     */
    public function click() {
        $notificationId = $_GET['id'] ?? 0;
        $userId = $_SESSION['user_id'] ?? null;

        if (!$notificationId || !$userId) {
            $this->redirect('/notifications');
            return;
        }

        // Get the notification with security check
        $notification = $this->notificationModel->getNotificationById($notificationId, $userId);

        if (!$notification) {
            // Notification doesn't exist or doesn't belong to user
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Notification introuvable ou accès non autorisé.'
            ];
            $this->redirect('/notifications');
            return;
        }

        // Mark notification as read
        $this->notificationModel->markAsRead($notificationId, $userId);

        // If notification has a demande_id, redirect to appropriate details page
        if (!empty($notification['demande_id'])) {
            $demandeId = $notification['demande_id'];
            $userRole = $_SESSION['role'] ?? 'employe';

            // Verify user has permission to view this demande
            $demandeModel = new DemandeModel();
            $demande = null;

            switch ($userRole) {
                case 'admin':
                    $demande = $demandeModel->getDemandeById($demandeId);
                    $redirectUrl = "/details-demande?id={$demandeId}";
                    break;

                case 'responsable':
                    // Check if it's their own demande or a team member's
                    $ownDemande = $demandeModel->getDemandeById($demandeId, $userId);
                    if ($ownDemande) {
                        $demande = $ownDemande;
                        $redirectUrl = "/responsable/details-demande?id={$demandeId}";
                    } else {
                        $demande = $demandeModel->getDemandeByIdForResponsable($demandeId, $userId);
                        $redirectUrl = "/responsable/details-demande?id={$demandeId}";
                    }
                    break;

                case 'planificateur':
                    // Check if it's their own demande or any other demande
                    $ownDemande = $demandeModel->getDemandeById($demandeId, $userId);
                    if ($ownDemande) {
                        $demande = $ownDemande;
                        $redirectUrl = "/planificateur/details-demande?id={$demandeId}";
                    } else {
                        $demande = $demandeModel->getDemandeById($demandeId);
                        $redirectUrl = "/planificateur/details-demande?id={$demandeId}";
                    }
                    break;

                default: // employe
                    $demande = $demandeModel->getDemandeById($demandeId, $userId);
                    $redirectUrl = "/details-demande?id={$demandeId}";
                    break;
            }

            if ($demande) {
                $this->redirect($redirectUrl);
                return;
            } else {
                // User doesn't have permission to view this demande or it doesn't exist
                $_SESSION['flash_message'] = [
                    'type' => 'error',
                    'message' => 'Demande introuvable ou accès non autorisé.'
                ];
            }
        }

        // Fallback: redirect to notifications page
        $this->redirect('/notifications');
    }

    /**
     * Create a new notification
     * This method can be called by other controllers to create notifications
     * @param int $userId User ID
     * @param string $message Notification message
     * @param string $title Optional notification title
     * @param string $type Notification type (info, success, error, warning)
     * @return bool Success status
     */
    public function createNotification($userId, $message, $title = null, $type = 'info') {
        return $this->notificationModel->createNotification($userId, $message, $title, $type);
    }
}
