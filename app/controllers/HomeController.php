<?php
// Home Controller

class HomeController extends Controller {
    public function index() {
        // If user is logged in, redirect to appropriate dashboard
        if (isset($_SESSION['user_id'])) {
            switch ($_SESSION['role']) {
                case 'employe':
                    $this->redirect('/mes-demandes');
                    break;
                case 'planificateur':
                    $this->redirect('/dashboard-planificateur');
                    break;
                case 'responsable':
                    $this->redirect('/dashboard-responsable');
                    break;
                case 'admin':
                    $this->redirect('/dashboard-admin');
                    break;
                default:
                    // If role is unknown, show login page
                    $this->view('auth/login');
                    break;
            }
        } else {
            // Not logged in, show login page
            $this->view('auth/login');
        }
    }
}
