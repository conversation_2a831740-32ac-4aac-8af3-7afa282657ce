<?php
// Controller for authentication

class AuthController extends Controller {
    private $userModel;

    public function __construct() {
        $this->userModel = new UserModel();
    }

    // Display login page
    public function index() {
        $this->view('auth/login');
    }

    // Handle login form submission
    public function login() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Support both new email field and legacy login field for backward compatibility
            $identifier = $_POST['email'] ?? $_POST['login'] ?? '';
            $password = $_POST['password'] ?? '';

            // Use method that supports both email and username for backward compatibility
            $user = $this->userModel->getUserByEmailOrLogin($identifier);

            if ($user && password_verify($password, $user['password'])) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['nom'] = $user['nom'];
                $_SESSION['prenom'] = $user['prenom'];

                // Redirect based on role
                switch ($user['role']) {
                    case 'employe':
                        $this->redirect('/mes-demandes');
                        break;
                    case 'planificateur':
                        $this->redirect('/dashboard-planificateur');
                        break;
                    case 'responsable':
                        $this->redirect('/dashboard-responsable');
                        break;
                    case 'admin':
                        $this->redirect('/dashboard-admin');
                        break;
                    default:
                        $this->redirect('/login?error=role_inconnu');
                        break;
                }
            } else {
                // Invalid credentials
                $this->redirect('/login?error=identifiants_incorrects');
            }
        } else {
            // If not a POST request, show login page
            $this->view('auth/login');
        }
    }

    // Handle logout
    public function logout() {
        // No need to call session_start() as it's already started in index.php
        session_destroy();
        $this->redirect('/home');
    }
}
