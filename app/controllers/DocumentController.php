<?php
// Document Controller for handling document-related operations

class DocumentController extends Controller {
    private $documentModel;

    public function __construct() {
        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('/home');
            exit();
        }

        // Load model
        $this->documentModel = new DocumentModel();
    }

    // List all documents
    public function liste() {
        // Get all documents
        $documents = $this->documentModel->getAllDocuments();

        // Display the documents list based on user role
        switch ($_SESSION['role']) {
            case 'admin':
                $this->view('admin/documents', ['documents' => $documents]);
                break;
            default:
                $this->view('documents/liste', ['documents' => $documents]);
                break;
        }
    }

    // Display form to add a new document (admin only)
    public function ajouter() {
        // Check if user is admin
        if ($_SESSION['role'] !== 'admin') {
            $this->redirect('/documents');
            exit();
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Get form data
            $nom = $_POST['nom'] ?? '';
            $lien = $_POST['lien'] ?? '';
            $description = $_POST['description'] ?? '';
            $ordre = $_POST['ordre'] ?? 0;
            $categorie = $_POST['categorie'] ?? 'general';

            // Validate form data
            $errors = [];

            if (empty($nom)) {
                $errors[] = 'Le nom du document est requis';
            }

            if (empty($lien)) {
                $errors[] = 'Le lien du document est requis';
            }

            if (empty($errors)) {
                // Create new document
                $success = $this->documentModel->createDocument($nom, $lien, $description, $ordre, $categorie);

                if ($success) {
                    $this->redirect('/documents');
                } else {
                    $this->view('admin/documents_ajouter', [
                        'error' => 'Erreur lors de la création du document',
                        'nom' => $nom,
                        'lien' => $lien,
                        'description' => $description,
                        'ordre' => $ordre,
                        'categorie' => $categorie
                    ]);
                }
            } else {
                $this->view('admin/documents_ajouter', [
                    'errors' => $errors,
                    'nom' => $nom,
                    'lien' => $lien,
                    'description' => $description,
                    'ordre' => $ordre,
                    'categorie' => $categorie
                ]);
            }
        } else {
            // Display form
            $this->view('admin/documents_ajouter');
        }
    }

    // Display form to edit an existing document (admin only)
    public function modifier() {
        // Check if user is admin
        if ($_SESSION['role'] !== 'admin') {
            $this->redirect('/documents');
            exit();
        }

        $id = $_GET['id'] ?? 0;

        if (empty($id)) {
            $this->redirect('/documents');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Get form data
            $nom = $_POST['nom'] ?? '';
            $lien = $_POST['lien'] ?? '';
            $description = $_POST['description'] ?? '';
            $ordre = $_POST['ordre'] ?? 0;
            $categorie = $_POST['categorie'] ?? 'general';
            $actif = isset($_POST['actif']) ? 1 : 0;

            // Validate form data
            $errors = [];

            if (empty($nom)) {
                $errors[] = 'Le nom du document est requis';
            }

            if (empty($lien)) {
                $errors[] = 'Le lien du document est requis';
            }

            if (empty($errors)) {
                // Update document
                $success = $this->documentModel->updateDocument($id, $nom, $lien, $description, $ordre, $categorie, $actif);

                if ($success) {
                    $this->redirect('/documents');
                } else {
                    $this->view('admin/documents_modifier', [
                        'error' => 'Erreur lors de la modification du document',
                        'document' => [
                            'id' => $id,
                            'nom' => $nom,
                            'lien' => $lien,
                            'description' => $description,
                            'ordre' => $ordre,
                            'categorie' => $categorie,
                            'actif' => $actif
                        ]
                    ]);
                }
            } else {
                $this->view('admin/documents_modifier', [
                    'errors' => $errors,
                    'document' => [
                        'id' => $id,
                        'nom' => $nom,
                        'lien' => $lien,
                        'description' => $description,
                        'ordre' => $ordre,
                        'categorie' => $categorie,
                        'actif' => $actif
                    ]
                ]);
            }
        } else {
            // Get document details
            $document = $this->documentModel->getDocumentById($id);

            if (!$document) {
                $this->redirect('/documents');
                return;
            }

            // Display form with document details
            $this->view('admin/documents_modifier', ['document' => $document]);
        }
    }

    // Delete a document (admin only)
    public function supprimer() {
        // Check if user is admin
        if ($_SESSION['role'] !== 'admin') {
            $this->redirect('/documents');
            exit();
        }

        $id = $_GET['id'] ?? 0;

        if (empty($id)) {
            $this->redirect('/documents');
            return;
        }

        // Delete document (soft delete)
        $success = $this->documentModel->deleteDocument($id);

        $this->redirect('/documents');
    }

    // Get internal documents for dashboard
    public function getInternalDocuments() {
        return $this->documentModel->getInternalDocuments();
    }

    // Serve PDF files from the document directory
    public function serve() {
        // Ensure session is started
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            http_response_code(401);
            echo 'Unauthorized access';
            return;
        }

        $filename = $_GET['file'] ?? '';

        // Validate filename to prevent directory traversal
        if (empty($filename) || strpos($filename, '..') !== false || strpos($filename, '/') !== false || strpos($filename, '\\') !== false) {
            http_response_code(400);
            echo 'Invalid file name';
            return;
        }

        // Only allow specific file types
        $allowedExtensions = ['pdf', 'doc', 'docx', 'txt'];
        $fileExtension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        if (!in_array($fileExtension, $allowedExtensions)) {
            http_response_code(400);
            echo 'File type not allowed';
            return;
        }

        // Build the file path
        $filePath = __DIR__ . '/../../public/document/' . $filename;

        // Check if file exists
        if (!file_exists($filePath)) {
            http_response_code(404);
            echo 'File not found';
            return;
        }

        // Check if file is readable
        if (!is_readable($filePath)) {
            http_response_code(403);
            echo 'File not accessible';
            return;
        }

        // Set appropriate headers based on file type
        $mimeTypes = [
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'txt' => 'text/plain'
        ];

        $mimeType = $mimeTypes[$fileExtension] ?? 'application/octet-stream';

        header('Content-Type: ' . $mimeType);
        header('Content-Disposition: inline; filename="' . basename($filename) . '"');
        header('Content-Length: ' . filesize($filePath));
        header('Cache-Control: public, max-age=3600');
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT');
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: SAMEORIGIN');

        // Clear any previous output
        if (ob_get_level()) {
            ob_end_clean();
        }

        // Output the file
        readfile($filePath);
        exit;
    }
}