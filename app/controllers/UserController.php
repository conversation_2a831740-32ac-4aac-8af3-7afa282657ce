<?php
// Controller for user-related operations

class UserController extends Controller {
    private $userModel;
    private $leaveBalanceModel;

    public function __construct() {
        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('/home');
            exit();
        }

        $this->userModel = new UserModel();
        $this->leaveBalanceModel = new LeaveBalanceModel();
    }

    // Display user profile
    public function profile() {
        try {
            // Get user profile from database
            $user = $this->userModel->getUserProfile($_SESSION['user_id']);

            if (!$user) {
                // User not found or database error
                $error = "Impossible de charger votre profil. Veuillez contacter l'administrateur.";
                error_log("Failed to load user profile for user ID: " . $_SESSION['user_id']);

                // Use session data as fallback
                $user = [
                    'id' => $_SESSION['user_id'],
                    'nom' => $_SESSION['nom'] ?? 'N/A',
                    'prenom' => $_SESSION['prenom'] ?? 'N/A',
                    'email' => $_SESSION['email'] ?? 'N/A',
                    'role' => $_SESSION['role'] ?? 'N/A',
                    'photo_profil' => '/assets/images/default-profile.png' // Default photo
                ];

                $this->view('user/profile', ['user' => $user, 'error' => $error]);
                return;
            }

            // Get user's leave balances
            try {
                // Initialize leave balances if they don't exist
                $this->leaveBalanceModel->initializeUserLeaveBalances($_SESSION['user_id']);

                // Apply monthly accruals to ensure balances are up-to-date
                $this->leaveBalanceModel->applyMonthlyAccrualForUser($_SESSION['user_id']);

                // Get current leave balance information
                $leaveBalances = $this->leaveBalanceModel->getUserLeaveBalances($_SESSION['user_id']);

                // Extract paid leave balance for display
                $paidLeaveBalance = $leaveBalances['payé'] ?? ['remaining' => 0];
                $user['solde_conge_paye'] = (int)$paidLeaveBalance['remaining'];
            } catch (Exception $e) {
                error_log("Error getting leave balances for user " . $_SESSION['user_id'] . ": " . $e->getMessage());
                // Set fallback value if leave balance retrieval fails
                $user['solde_conge_paye'] = 'N/A';
            }

            $this->view('user/profile', ['user' => $user]);
        } catch (Exception $e) {
            error_log("Exception in profile method: " . $e->getMessage());
            $this->view('errors/error', [
                'title' => 'Erreur système',
                'message' => "Une erreur s'est produite lors du chargement de votre profil."
            ]);
        }
    }

    // Update user profile
    public function update() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // Check if user has permission to edit profile information
                if (!Auth::hasPermission('edit_profile_information')) {
                    // Return JSON response for AJAX requests
                    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => false,
                            'message' => 'Vous n\'avez pas l\'autorisation de modifier les informations de profil'
                        ]);
                        exit;
                    }

                    // For non-AJAX requests, redirect with error
                    $this->redirect('/profil?error=' . urlencode('Vous n\'avez pas l\'autorisation de modifier les informations de profil'));
                    return;
                }
                $nom = $_POST['nom'] ?? '';
                $prenom = $_POST['prenom'] ?? '';
                $email = $_POST['email'] ?? '';
                $telephone = $_POST['telephone'] ?? '';

                // Validate input
                if (empty($nom) || empty($prenom) || empty($email)) {
                    // Always send JSON for /user/update path
                    if ($_SERVER['REQUEST_URI'] == '/user/update') {
                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => false,
                            'message' => 'Tous les champs obligatoires doivent être remplis'
                        ]);
                        exit;
                    }

                    // Get current profile data to re-display
                    $user = $this->userModel->getUserProfile($_SESSION['user_id']);

                    // Override with submitted values
                    $user['nom'] = $nom;
                    $user['prenom'] = $prenom;
                    $user['email'] = $email;
                    $user['telephone'] = $telephone;

                    $this->view('user/profile', [
                        'error' => 'Tous les champs obligatoires doivent être remplis',
                        'user' => $user
                    ]);
                    return;
                }

                // Validate email format
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    // Always send JSON for /user/update path
                    if ($_SERVER['REQUEST_URI'] == '/user/update') {
                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => false,
                            'message' => 'Format d\'email invalide'
                        ]);
                        exit;
                    }

                    $user = $this->userModel->getUserProfile($_SESSION['user_id']);
                    $user['email'] = $email;
                    $this->view('user/profile', [
                        'error' => 'Format d\'email invalide',
                        'user' => $user
                    ]);
                    return;
                }

                // Check if email already exists for another user
                $existingEmail = $this->userModel->getUserByEmail($email);
                if ($existingEmail && $existingEmail['id'] != $_SESSION['user_id']) {
                    // Always send JSON for /user/update path
                    if ($_SERVER['REQUEST_URI'] == '/user/update') {
                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => false,
                            'message' => 'Cette adresse email est déjà utilisée par un autre utilisateur'
                        ]);
                        exit;
                    }

                    $user = $this->userModel->getUserProfile($_SESSION['user_id']);
                    $user['email'] = $email;
                    $this->view('user/profile', [
                        'error' => 'Cette adresse email est déjà utilisée par un autre utilisateur',
                        'user' => $user
                    ]);
                    return;
                }

                // Prepare data for update
                $updateData = [
                    'nom' => $nom,
                    'prenom' => $prenom,
                    'email' => $email,
                    'telephone' => $telephone
                ];

                // Update user profile in database
                $success = $this->userModel->updateUser($_SESSION['user_id'], $updateData);

                if ($success) {
                    // Update session variables
                    $_SESSION['nom'] = $nom;
                    $_SESSION['prenom'] = $prenom;
                    $_SESSION['email'] = $email;

                    // Return JSON response for AJAX requests
                    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => true,
                            'message' => 'Profil mis à jour avec succès'
                        ]);
                        exit;
                    }

                    // Redirect with success message
                    $this->redirect('/profil?success=' . urlencode('Profil mis à jour avec succès'));
                } else {
                    // Return JSON response for AJAX requests
                    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => false,
                            'message' => 'Erreur lors de la mise à jour du profil'
                        ]);
                        exit;
                    }

                    // Redirect with error message
                    $this->redirect('/profil?error=' . urlencode('Erreur lors de la mise à jour du profil'));
                }
            } catch (Exception $e) {
                error_log("Exception in profile update: " . $e->getMessage());

                // Return JSON response for AJAX requests
                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => false,
                        'message' => 'Une erreur est survenue lors de la mise à jour du profil'
                    ]);
                    exit;
                }

                // Redirect with error message
                $this->redirect('/profil?error=' . urlencode('Une erreur est survenue lors de la mise à jour du profil'));
            }
        } else {
            $this->redirect('/profil');
        }
    }

    // Change password
    public function changePassword() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                $currentPassword = $_POST['current_password'] ?? '';
                $newPassword = $_POST['new_password'] ?? '';
                $confirmPassword = $_POST['confirm_password'] ?? '';

                // Validate input
                if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                    // Always send JSON for /user/changePassword path
                    if ($_SERVER['REQUEST_URI'] == '/user/changePassword') {
                        header('Content-Type: application/json');
                        echo json_encode(['success' => false, 'message' => 'Tous les champs sont obligatoires']);
                        exit;
                    }

                    $this->view('user/change_password', [
                        'error' => 'Tous les champs sont obligatoires'
                    ]);
                    return;
                }

                if ($newPassword !== $confirmPassword) {
                    // Always send JSON for /user/changePassword path
                    if ($_SERVER['REQUEST_URI'] == '/user/changePassword') {
                        header('Content-Type: application/json');
                        echo json_encode(['success' => false, 'message' => 'Les nouveaux mots de passe ne correspondent pas']);
                        exit;
                    }

                    $this->view('user/change_password', [
                        'error' => 'Les nouveaux mots de passe ne correspondent pas'
                    ]);
                    return;
                }

                // Validate password strength
                $passwordValidation = $this->validatePasswordStrength($newPassword);
                if (!$passwordValidation['valid']) {
                    // Always send JSON for /user/changePassword path
                    if ($_SERVER['REQUEST_URI'] == '/user/changePassword') {
                        header('Content-Type: application/json');
                        echo json_encode(['success' => false, 'message' => $passwordValidation['message']]);
                        exit;
                    }

                    $this->view('user/change_password', [
                        'error' => $passwordValidation['message']
                    ]);
                    return;
                }

                // Verify current password and update
                $user = $this->userModel->getUserById($_SESSION['user_id']);
                if (!$user || !password_verify($currentPassword, $user['password'])) {
                    // Always send JSON for /user/changePassword path
                    if ($_SERVER['REQUEST_URI'] == '/user/changePassword') {
                        header('Content-Type: application/json');
                        echo json_encode(['success' => false, 'message' => 'Mot de passe actuel incorrect']);
                        exit;
                    }

                    $this->view('user/change_password', [
                        'error' => 'Mot de passe actuel incorrect'
                    ]);
                    return;
                }

                // Update password in database
                $success = $this->userModel->changePassword($_SESSION['user_id'], $newPassword);

                // Return JSON response for AJAX requests or /user/changePassword route
                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest' ||
                    $_SERVER['REQUEST_URI'] == '/user/changePassword') {
                    header('Content-Type: application/json');
                    if ($success) {
                        echo json_encode(['success' => true, 'message' => 'Mot de passe mis à jour avec succès']);
                    } else {
                        echo json_encode(['success' => false, 'message' => 'Erreur lors de la mise à jour du mot de passe']);
                    }
                    exit;
                }

                // For non-AJAX requests to other URLs
                if ($success) {
                    $this->view('user/profile', [
                        'success' => 'Mot de passe mis à jour avec succès',
                        'user' => $this->userModel->getUserProfile($_SESSION['user_id'])
                    ]);
                } else {
                    $this->view('user/profile', [
                        'error' => 'Erreur lors de la mise à jour du mot de passe',
                        'user' => $this->userModel->getUserProfile($_SESSION['user_id'])
                    ]);
                }
            } catch (Exception $e) {
                error_log("Exception in changePassword: " . $e->getMessage());

                // Return JSON response for AJAX requests
                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => 'Une erreur est survenue']);
                    exit;
                }

                $this->view('user/profile', [
                    'error' => 'Une erreur est survenue lors du changement de mot de passe',
                    'user' => $this->userModel->getUserProfile($_SESSION['user_id'])
                ]);
            }
        } else {
            $this->redirect('/profil');
        }
    }

    // Create a new user (admin only)
    public function create() {
        // Check if user is admin
        if ($_SESSION['role'] !== 'admin') {
            $this->redirect('/home');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $matricule_rh = trim($_POST['matricule_rh'] ?? '');
            $login = $_POST['login'] ?? '';
            $password = $_POST['password'] ?? '';
            $nom = $_POST['nom'] ?? '';
            $prenom = $_POST['prenom'] ?? '';
            $email = $_POST['email'] ?? '';
            $role = $_POST['role'] ?? '';
            $departement = $_POST['departement'] ?? '';
            $poste = $_POST['poste'] ?? '';
            $telephone = $_POST['telephone'] ?? '';
            $date_entree = $_POST['date_entree'] ?? '';

            // Validate required input
            if (empty($login) || empty($password) || empty($nom) || empty($prenom) || empty($email) || empty($role) || empty($departement)) {
                $this->view('admin/create_user', [
                    'error' => 'Tous les champs obligatoires doivent être remplis (nom, prénom, login, email, mot de passe, rôle, département)',
                    'data' => $_POST
                ]);
                return;
            }

            // Validate email format
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $this->view('admin/create_user', [
                    'error' => 'Format d\'email invalide',
                    'data' => $_POST
                ]);
                return;
            }

            // Validate password strength
            $passwordValidation = $this->validatePasswordStrength($password);
            if (!$passwordValidation['valid']) {
                $this->view('admin/create_user', [
                    'error' => $passwordValidation['message'],
                    'data' => $_POST
                ]);
                return;
            }

            // Check if email already exists (primary validation)
            if ($this->userModel->emailExists($email)) {
                $this->view('admin/create_user', [
                    'error' => 'Cette adresse email existe déjà. L\'email est maintenant l\'identifiant principal de connexion.',
                    'data' => $_POST
                ]);
                return;
            }

            // Check if login already exists (for backward compatibility)
            if ($this->userModel->loginExists($login)) {
                $this->view('admin/create_user', [
                    'error' => 'Ce nom d\'utilisateur existe déjà',
                    'data' => $_POST
                ]);
                return;
            }

            // Generate matricule_rh if not provided
            if (empty($matricule_rh)) {
                $matricule_rh = $this->generateMatriculeRH($role, $nom, $prenom);
            }

            // Check if matricule_rh already exists
            $existingMatricule = $this->userModel->getUserByMatricule($matricule_rh);
            if ($existingMatricule) {
                $this->view('admin/create_user', [
                    'error' => 'Ce matricule RH existe déjà: ' . $matricule_rh,
                    'data' => $_POST
                ]);
                return;
            }

            // Create new user with all required fields
            $userData = [
                'matricule_rh' => $matricule_rh,
                'login' => $login,
                'password' => $password, // Will be hashed in model
                'nom' => $nom,
                'prenom' => $prenom,
                'email' => $email,
                'role' => $role,
                'departement' => $departement, // Required field
                'date_creation' => date('Y-m-d H:i:s'),
                'actif' => 1
            ];

            // Add optional fields if provided
            if (!empty($poste)) $userData['poste'] = $poste;
            if (!empty($telephone)) $userData['telephone'] = $telephone;
            if (!empty($date_entree)) $userData['date_entree'] = $date_entree;

            $success = $this->userModel->createUser($userData);

            if ($success) {
                $this->view('admin/create_user', [
                    'success' => 'Utilisateur créé avec succès'
                ]);
            } else {
                $this->view('admin/create_user', [
                    'error' => 'Erreur lors de la création de l\'utilisateur',
                    'data' => $_POST
                ]);
            }
        } else {
            $this->view('admin/create_user');
        }
    }

    /**
     * Handle profile photo uploads
     */
    public function uploadPhoto() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            // Return JSON response for non-POST requests
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Méthode non autorisée']);
                exit;
            }
            $this->redirect('/profil');
            return;
        }

        // Check if user has permission to edit profile information
        if (!Auth::hasPermission('edit_profile_information')) {
            // Return JSON response for AJAX requests
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'Vous n\'avez pas l\'autorisation de modifier votre photo de profil'
                ]);
                exit;
            }

            // For non-AJAX requests, redirect with error
            $this->redirect('/profil?error=' . urlencode('Vous n\'avez pas l\'autorisation de modifier votre photo de profil'));
            return;
        }

        try {
            // Log upload attempt for debugging
            error_log('Upload photo attempt: ' . json_encode($_FILES));

            // Check if file was uploaded properly
            if (!isset($_FILES['photo']) || $_FILES['photo']['error'] !== UPLOAD_ERR_OK) {
                $errorMessage = 'Erreur lors du téléchargement de l\'image';

                // Provide more specific error messages based on the upload error code
                if (isset($_FILES['photo'])) {
                    switch ($_FILES['photo']['error']) {
                        case UPLOAD_ERR_INI_SIZE:
                            $errorMessage = 'L\'image est trop grande (dépasse la limite du serveur)';
                            break;
                        case UPLOAD_ERR_FORM_SIZE:
                            $errorMessage = 'L\'image est trop grande (dépasse la limite du formulaire)';
                            break;
                        case UPLOAD_ERR_PARTIAL:
                            $errorMessage = 'L\'image n\'a été que partiellement téléchargée';
                            break;
                        case UPLOAD_ERR_NO_FILE:
                            $errorMessage = 'Aucune image n\'a été téléchargée';
                            break;
                    }
                }

                // Return JSON response for AJAX requests
                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => $errorMessage]);
                    exit;
                }

                $user = $this->userModel->getUserProfile($_SESSION['user_id']);
                $this->view('user/profile', ['error' => $errorMessage, 'user' => $user]);
                return;
            }

            // Validate file type
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            $fileInfo = finfo_open(FILEINFO_MIME_TYPE);
            $fileType = finfo_file($fileInfo, $_FILES['photo']['tmp_name']);

            if (!in_array($fileType, $allowedTypes)) {
                // Return JSON response for AJAX requests
                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => 'Type de fichier non autorisé. Utilisez JPG, PNG ou GIF.']);
                    exit;
                }

                $user = $this->userModel->getUserProfile($_SESSION['user_id']);
                $this->view('user/profile', [
                    'error' => 'Type de fichier non autorisé. Utilisez JPG, PNG ou GIF.',
                    'user' => $user
                ]);
                return;
            }

            // Use a consistent path for uploads
            error_log('SERVER_ROOT: ' . $_SERVER['DOCUMENT_ROOT']);
            error_log('ROOT_PATH: ' . ROOT_PATH);

            // Define the upload directory - use assets/uploads for consistency with frontend
            $uploadDir = ROOT_PATH . '/public/assets/uploads/profile_photos/';
            $webAccessPath = '/assets/uploads/profile_photos/';

            // Create directory if it doesn't exist
            if (!file_exists($uploadDir)) {
                if (!mkdir($uploadDir, 0777, true)) {
                    error_log('Failed to create directory: ' . $uploadDir);
                    throw new Exception('Failed to create upload directory');
                }
                error_log('Created directory: ' . $uploadDir);
            }

            // Generate unique filename
            $fileExtension = pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION);
            $fileName = 'user_' . $_SESSION['user_id'] . '_' . time() . '.' . $fileExtension;
            $filePath = $uploadDir . $fileName;

            error_log('Attempting to upload file to: ' . $filePath);

            // Make sure the upload directory is writable
            if (!is_writable($uploadDir)) {
                chmod($uploadDir, 0777);
                error_log('Changed permissions on upload directory to 0777');
            }

            // Move the uploaded file
            error_log('Moving uploaded file from ' . $_FILES['photo']['tmp_name'] . ' to ' . $filePath);

            // Ensure the uploaded file exists and is readable
            if (!is_readable($_FILES['photo']['tmp_name'])) {
                error_log('Uploaded file is not readable: ' . $_FILES['photo']['tmp_name']);
                throw new Exception('Uploaded file is not readable');
            }

            // Check if destination directory is writable
            if (!is_writable(dirname($filePath))) {
                error_log('Destination directory is not writable: ' . dirname($filePath));
                error_log('Directory permissions: ' . substr(sprintf('%o', fileperms(dirname($filePath))), -4));
                error_log('Directory owner: ' . posix_getpwuid(fileowner(dirname($filePath)))['name']);
                error_log('Directory group: ' . posix_getgrgid(filegroup(dirname($filePath)))['name']);
                error_log('Web server user: ' . posix_getpwuid(posix_geteuid())['name']);
                throw new Exception('Destination directory is not writable');
            }

            // Try to move the file
            $moveResult = move_uploaded_file($_FILES['photo']['tmp_name'], $filePath);
            error_log('Move result: ' . ($moveResult ? 'success' : 'failure'));

            // Log detailed error information if move fails
            if (!$moveResult) {
                error_log('Upload error details: ' . print_r($_FILES['photo'], true));
                error_log('PHP upload error code: ' . $_FILES['photo']['error']);
                error_log('PHP error: ' . error_get_last()['message'] ?? 'No error message');
                error_log('Target path: ' . $filePath);
                error_log('File exists: ' . (file_exists($_FILES['photo']['tmp_name']) ? 'Yes' : 'No'));
                error_log('Temp file readable: ' . (is_readable($_FILES['photo']['tmp_name']) ? 'Yes' : 'No'));
            }

            if ($moveResult) {
                error_log('File moved successfully to: ' . $filePath);

                // Make sure the file is readable by web server
                chmod($filePath, 0644);

                // Update user record with new photo path - using web accessible path
                $relativePath = $webAccessPath . $fileName;
                error_log('Setting photo_profil to: ' . $relativePath);
                $success = $this->userModel->updateUser($_SESSION['user_id'], ['photo_profil' => $relativePath]);

                if ($success) {
                    // Return JSON response for AJAX requests
                    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => true,
                            'message' => 'Photo de profil mise à jour avec succès',
                            'photoUrl' => $relativePath
                        ]);
                        exit;
                    }

                    $user = $this->userModel->getUserProfile($_SESSION['user_id']);
                    $this->view('user/profile', [
                        'success' => 'Photo de profil mise à jour avec succès',
                        'user' => $user
                    ]);
                } else {
                    // Return JSON response for AJAX requests
                    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                        header('Content-Type: application/json');
                        echo json_encode(['success' => false, 'message' => 'Erreur lors de la mise à jour de la photo de profil']);
                        exit;
                    }

                    $user = $this->userModel->getUserProfile($_SESSION['user_id']);
                    $this->view('user/profile', [
                        'error' => 'Erreur lors de la mise à jour de la photo de profil',
                        'user' => $user
                    ]);
                }
            } else {
                // Log detailed error information
                error_log('Failed to move uploaded file. PHP error: ' . error_get_last()['message'] ?? 'No error message');
                error_log('Source file exists: ' . (file_exists($_FILES['photo']['tmp_name']) ? 'Yes' : 'No'));
                error_log('Source file permissions: ' . substr(sprintf('%o', fileperms($_FILES['photo']['tmp_name'])), -4));
                error_log('Destination directory exists: ' . (file_exists(dirname($filePath)) ? 'Yes' : 'No'));
                error_log('Destination directory permissions: ' . substr(sprintf('%o', fileperms(dirname($filePath))), -4));

                // Try an alternative approach with copy
                error_log('Attempting to copy file instead of move_uploaded_file');
                if (copy($_FILES['photo']['tmp_name'], $filePath)) {
                    error_log('File copied successfully to: ' . $filePath);

                    // Make sure the file is readable by web server
                    chmod($filePath, 0644);

                    // Update user record with new photo path - using web accessible path
                    $relativePath = $webAccessPath . $fileName;
                    error_log('Setting photo_profil to: ' . $relativePath);
                    $success = $this->userModel->updateUser($_SESSION['user_id'], ['photo_profil' => $relativePath]);

                    if ($success) {
                        // Return JSON response for AJAX requests
                        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                            header('Content-Type: application/json');
                            echo json_encode([
                                'success' => true,
                                'message' => 'Photo de profil mise à jour avec succès (via copy)',
                                'photoUrl' => $relativePath
                            ]);
                            exit;
                        }

                        $user = $this->userModel->getUserProfile($_SESSION['user_id']);
                        $this->view('user/profile', [
                            'success' => 'Photo de profil mise à jour avec succès',
                            'user' => $user
                        ]);
                        return;
                    }
                } else {
                    error_log('Copy also failed. PHP error: ' . error_get_last()['message'] ?? 'No error message');
                }

                // Return JSON response for AJAX requests
                if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => 'Erreur lors du déplacement du fichier téléchargé']);
                    exit;
                }

                $user = $this->userModel->getUserProfile($_SESSION['user_id']);
                $this->view('user/profile', [
                    'error' => 'Erreur lors du déplacement du fichier téléchargé',
                    'user' => $user
                ]);
            }
        } catch (Exception $e) {
            $errorMessage = $e->getMessage();
            error_log("Exception in uploadPhoto: " . $errorMessage);
            error_log("Exception trace: " . $e->getTraceAsString());

            // Return JSON response for AJAX requests
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'Une erreur est survenue lors du téléchargement de la photo',
                    'debug' => $errorMessage  // Only in development, remove in production
                ]);
                exit;
            }

            $user = $this->userModel->getUserProfile($_SESSION['user_id']);
            $this->view('user/profile', [
                'error' => 'Une erreur est survenue lors du téléchargement de la photo',
                'user' => $user
            ]);
        }
    }

    /**
     * Generate a unique matricule RH based on role and user info
     */
    private function generateMatriculeRH($role, $nom, $prenom) {
        // Define prefixes based on role
        $prefixes = [
            'employe' => 'EMP',
            'responsable' => 'RSC',
            'planificateur' => 'PLN',
            'admin' => 'ADM'
        ];

        $prefix = $prefixes[$role] ?? 'EMP';

        // Try to generate a unique matricule
        $attempts = 0;
        $maxAttempts = 100;

        do {
            $attempts++;

            if ($attempts === 1) {
                // First attempt: use initials + random number
                $initials = strtoupper(substr($prenom, 0, 1) . substr($nom, 0, 1));
                $number = str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
                $matricule = $prefix . '-' . $initials . $number;
            } else {
                // Subsequent attempts: use prefix + sequential number
                $number = str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT);
                $matricule = $prefix . '-' . $number;
            }

            // Check if this matricule already exists
            $existing = $this->userModel->getUserByMatricule($matricule);

        } while ($existing && $attempts < $maxAttempts);

        if ($attempts >= $maxAttempts) {
            // Fallback: use timestamp
            $matricule = $prefix . '-' . time();
        }

        return $matricule;
    }

    /**
     * Validate password strength according to security requirements
     *
     * @param string $password
     * @return array
     */
    private function validatePasswordStrength($password) {
        $errors = [];

        // Check minimum length (8 characters)
        if (strlen($password) < 8) {
            $errors[] = 'au moins 8 caractères';
        }

        // Check for letters (both uppercase and lowercase)
        if (!preg_match('/[a-z]/', $password) || !preg_match('/[A-Z]/', $password)) {
            $errors[] = 'des lettres majuscules et minuscules';
        }

        // Check for numbers
        if (!preg_match('/\d/', $password)) {
            $errors[] = 'au moins un chiffre';
        }

        // Check for special characters
        if (!preg_match('/[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]/', $password)) {
            $errors[] = 'au moins un caractère spécial';
        }

        if (empty($errors)) {
            return ['valid' => true, 'message' => ''];
        } else {
            $message = 'Le mot de passe doit contenir ' . implode(', ', $errors) . '.';
            return ['valid' => false, 'message' => $message];
        }
    }
}
