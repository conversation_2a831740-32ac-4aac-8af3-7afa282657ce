<?php
// Responsable Controller

class ResponsableController extends Controller {

    private $demandeModel;
    private $userModel;
    private $notificationModel;

    public function __construct() {
        // Check if user is logged in and is a responsable
        Auth::requireRole('responsable');

        $this->demandeModel = new DemandeModel();
        $this->userModel = new UserModel();
        $this->notificationModel = new NotificationModel();
    }

    // Check if the employee is managed by this responsable
    private function isEmployeeManagedByResponsable($employeeId) {
        return $this->userModel->isUserManagedByResponsable($employeeId, $_SESSION['user_id']);
    }

    public function dashboard() {
        // Get real data for the dashboard
        $responsableId = $_SESSION['user_id'];

        // Get pending requests count
        $pendingRequests = $this->demandeModel->getPendingDemandesForApproval($responsableId);
        $pendingRequestsCount = count($pendingRequests);

        // Get team members
        $teamMembers = $this->userModel->getTeamMembers($responsableId);
        $teamMembersCount = count($teamMembers);

        // Get upcoming absences
        $upcomingAbsences = [];
        foreach ($teamMembers as $member) {
            if (!empty($member['prochaineAbsence'])) {
                // Get the absence details
                $stmt = $this->demandeModel->db->prepare("
                    SELECT d.*, u.nom, u.prenom
                    FROM demandes_conges d
                    JOIN users u ON d.user_id = u.id
                    WHERE d.user_id = ? AND d.date_debut = ? AND d.statut = 'acceptee'
                ");
                $stmt->execute([$member['id'], $member['prochaineAbsence']]);
                $absence = $stmt->fetch();

                if ($absence) {
                    $absence['type'] = $this->demandeModel->formatLeaveType($absence['type']);
                    $upcomingAbsences[] = $absence;
                }
            }
        }

        // Get unavailable members (currently on leave)
        $unavailableMembers = 0;
        $today = date('Y-m-d');
        $stmt = $this->demandeModel->db->prepare("
            SELECT COUNT(DISTINCT d.user_id) as count
            FROM demandes_conges d
            JOIN users u ON d.user_id = u.id
            WHERE u.manager_id = ?
            AND d.statut = 'acceptee'
            AND ? BETWEEN d.date_debut AND d.date_fin
        ");
        $stmt->execute([$responsableId, $today]);
        $unavailableMembers = $stmt->fetch()['count'];

        // Get requests to approve (most recent 5)
        $requestsToApprove = array_slice($pendingRequests, 0, 5);

        $data = [
            'pendingRequests' => $pendingRequestsCount,
            'teamMembers' => $teamMembersCount,
            'unavailableMembers' => $unavailableMembers,
            'upcomingAbsences' => $upcomingAbsences,
            'requestsToApprove' => $requestsToApprove
        ];

        $this->view('responsable/dashboard', $data);
    }

    // Get all pending leave requests for approval
    public function demandes_approbation() {
        $responsableId = $_SESSION['user_id'];

        // Get filter parameters
        $employeeFilter = $_GET['employee'] ?? 'all';
        $typeFilter = $_GET['type'] ?? 'all';
        $dateFilter = $_GET['date'] ?? 'all'; // recent, week, month, all

        // Get team members for dropdown filter
        $teamMembers = $this->userModel->getTeamMembers($responsableId);

        // Get pending leave requests with filters
        $pendingDemandes = $this->demandeModel->getPendingDemandesForApprovalWithFilters(
            $responsableId,
            $employeeFilter,
            $typeFilter,
            $dateFilter
        );

        // Count requests by type
        $requestsByType = [
            'paye' => 0,
            'sans_solde' => 0,
            'exceptionnel' => 0,
            'familial' => 0,
            'maladie' => 0
        ];

        foreach ($pendingDemandes as $demande) {
            $type = strtolower(preg_replace('/[^a-zA-Z0-9]/', '_', $demande['type']));
            if (isset($requestsByType[$type])) {
                $requestsByType[$type]++;
            }
        }

        $data = [
            'demandes' => $pendingDemandes,
            'teamMembers' => $teamMembers,
            'requestsByType' => $requestsByType,
            'employeeFilter' => $employeeFilter,
            'typeFilter' => $typeFilter,
            'dateFilter' => $dateFilter,
            'success' => $_GET['success'] ?? null
        ];

        $this->view('responsable/demandes_approbation', $data);
    }

    // Get history of processed leave requests
    public function historique_demandes() {
        $responsableId = $_SESSION['user_id'];

        // Get filter parameters
        $period = $_GET['period'] ?? null;
        $status = $_GET['status'] ?? null;
        $employeeId = $_GET['employee'] ?? null;

        $demandes = $this->demandeModel->getDemandesHistoryForResponsable(
            $responsableId,
            $period,
            $status,
            $employeeId
        );

        // Get team members for the filter dropdown
        $teamMembers = $this->userModel->getTeamMembers($responsableId);

        $data = [
            'demandes' => $demandes,
            'teamMembers' => $teamMembers,
            'currentPeriod' => $period,
            'currentStatus' => $status,
            'currentEmployee' => $employeeId
        ];

        $this->view('responsable/historique_demandes', $data);
    }

    // View team members
    public function team_members() {
        $responsableId = $_SESSION['user_id'];

        // Get filter parameters
        $departmentFilter = $_GET['department'] ?? 'all';
        $statusFilter = $_GET['status'] ?? 'all'; // active, inactive, all

        // Get team members with detailed information
        $teamMembers = $this->userModel->getTeamMembersWithDetails($responsableId, $departmentFilter, $statusFilter);

        // Get departments for the filter dropdown
        $departments = [];
        foreach ($teamMembers as $member) {
            if (!empty($member['departement']) && !in_array($member['departement'], $departments)) {
                $departments[] = $member['departement'];
            }
        }

        // Get leave statistics for team members
        $memberLeaveStats = [];
        foreach ($teamMembers as $member) {
            $memberLeaveStats[$member['id']] = $this->demandeModel->getUserLeaveStatistics($member['id']);
        }

        // Count members on leave today
        $today = date('Y-m-d');
        $membersOnLeave = 0;
        $membersWithPendingRequests = 0;

        foreach ($teamMembers as $member) {
            // Check if member is currently on leave
            $stmt = $this->demandeModel->db->prepare("
                SELECT COUNT(*) as count
                FROM demandes_conges
                WHERE user_id = ?
                AND statut = 'acceptee'
                AND ? BETWEEN date_debut AND date_fin
            ");
            $stmt->execute([$member['id'], $today]);
            $result = $stmt->fetch();

            if ($result && $result['count'] > 0) {
                $membersOnLeave++;
            }

            // Check if member has pending requests
            $stmt = $this->demandeModel->db->prepare("
                SELECT COUNT(*) as count
                FROM demandes_conges
                WHERE user_id = ?
                AND statut = 'en_attente_responsable'
            ");
            $stmt->execute([$member['id']]);
            $result = $stmt->fetch();

            if ($result && $result['count'] > 0) {
                $membersWithPendingRequests++;
            }
        }

        $data = [
            'teamMembers' => $teamMembers,
            'departments' => $departments,
            'departmentFilter' => $departmentFilter,
            'statusFilter' => $statusFilter,
            'memberLeaveStats' => $memberLeaveStats,
            'membersOnLeave' => $membersOnLeave,
            'membersWithPendingRequests' => $membersWithPendingRequests
        ];

        $this->view('responsable/team_members', $data);
    }

    // View team availability calendar
    public function team_availability() {
        $responsableId = $_SESSION['user_id'];

        // Get month parameter (YYYY-MM format)
        $month = $_GET['month'] ?? date('Y-m');

        $availabilityData = $this->demandeModel->getTeamAvailabilityData($responsableId, $month);

        $data = [
            'availabilityData' => $availabilityData,
            'currentMonth' => $month
        ];

        $this->view('responsable/team_availability', $data);
    }

    // View statistics
    public function statistiques() {
        $responsableId = $_SESSION['user_id'];
        $stats = $this->demandeModel->getResponsableStatistics($responsableId);

        // Make sure we have the demandesByMonth array in the correct format for the chart
        if (!isset($stats['demandesByMonth']) || !is_array($stats['demandesByMonth'])) {
            $stats['demandesByMonth'] = array_fill(0, 12, 0);
        }

        // Ensure we have all the required statistics
        $requiredStats = [
            'totalDemandes', 'demandesApprouvees', 'demandesRejetees',
            'demandesEnAttente', 'pourcentageApprouve', 'averageTeamAvailability'
        ];

        foreach ($requiredStats as $stat) {
            if (!isset($stats[$stat])) {
                $stats[$stat] = 0;
            }
        }

        $this->view('responsable/statistiques', $stats);
    }

    // Approve a leave request
    public function approuverDemande() {
        // Check if user is logged in and is a responsable
        if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'responsable') {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Vous n\'avez pas les droits nécessaires pour effectuer cette action.'
            ];
            $this->redirect('/home');
            return;
        }

        $id = $_GET['id'] ?? 0;

        if (empty($id)) {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'ID de demande invalide.'
            ];
            $this->redirect('/responsable/demandes_approbation');
            return;
        }

        // Check if the demande exists
        $stmt = $this->demandeModel->db->prepare("
            SELECT d.*, u.manager_id, u.nom, u.prenom,
                   DATEDIFF(d.date_fin, d.date_debut) + 1 as nbJours,
                   u.email
            FROM demandes_conges d
            JOIN users u ON d.user_id = u.id
            WHERE d.id = ?
        ");
        $stmt->execute([$id]);
        $demande = $stmt->fetch();

        if (!$demande) {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'La demande n\'existe pas.'
            ];
            $this->redirect('/responsable/demandes_approbation');
            return;
        }

        // Check if the responsable is authorized to approve this demande
        if ($demande['manager_id'] != $_SESSION['user_id']) {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Vous n\'êtes pas autorisé à approuver cette demande.'
            ];
            $this->redirect('/responsable/demandes_approbation');
            return;
        }

        // Check if the demande is still pending responsable approval
        if ($demande['statut'] !== 'en_attente_responsable') {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Cette demande a déjà été traitée ou n\'est pas en attente d\'approbation responsable.'
            ];
            $this->redirect('/responsable/demandes_approbation');
            return;
        }

        // Approve the request in the database (first step of two-step approval)
        $success = $this->demandeModel->approveDemandeByResponsable($id, $_SESSION['user_id']);

        if ($success) {
            // Create a more detailed success message
            $_SESSION['flash_message'] = [
                'type' => 'success',
                'message' => 'Vous avez approuvé la demande de congé de ' . $demande['prenom'] . ' ' . $demande['nom'] .
                             ' pour ' . $demande['nbJours'] . ' jour(s) du ' . date('d/m/Y', strtotime($demande['date_debut'])) .
                             ' au ' . date('d/m/Y', strtotime($demande['date_fin'])) . '. La demande est maintenant en attente d\'approbation finale par un planificateur.'
            ];

            // You could add code here to send an email notification to the employee

        } else {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Une erreur est survenue lors de l\'approbation de la demande.'
            ];
        }

        $this->redirect('/responsable/demandes_approbation');
    }

    // Reject a leave request
    public function rejeterDemande() {
        // Check if user is logged in and is a responsable
        if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'responsable') {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Vous n\'avez pas les droits nécessaires pour effectuer cette action.'
            ];
            $this->redirect('/home');
            return;
        }

        $id = $_GET['id'] ?? 0;

        if (empty($id)) {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'ID de demande invalide.'
            ];
            $this->redirect('/responsable/demandes_approbation');
            return;
        }

        // Get the demande details with employee information
        $stmt = $this->demandeModel->db->prepare("
            SELECT d.*, u.nom, u.prenom, u.manager_id, u.email,
                   DATEDIFF(d.date_fin, d.date_debut) + 1 as nbJours
            FROM demandes_conges d
            JOIN users u ON d.user_id = u.id
            WHERE d.id = ?
        ");
        $stmt->execute([$id]);
        $demande = $stmt->fetch();

        if (!$demande) {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'La demande n\'existe pas.'
            ];
            $this->redirect('/responsable/demandes_approbation');
            return;
        }

        // Check if the responsable is authorized to reject this demande
        if ($demande['manager_id'] != $_SESSION['user_id']) {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Vous n\'êtes pas autorisé à rejeter cette demande.'
            ];
            $this->redirect('/responsable/demandes_approbation');
            return;
        }

        // Check if the demande is still pending responsable approval
        if ($demande['statut'] !== 'en_attente_responsable') {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Cette demande a déjà été traitée ou n\'est pas en attente d\'approbation responsable.'
            ];
            $this->redirect('/responsable/demandes_approbation');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $motifRejet = $_POST['motif_rejet'] ?? '';

            if (empty($motifRejet)) {
                $this->view('responsable/rejet_demande', [
                    'error' => 'Veuillez indiquer un motif de rejet',
                    'id' => $id,
                    'demande' => $demande
                ]);
                return;
            }

            // Reject the request in the database
            $success = $this->demandeModel->rejectDemande($id, $_SESSION['user_id'], $motifRejet);

            if ($success) {
                // Create a more detailed success message
                $_SESSION['flash_message'] = [
                    'type' => 'success',
                    'message' => 'Vous avez rejeté la demande de congé de ' . $demande['prenom'] . ' ' . $demande['nom'] .
                                 ' pour ' . $demande['nbJours'] . ' jour(s) du ' . date('d/m/Y', strtotime($demande['date_debut'])) .
                                 ' au ' . date('d/m/Y', strtotime($demande['date_fin'])) . '.'
                ];

                // You could add code here to send an email notification to the employee

            } else {
                $_SESSION['flash_message'] = [
                    'type' => 'error',
                    'message' => 'Une erreur est survenue lors du rejet de la demande.'
                ];
            }

            $this->redirect('/responsable/demandes_approbation');
        } else {
            // Format the demande for display
            if ($demande) {
                $demande['type'] = $this->demandeModel->formatLeaveType($demande['type']);

                // Calculate the number of days
                $dateDebut = new DateTime($demande['date_debut']);
                $dateFin = new DateTime($demande['date_fin']);
                $interval = $dateDebut->diff($dateFin);
                $demande['nbJours'] = $interval->days + 1; // +1 because the end date is inclusive
            }

            // Display rejection form
            $this->view('responsable/rejet_demande', [
                'id' => $id,
                'demande' => $demande
            ]);
        }
    }

    // Manage Responsable's own leave requests
    public function mes_demandes() {
        // Redirect to the shared DemandeController's liste method
        // The view will be automatically selected based on the URL path
        $demandeController = new DemandeController();
        $demandeController->liste();
    }

    // Create a new leave request for the Responsable
    public function nouvelle_demande() {
        // Redirect to the shared DemandeController's nouvelle method
        // The view will be automatically selected based on the URL path
        $demandeController = new DemandeController();
        $demandeController->nouvelle();
    }
}
