<?php
// Controller for statistics

class StatistiqueController extends Controller {
    private $demandeModel;
    private $leaveBalanceModel;

    public function __construct() {
        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('/home');
            exit();
        }

        // Load models
        $this->demandeModel = new DemandeModel();
        $this->leaveBalanceModel = new LeaveBalanceModel();
    }

    public function index() {
        // Get statistics data based on role
        $role = $_SESSION['role'];
        $userId = $_SESSION['user_id'];

        // Prepare data structure for statistics
        $data = [];

        // Different statistics per role
        switch ($role) {
        case 'admin':
            $globalStats = $this->demandeModel->getGlobalStatistics();
            $monthlyStats = $this->demandeModel->getMonthlyStatsForAllUsers();
            $typeDistribution = $this->demandeModel->getTypeDistributionForAllUsers();
            $data = [
                'totalDemandes' => $globalStats['total'],
                'demandesApprouvees' => $globalStats['approved'],
                'demandesRejetees' => $globalStats['rejected'],
                'demandesEnAttente' => $globalStats['pending'],
                'pourcentageApprouve' => $globalStats['approval_rate'],
                'demandesParMois' => $monthlyStats,
                'demandesParType' => $typeDistribution
            ];
            $this->view('admin/statistiques', $data);
            break;
        case 'responsable':
            $teamStats = $this->demandeModel->getTeamStatistics($userId);
            $monthlyStats = $this->demandeModel->getMonthlyStatsForAllUsers();
            $data = [
                'totalDemandes' => $teamStats['total'],
                'demandesApprouvees' => $teamStats['approved'],
                'demandesRejetees' => $teamStats['rejected'],
                'demandesEnAttente' => $teamStats['pending'],
                'pourcentageApprouve' => $teamStats['approval_rate'],
                'demandesParMois' => $monthlyStats,
                // Add more team data as needed
            ];
            $this->view('responsable/statistiques', $data);
            break;
        case 'planificateur':
            // Get filter parameters from URL
            $year = isset($_GET['year']) ? intval($_GET['year']) : date('Y');

            // Get total users count
            $userModel = new UserModel();
            $totalUsers = $userModel->getTotalUsersCount();

            // Get department statistics
            $departmentStats = $this->demandeModel->getDepartmentStats($year);

            // Get monthly statistics
            $monthlyStats = $this->demandeModel->getDetailedMonthlyStats($year);

            // Get leave type statistics
            $leaveTypeStats = $this->demandeModel->getLeaveTypeStats($year);

            // Get dashboard statistics
            $dashboardStats = $this->demandeModel->getPlanificateurDashboardStats();

            $statsData = [
                'totalUsers' => $totalUsers,
                'departmentStats' => $departmentStats,
                'monthlyStats' => $monthlyStats,
                'leaveTypeStats' => $leaveTypeStats,
                'year' => $year,
                'totalDemandes' => array_sum(array_column($monthlyStats, 'requests')),
                'demandesApprouvees' => $dashboardStats['approvedRequests'],
                'demandesRejetees' => $dashboardStats['rejectedRequests'],
                'demandesEnAttente' => $dashboardStats['pendingRequests'],
                'pourcentageApprouve' => $dashboardStats['approvedRequests'] > 0 ?
                    round(($dashboardStats['approvedRequests'] / ($dashboardStats['approvedRequests'] + $dashboardStats['rejectedRequests'])) * 100) : 0
            ];

            $this->view('planificateur/statistiques', ['statsData' => $statsData]);
            break;
        case 'employe':
        default:
            $userStats = $this->demandeModel->getUserStatistics($userId);
            $monthlyStats = $this->demandeModel->getMonthlyStatistics($userId);
            $upcomingLeaves = $this->demandeModel->getUpcomingLeaves($userId);
            $recentHistory = $this->demandeModel->getRecentHistory($userId);

            // Get leave balance information from LeaveBalanceModel
            $leaveBalances = $this->leaveBalanceModel->getUserLeaveBalances($userId);

            // Initialize default values
            $congesRestants = 0;
            $congesPris = 0;

            // Sum up the remaining and used days from all leave types
            // or use only 'payé' type if you want to show only paid leave
            if (!empty($leaveBalances)) {
                foreach ($leaveBalances as $type => $balance) {
                    $congesRestants += $balance['remaining'];
                    $congesPris += $balance['used'];
                }
            }

            $data = [
                'totalDemandes' => $userStats['total'],
                'demandesApprouvees' => $userStats['approved'],
                'demandesRejetees' => $userStats['rejected'],
                'demandesEnAttente' => $userStats['pending'],
                'pourcentageApprouve' => $userStats['approval_rate'],
                'congesRestants' => $congesRestants,
                'congesPris' => $congesPris,
                'historiqueParMois' => $monthlyStats,
                'upcomingLeaves' => $upcomingLeaves,
                'recentHistory' => $recentHistory,
                'leaveBalances' => $leaveBalances // Add detailed balance information
            ];
            $this->view('employe/statistiques', $data);
            break;
    }
}
}