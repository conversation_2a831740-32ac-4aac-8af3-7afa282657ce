<?php
// Planificateur Controller

class PlanificateurController extends Controller {

    private $demandeModel;
    private $userModel;
    private $jourFerieModel;

    public function __construct() {
        // Check if user is logged in and is a planificateur
        Auth::requireRole('planificateur');

        // Initialize models
        $this->demandeModel = new DemandeModel();
        $this->userModel = new UserModel();
        $this->jourFerieModel = new JourFerieModel();
    }

    public function dashboard() {
        // Get dashboard statistics from the database
        $stats = $this->demandeModel->getPlanificateurDashboardStats();

        // Get upcoming holidays
        $upcomingHolidays = $this->jourFerieModel->getUpcomingJoursFeries(5);

        // Format holidays for display
        $nextHolidays = [];
        foreach ($upcomingHolidays as $holiday) {
            $nextHolidays[] = [
                'name' => $holiday['nom'],
                'date' => $holiday['date'],
                'pays' => $holiday['pays']
            ];
        }

        // Get team availability for the next 7 days
        $teamAvailability = $this->demandeModel->getTeamAvailability();

        // Get upcoming absences
        $upcomingAbsences = $this->demandeModel->getAllUpcomingLeaves(5);

        $data = [
            'pendingRequests' => $stats['pendingRequests'],
            'approvedRequests' => $stats['approvedRequests'],
            'rejectedRequests' => $stats['rejectedRequests'],
            'nextHolidays' => $nextHolidays,
            'teamAvailability' => $teamAvailability,
            'upcomingAbsences' => $upcomingAbsences
        ];

        $this->view('planificateur/dashboard', $data);
    }

    // Planning view with calendar of all leaves
    public function planning() {
        // Get filter parameters from URL
        $department = isset($_GET['department']) ? $_GET['department'] : 'all';
        $month = isset($_GET['month']) ? $_GET['month'] : date('Y-m');

        // Calculate start and end dates for the month
        $startDate = $month . '-01';
        $endDate = date('Y-m-t', strtotime($startDate));

        // Get approved leave requests from the database
        $events = $this->demandeModel->getAllApprovedDemandes($startDate, $endDate, $department);

        // Get holidays for the period
        $holidays = $this->jourFerieModel->getJoursFeriesByYear(date('Y', strtotime($startDate)));

        // Format holidays for FullCalendar
        $formattedHolidays = [];
        foreach ($holidays as $holiday) {
            if (strtotime($holiday['date']) >= strtotime($startDate) &&
                strtotime($holiday['date']) <= strtotime($endDate)) {
                $formattedHolidays[] = [
                    'id' => 'holiday_' . ($holiday['id'] ?? uniqid('holiday_')),
                    'title' => $holiday['nom'] ?? 'Jour férié',
                    'start' => $holiday['date'],
                    'end' => date('Y-m-d', strtotime($holiday['date'] . ' +1 day')),
                    'type' => 'public_holiday',
                    'allDay' => true
                ];
            }
        }

        // Get all departments for the filter
        $departments = $this->userModel->getAllDepartments();

        $planningData = [
            'month' => date('F Y', strtotime($startDate)),
            'events' => $events,
            'holidays' => $formattedHolidays,
            'departments' => $departments,
            'currentDepartment' => $department,
            'currentMonth' => $month
        ];

        $this->view('planificateur/planning', ['planningData' => $planningData]);
    }

    // Statistics view for the planificateur
    public function statistiques() {
        // Get filter parameters from URL
        $year = isset($_GET['year']) ? intval($_GET['year']) : date('Y');

        // Get total users count
        $totalUsers = $this->userModel->getTotalUsersCount();

        // Get department statistics
        $departmentStats = $this->demandeModel->getDepartmentStats($year);

        // Get monthly statistics
        $monthlyStats = $this->demandeModel->getDetailedMonthlyStats($year);

        // Get leave type statistics
        $leaveTypeStats = $this->demandeModel->getLeaveTypeStats($year);

        $statsData = [
            'totalUsers' => $totalUsers,
            'departmentStats' => $departmentStats,
            'monthlyStats' => $monthlyStats,
            'leaveTypeStats' => $leaveTypeStats,
            'year' => $year
        ];

        $this->view('planificateur/statistiques', ['statsData' => $statsData]);
    }

    // Get all pending leave requests for approval
    public function demandes_approbation() {
        // Get all pending leave requests
        $pendingDemandes = $this->demandeModel->getAllPendingDemandes();

        // Filter out the planificateur's own requests
        $planificateurId = $_SESSION['user_id'];
        $filteredDemandes = array_filter($pendingDemandes, function($demande) use ($planificateurId) {
            return $demande['user_id'] != $planificateurId;
        });

        $data = [
            'demandes' => $filteredDemandes,
            'success' => $_GET['success'] ?? null
        ];

        $this->view('planificateur/demandes_approbation', $data);
    }

    // Approve a leave request
    public function approuverDemande() {
        // Check if user is logged in and is a planificateur
        if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'planificateur') {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Vous n\'avez pas les droits nécessaires pour effectuer cette action.'
            ];
            $this->redirect('/home');
            return;
        }

        $id = $_GET['id'] ?? 0;

        if (empty($id)) {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'ID de demande invalide.'
            ];
            $this->redirect('/planificateur/demandes_approbation');
            return;
        }

        // Check if the demande exists and is not the planificateur's own request
        $stmt = $this->demandeModel->db->prepare("
            SELECT d.*, u.manager_id
            FROM demandes_conges d
            JOIN users u ON d.user_id = u.id
            WHERE d.id = ?
        ");
        $stmt->execute([$id]);
        $demande = $stmt->fetch();

        if (!$demande) {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'La demande n\'existe pas.'
            ];
            $this->redirect('/planificateur/demandes_approbation');
            return;
        }

        // Check if the planificateur is trying to approve their own request
        if ($demande['user_id'] == $_SESSION['user_id']) {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Vous ne pouvez pas approuver votre propre demande.'
            ];
            $this->redirect('/planificateur/demandes_approbation');
            return;
        }

        // Approve the request in the database (final step of two-step approval)
        $success = $this->demandeModel->approveDemandeByPlanificateur($id, $_SESSION['user_id']);

        if ($success) {
            $_SESSION['flash_message'] = [
                'type' => 'success',
                'message' => 'La demande a été définitivement approuvée avec succès. L\'employé a été notifié.'
            ];
        } else {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Une erreur est survenue lors de l\'approbation de la demande.'
            ];
        }

        $this->redirect('/planificateur/demandes_approbation');
    }

    // Reject a leave request
    public function rejeterDemande() {
        // Check if user is logged in and is a planificateur
        if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'planificateur') {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Vous n\'avez pas les droits nécessaires pour effectuer cette action.'
            ];
            $this->redirect('/home');
            return;
        }

        $id = $_GET['id'] ?? 0;

        if (empty($id)) {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'ID de demande invalide.'
            ];
            $this->redirect('/planificateur/demandes_approbation');
            return;
        }

        error_log("Valid demande ID: $id");

        // Get the demande details
        $stmt = $this->demandeModel->db->prepare("
            SELECT d.*, u.nom, u.prenom, u.manager_id
            FROM demandes_conges d
            JOIN users u ON d.user_id = u.id
            WHERE d.id = ?
        ");
        $stmt->execute([$id]);
        $demande = $stmt->fetch();

        if (!$demande) {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'La demande n\'existe pas.'
            ];
            $this->redirect('/planificateur/demandes_approbation');
            return;
        }

        // Check if the planificateur is trying to reject their own request
        if ($demande['user_id'] == $_SESSION['user_id']) {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Vous ne pouvez pas rejeter votre propre demande.'
            ];
            $this->redirect('/planificateur/demandes_approbation');
            return;
        }

        // Check if the demande is still pending planificateur approval
        if ($demande['statut'] !== 'en_attente_planificateur') {
            $_SESSION['flash_message'] = [
                'type' => 'error',
                'message' => 'Cette demande a déjà été traitée ou n\'est pas en attente d\'approbation planificateur.'
            ];
            $this->redirect('/planificateur/demandes_approbation');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            error_log("POST request received for rejeterDemande");
            error_log("POST data: " . json_encode($_POST));

            $motifRejet = $_POST['motif_rejet'] ?? '';
            error_log("Motif de rejet: '$motifRejet'");

            if (empty($motifRejet)) {
                error_log("Empty motif de rejet");
                $this->view('planificateur/rejet_demande', [
                    'error' => 'Veuillez indiquer un motif de rejet',
                    'id' => $id,
                    'demande' => $demande
                ]);
                return;
            }

            error_log("Valid motif de rejet, proceeding with rejection");

            // Reject the request in the database
            $success = $this->demandeModel->rejectDemande($id, $_SESSION['user_id'], $motifRejet);
            error_log("Rejection result: " . ($success ? 'success' : 'failure'));

            if ($success) {
                error_log("Setting success flash message");
                $_SESSION['flash_message'] = [
                    'type' => 'success',
                    'message' => 'La demande a été rejetée avec succès.'
                ];

                // Log the rejection
                error_log('Demande #' . $id . ' rejected by planificateur #' . $_SESSION['user_id'] . '. Motif: ' . $motifRejet);
            } else {
                error_log("Setting error flash message");
                $_SESSION['flash_message'] = [
                    'type' => 'error',
                    'message' => 'Une erreur est survenue lors du rejet de la demande.'
                ];
            }

            error_log("Redirecting to demandes_approbation");
            $this->redirect('/planificateur/demandes_approbation');
        } else {
            // Format the demande for display
            if ($demande) {
                $demande['type'] = $this->demandeModel->formatLeaveType($demande['type']);

                // Calculate the number of days
                $dateDebut = new DateTime($demande['date_debut']);
                $dateFin = new DateTime($demande['date_fin']);
                $interval = $dateDebut->diff($dateFin);
                $demande['nbJours'] = $interval->days + 1; // +1 because the end date is inclusive
            }

            // Display rejection form
            $this->view('planificateur/rejet_demande', [
                'id' => $id,
                'demande' => $demande
            ]);
        }
    }

    // Manage public holidays - View only for planificateur
    public function joursFeries() {
        // Get parameters from URL
        $year = $_GET['year'] ?? date('Y');
        $pays = $_GET['pays'] ?? null;

        // Get holidays based on filters (without search - we'll do that client-side)
        $holidays = $this->jourFerieModel->getJoursFeriesByYear($year, $pays);

        // Get all available countries
        $allPays = $this->jourFerieModel->getAllPays();

        // Map database fields to view fields
        $mappedHolidays = [];
        foreach ($holidays as $holiday) {
            $mappedHolidays[] = [
                'id' => $holiday['id'],
                'name' => $holiday['nom'],
                'date' => $holiday['date'],
                'is_recurring' => $holiday['est_recurrent'] == 1,
                'pays' => $holiday['pays']
            ];
        }

        $this->view('planificateur/jours_feries', [
            'holidays' => $mappedHolidays,
            'year' => $year,
            'pays' => $pays,
            'allPays' => $allPays,
            'success' => isset($_GET['success']),
            'deleted' => isset($_GET['deleted'])
        ]);
    }

    // View upcoming absences for all employees with complete historical information
    public function absencesAVenir() {
        // Get filter parameters
        $selectedDept = isset($_GET['departement']) ? $_GET['departement'] : 'all';
        $startDate = isset($_GET['date_debut']) ? $_GET['date_debut'] : date('Y-m-d');
        $endDate = isset($_GET['date_fin']) ? $_GET['date_fin'] : date('Y-m-d', strtotime('+3 months'));
        $selectedStatus = isset($_GET['statut']) ? $_GET['statut'] : 'all';

        // Get departments for filter
        $departments = $this->userModel->getAllDepartments();

        // Get upcoming absences with complete historical information
        $absences = $this->demandeModel->getUpcomingAbsencesWithHistory($selectedDept, $startDate, $endDate, $selectedStatus);

        $data = [
            'absences' => $absences,
            'departments' => array_values($departments),
            'selectedDept' => $selectedDept,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'selectedStatus' => $selectedStatus
        ];

        $this->view('planificateur/absences-a-venir', $data);
    }

    // Helper method to get filtered absences
    private function getFilteredAbsences($department, $startDate, $endDate, $status) {
        // Get all demandes with basic filters
        $demandes = $this->demandeModel->getAllDemandes();

        // Apply additional filters manually
        $filteredDemandes = [];
        foreach ($demandes as $demande) {
            // Skip if department filter is active and doesn't match
            if ($department && $department !== 'all') {
                // We don't have department in the result set from getAllDemandes
                // We would need to modify that method to include it
                // For now, we'll skip this filter
            }

            // Skip if date filter is active and doesn't match
            if ($startDate && $endDate) {
                $demandeStart = strtotime($demande['date_debut']);
                $demandeEnd = strtotime($demande['date_fin']);
                $filterStart = strtotime($startDate);
                $filterEnd = strtotime($endDate);

                // Skip if the demande is completely outside the filter range
                if (($demandeEnd < $filterStart) || ($demandeStart > $filterEnd)) {
                    continue;
                }
            } else if ($startDate) {
                $demandeStart = strtotime($demande['date_debut']);
                $filterStart = strtotime($startDate);

                // Skip if the demande starts before the filter date
                if ($demandeStart < $filterStart) {
                    continue;
                }
            }

            // Skip if status filter is active and doesn't match
            if ($status && $status !== 'all') {
                $demandeStatus = '';
                switch ($demande['statut']) {
                    case 'Approuvée':
                        $demandeStatus = 'acceptée';
                        break;
                    case 'En attente':
                        $demandeStatus = 'en cours';
                        break;
                    case 'Rejetée':
                        $demandeStatus = 'refusée';
                        break;
                }

                if ($demandeStatus !== $status) {
                    continue;
                }
            } else {
                // Default filter: only show approved and pending
                if ($demande['statut'] !== 'Approuvée' && $demande['statut'] !== 'En attente') {
                    continue;
                }
            }

            // Calculate duration
            $demandeStartDate = new DateTime($demande['date_debut']);
            $demandeEndDate = new DateTime($demande['date_fin']);
            $interval = $demandeStartDate->diff($demandeEndDate);
            $demande['duree'] = $interval->days + 1;

            // Format for display
            $demande['type_formatted'] = $demande['type']; // Already formatted by getAllDemandes
            $demande['statut_formatted'] = $demande['statut']; // Already formatted by getAllDemandes
            $demande['nom_complet'] = $demande['user_prenom'] . ' ' . $demande['user_nom'];

            $filteredDemandes[] = $demande;
        }

        // Sort by start date
        usort($filteredDemandes, function($a, $b) {
            return strtotime($a['date_debut']) - strtotime($b['date_debut']);
        });

        return $filteredDemandes;
    }

    // Format status for display
    private function formatStatus($status) {
        switch ($status) {
            case 'acceptée':
                return 'Approuvée';
            case 'en cours':
                return 'En attente';
            case 'refusée':
                return 'Rejetée';
            default:
                return ucfirst($status);
        }
    }
}
