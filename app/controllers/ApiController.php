<?php
// ApiController to handle API requests

class ApiController extends Controller {
    private $demandeModel;
    private $jourFerieModel;

    public function __construct() {
        // Check if user is logged in for non-API routes
        if (!isset($_SESSION['user_id']) && !$this->isApiRequest()) {
            $this->sendJsonResponse(['error' => 'Unauthorized'], 401);
            exit();
        }

        try {
            // Load models
            $this->demandeModel = new DemandeModel();
            $this->jourFerieModel = new JourFerieModel();

            // Test database connection
            if (!$this->demandeModel->db) {
                error_log("ApiController::__construct - Database connection failed");
                throw new Exception("Database connection failed");
            }

            error_log("ApiController::__construct - Database connection successful");
        } catch (Exception $e) {
            error_log("ApiController::__construct - Exception: " . $e->getMessage());
            if ($this->isApiRequest()) {
                $this->sendJsonResponse(['error' => 'Database connection error: ' . $e->getMessage()], 500);
                exit();
            }
        }
    }

    // Check if the current request is an API request
    private function isApiRequest() {
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        return strpos($uri, '/api/') !== false;
    }

    /**
     * Get absence details by ID
     *
     * @param int $id The absence ID
     * @return void
     */
    public function getAbsenceDetails($id) {
        // Debug information
        error_log("ApiController::getAbsenceDetails called with ID: " . $id);

        // Ensure ID is numeric
        if (is_array($id) && isset($id['id'])) {
            $id = $id['id'];
            error_log("ApiController::getAbsenceDetails - ID extracted from array: " . $id);
        }

        // Validate ID
        if (!is_numeric($id) || $id <= 0) {
            error_log("ApiController::getAbsenceDetails - Invalid ID: " . $id);
            $this->sendJsonResponse(['error' => 'Invalid ID'], 400);
            return;
        }

        try {
            // Get absence details from database
            error_log("ApiController::getAbsenceDetails - About to call getAbsenceById with ID: " . $id);
            $absence = $this->getAbsenceById($id);

            if (!$absence) {
                error_log("ApiController::getAbsenceDetails - Absence not found with ID: " . $id);
                $this->sendJsonResponse(['error' => 'Absence not found'], 404);
                return;
            }

            // Debug successful retrieval
            error_log("ApiController::getAbsenceDetails - Successfully retrieved absence with ID: " . $id);
            error_log("ApiController::getAbsenceDetails - Absence data: " . print_r($absence, true));

            // Return absence details as JSON
            $this->sendJsonResponse($absence);
        } catch (Exception $e) {
            error_log("ApiController::getAbsenceDetails - Exception: " . $e->getMessage());
            error_log("ApiController::getAbsenceDetails - Exception trace: " . $e->getTraceAsString());
            $this->sendJsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
        } catch (Error $e) {
            error_log("ApiController::getAbsenceDetails - PHP Error: " . $e->getMessage());
            error_log("ApiController::getAbsenceDetails - Error trace: " . $e->getTraceAsString());
            $this->sendJsonResponse(['error' => 'PHP Error: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Get absence by ID with additional details
     *
     * @param int $id The absence ID
     * @return array|false The absence details or false if not found
     */
    private function getAbsenceById($id) {
        // Get the absence from the database
        $query = "
            SELECT
                d.id, d.user_id, d.type, d.date_debut, d.date_fin, d.motif, d.statut,
                d.date_demande, d.valide_par,
                DATEDIFF(d.date_fin, d.date_debut) + 1 as duree,
                u.nom as user_nom, u.prenom as user_prenom,
                NULL as valideur_nom, NULL as valideur_prenom
            FROM demandes_conges d
            JOIN users u ON d.user_id = u.id
            WHERE d.id = ?
        ";

        error_log("ApiController::getAbsenceById - Executing query for ID: " . $id);

        try {
            // Check if demandeModel is initialized
            if (!isset($this->demandeModel) || !$this->demandeModel) {
                error_log("ApiController::getAbsenceById - demandeModel is not initialized");
                throw new Exception("DemandeModel is not initialized");
            }

            // Check if db connection is available
            if (!isset($this->demandeModel->db) || !$this->demandeModel->db) {
                error_log("ApiController::getAbsenceById - Database connection is not available");
                throw new Exception("Database connection is not available");
            }

            $stmt = $this->demandeModel->db->prepare($query);
            if (!$stmt) {
                error_log("ApiController::getAbsenceById - Failed to prepare statement: " .
                    print_r($this->demandeModel->db->errorInfo(), true));
                throw new Exception("Failed to prepare statement");
            }

            $result = $stmt->execute([$id]);
            if (!$result) {
                error_log("ApiController::getAbsenceById - Failed to execute statement: " .
                    print_r($stmt->errorInfo(), true));
                throw new Exception("Failed to execute statement");
            }

            $absence = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$absence) {
                error_log("ApiController::getAbsenceById - No absence found with ID: " . $id);
                return false;
            }

            error_log("ApiController::getAbsenceById - Found absence with ID: " . $id);
            error_log("ApiController::getAbsenceById - Raw absence data: " . print_r($absence, true));

        } catch (PDOException $e) {
            error_log("ApiController::getAbsenceById - PDO Exception: " . $e->getMessage());
            error_log("ApiController::getAbsenceById - PDO Exception trace: " . $e->getTraceAsString());
            throw $e; // Re-throw to be caught by the calling method
        } catch (Exception $e) {
            error_log("ApiController::getAbsenceById - Exception: " . $e->getMessage());
            error_log("ApiController::getAbsenceById - Exception trace: " . $e->getTraceAsString());
            throw $e; // Re-throw to be caught by the calling method
        }

        try {
            // Format the data for display
            $absence['type_formatted'] = $this->formatLeaveType($absence['type']);
            $absence['statut_formatted'] = $this->formatStatus($absence['statut']);
            $absence['nom_complet'] = $absence['user_prenom'] . ' ' . $absence['user_nom'];

            return $absence;
        } catch (Exception $e) {
            error_log("ApiController::getAbsenceById - Exception during formatting: " . $e->getMessage());
            error_log("ApiController::getAbsenceById - Exception trace: " . $e->getTraceAsString());
            throw $e; // Re-throw to be caught by the calling method
        }
    }

    /**
     * Format leave type for display
     *
     * @param string $type The leave type
     * @return string The formatted leave type
     */
    private function formatLeaveType($type) {
        switch ($type) {
            case 'payé':
                return 'Congé payé';
            case 'sans solde':
                return 'Congé sans solde';
            case 'maladie':
                return 'Congé maladie';
            case 'familial':
                return 'Congé familial';
            case 'exceptionnel':
                return 'Congé exceptionnel';
            default:
                return ucfirst($type);
        }
    }

    /**
     * Format status for display
     *
     * @param string $status The status
     * @return string The formatted status
     */
    private function formatStatus($status) {
        switch ($status) {
            case 'acceptée':
                return 'Approuvée';
            case 'en cours':
                return 'En attente';
            case 'refusée':
                return 'Rejetée';
            default:
                return ucfirst($status);
        }
    }

    /**
     * Get holidays data for the current year
     *
     * @return void
     */
    public function getHolidays() {
        try {
            // Get parameters
            $year = $_GET['year'] ?? date('Y');
            $pays = $_GET['pays'] ?? null;

            // Validate year
            if (!is_numeric($year) || $year < 2020 || $year > 2030) {
                $this->sendJsonResponse(['error' => 'Invalid year'], 400);
                return;
            }

            // Get holidays from database
            $holidays = $this->jourFerieModel->getJoursFeriesByYear($year, $pays);

            // Format holidays for frontend
            $formattedHolidays = [];
            foreach ($holidays as $holiday) {
                $formattedHolidays[] = [
                    'id' => $holiday['id'],
                    'nom' => $holiday['nom'],
                    'date' => $holiday['date'],
                    'date_formatted' => date('d/m/Y', strtotime($holiday['date'])),
                    'est_recurrent' => $holiday['est_recurrent'] == 1,
                    'pays' => $holiday['pays']
                ];
            }

            // Group by country
            $groupedHolidays = [];
            foreach ($formattedHolidays as $holiday) {
                $country = $holiday['pays'];
                if (!isset($groupedHolidays[$country])) {
                    $groupedHolidays[$country] = [];
                }
                $groupedHolidays[$country][] = $holiday;
            }

            $this->sendJsonResponse([
                'success' => true,
                'year' => $year,
                'holidays' => $groupedHolidays
            ]);

        } catch (Exception $e) {
            error_log("ApiController::getHolidays - Exception: " . $e->getMessage());
            $this->sendJsonResponse(['error' => 'Server error: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Send JSON response
     *
     * @param mixed $data The data to send
     * @param int $statusCode The HTTP status code
     * @return void
     */
    private function sendJsonResponse($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data);
        exit();
    }
}
