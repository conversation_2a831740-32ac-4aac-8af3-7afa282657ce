<?php
// Admin Controller

class AdminController extends Controller {
    private $userModel;
    private $demandeModel;
    private $jourFerieModel;
    private $leaveBalanceModel;
    private $departmentLeaveBalanceModel;
    private $db;

    public function __construct() {
        // Check if user is logged in and is an admin
        Auth::requireRole('admin');

        // Initialize database connection
        $this->db = Database::getInstance()->getConnection();

        // Load models
        $this->userModel = new UserModel();
        $this->demandeModel = new DemandeModel();
        $this->jourFerieModel = new JourFerieModel();
        $this->leaveBalanceModel = new LeaveBalanceModel();
        $this->departmentLeaveBalanceModel = new DepartmentLeaveBalanceModel();
    }

    public function dashboard() {
        // Get real data for the dashboard
        $totalUsers = count($this->userModel->getAllUsers());

        // Get statistics from the demandeModel
        $stats = $this->demandeModel->getGlobalStatistics();

        // Get recent users (limit to 5)
        $recentUsers = $this->userModel->getAllUsers();
        $recentUsers = array_slice($recentUsers, 0, 5);

        // Get department leave balances
        $departmentBalances = $this->departmentLeaveBalanceModel->getAllDepartmentBalances();

        // Find departments with low balance (less than 3 days)
        $lowBalanceDepartments = array_filter($departmentBalances, function($dept) {
            return $dept['remaining_days'] < 3;
        });

        $data = [
            'totalUsers' => $totalUsers,
            'totalRequests' => $stats['total'],
            'pendingRequests' => $stats['pending'],
            'recentUsers' => $recentUsers,
            'departmentBalances' => $departmentBalances,
            'lowBalanceDepartments' => $lowBalanceDepartments,
            'systemStatus' => [
                'database' => 'OK',
                'storage' => '75%',
                'lastBackup' => date('Y-m-d')
            ]
        ];

        $this->view('admin/dashboard', $data);
    }

    // List all users in the system
    public function listUsers() {
        // Get search parameters from URL
        $search = $_GET['search'] ?? '';
        $role = $_GET['role'] ?? '';

        // Get users from the database with search/filter
        $users = $this->userModel->getAllUsers($search, $role);

        $this->view('admin/list_users', ['users' => $users]);
    }

    // Edit a user
    public function editUser() {
        $id = $_GET['id'] ?? 0;

        if (empty($id)) {
            $this->redirect('/list-users');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Process form submission
            $nom = $_POST['nom'] ?? '';
            $prenom = $_POST['prenom'] ?? '';
            $email = $_POST['email'] ?? '';
            $role = $_POST['role'] ?? '';

            // Validate input
            if (empty($nom) || empty($prenom) || empty($email) || empty($role)) {
                $this->view('admin/edit_user', [
                    'error' => 'Tous les champs obligatoires doivent être remplis',
                    'user' => [
                        'id' => $id,
                        'nom' => $nom,
                        'prenom' => $prenom,
                        'email' => $email,
                        'role' => $role
                    ]
                ]);
                return;
            }

            // Validate email format
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $this->view('admin/edit_user', [
                    'error' => 'Format d\'email invalide',
                    'user' => [
                        'id' => $id,
                        'nom' => $nom,
                        'prenom' => $prenom,
                        'email' => $email,
                        'role' => $role
                    ]
                ]);
                return;
            }

            // Check if email already exists for another user
            if ($this->userModel->emailExists($email, $id)) {
                $this->view('admin/edit_user', [
                    'error' => 'Cette adresse email est déjà utilisée par un autre utilisateur. L\'email est maintenant l\'identifiant principal de connexion.',
                    'user' => [
                        'id' => $id,
                        'nom' => $nom,
                        'prenom' => $prenom,
                        'email' => $email,
                        'role' => $role
                    ]
                ]);
                return;
            }



            // Update user
            $success = $this->userModel->updateUser($id, [
                'nom' => $nom,
                'prenom' => $prenom,
                'email' => $email,
                'role' => $role
            ]);

            if ($success) {
                $this->redirect('/list-users?success=1');
            } else {
                $this->view('admin/edit_user', [
                    'error' => 'Erreur lors de la modification de l\'utilisateur',
                    'user' => [
                        'id' => $id,
                        'nom' => $nom,
                        'prenom' => $prenom,
                        'email' => $email,
                        'role' => $role
                    ]
                ]);
            }
        } else {
            // Get user details
            $user = $this->userModel->getUserById($id);

            if (!$user) {
                $this->redirect('/list-users');
                return;
            }

            // Display form with user details
            $this->view('admin/edit_user', ['user' => $user]);
        }
    }

    // Delete a user
    public function deleteUser() {
        $id = $_GET['id'] ?? 0;

        if (empty($id) || $id == $_SESSION['user_id']) {
            // Don't allow deleting self
            $this->redirect('/list-users');
            return;
        }

        // Delete user (soft delete)
        $success = $this->userModel->deleteUser($id);

        $this->redirect('/list-users' . ($success ? '?success=2' : '?error=1'));
    }

    // View all leave requests in the system
    public function allDemandes() {
        // Get search parameters from URL
        $search = $_GET['search'] ?? '';
        $status = $_GET['status'] ?? '';
        $type = $_GET['type'] ?? '';

        // Get all demandes from the database with search/filters
        $demandes = $this->demandeModel->getAllDemandes($search, $status, $type);

        $this->view('admin/all_demandes', ['demandes' => $demandes]);
    }

    // System settings
    public function settings() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Process form submission
            $appName = $_POST['app_name'] ?? '';
            $emailNotifications = isset($_POST['email_notifications']) ? 1 : 0;
            $daysBeforeExpiration = $_POST['days_before_expiration'] ?? 30;

            // Update settings (this would typically update a settings table or config file)
            $success = true; // Simulated success

            if ($success) {
                $this->view('admin/settings', [
                    'success' => 'Paramètres mis à jour avec succès',
                    'settings' => [
                        'app_name' => $appName,
                        'email_notifications' => $emailNotifications,
                        'days_before_expiration' => $daysBeforeExpiration
                    ]
                ]);
            } else {
                $this->view('admin/settings', [
                    'error' => 'Erreur lors de la mise à jour des paramètres',
                    'settings' => [
                        'app_name' => $appName,
                        'email_notifications' => $emailNotifications,
                        'days_before_expiration' => $daysBeforeExpiration
                    ]
                ]);
            }
        } else {
            // Get current settings
            $settings = [
                'app_name' => 'Gestion Congés',
                'email_notifications' => 1,
                'days_before_expiration' => 30
            ];

            $this->view('admin/settings', ['settings' => $settings]);
        }
    }

    // System logs
    public function logs() {
        // Simulated log data
        $logs = [
            ['date' => '2025-05-13 14:30:00', 'user' => 'admin', 'action' => 'Login', 'details' => 'Successful login from 192.168.1.100'],
            ['date' => '2025-05-13 12:15:00', 'user' => 'jean.dupont', 'action' => 'Create request', 'details' => 'Created leave request #123'],
            ['date' => '2025-05-12 16:45:00', 'user' => 'marie.dubois', 'action' => 'Approve request', 'details' => 'Approved leave request #122'],
            ['date' => '2025-05-12 10:30:00', 'user' => 'system', 'action' => 'Backup', 'details' => 'Daily backup completed successfully']
        ];

        $this->view('admin/logs', ['logs' => $logs]);
    }

    // Manage public holidays (Jours Fériés)
    public function joursFeries() {
        // Get year parameter from URL, default to current year
        $year = $_GET['year'] ?? date('Y');

        // Handle form submission for adding a new holiday
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $name = $_POST['name'] ?? '';
            $date = $_POST['date'] ?? '';
            $isRecurring = isset($_POST['is_recurring']) ? 1 : 0;

            if (empty($name) || empty($date)) {
                // Error handling
                $this->view('admin/jours_feries', [
                    'error' => 'Tous les champs sont obligatoires',
                    'holidays' => $this->jourFerieModel->getJoursFeriesByYear($year)
                ]);
                return;
            }

            // Check if we're updating or creating
            if (!empty($_POST['id'])) {
                // Update existing holiday
                $id = $_POST['id'];
                $success = $this->jourFerieModel->updateJourFerie($id, $name, $date, $isRecurring);
                $message = $success ? 'Le jour férié a été mis à jour avec succès.' : 'Erreur lors de la mise à jour du jour férié.';
            } else {
                // Create new holiday
                $success = $this->jourFerieModel->createJourFerie($name, $date, $isRecurring);
                $message = $success ? 'Le jour férié a été ajouté avec succès.' : 'Erreur lors de l\'ajout du jour férié.';
            }

            // Redirect to refresh the page
            $this->redirect('/jours-feries-admin?success=1&message=' . urlencode($message));
            return;
        }

        // Delete a holiday
        if (isset($_GET['delete'])) {
            $id = $_GET['delete'];
            $success = $this->jourFerieModel->deleteJourFerie($id);

            $this->redirect('/jours-feries-admin?deleted=1');
            return;
        }

        // Get holidays for the selected year
        $holidays = $this->jourFerieModel->getJoursFeriesByYear($year);

        $this->view('admin/jours_feries', [
            'holidays' => $holidays,
            'year' => $year,
            'success' => isset($_GET['success']),
            'deleted' => isset($_GET['deleted']),
            'message' => $_GET['message'] ?? null
        ]);
    }

    // Manage leave balances
    public function leaveBalances() {
        // Get search parameters from URL
        $search = $_GET['search'] ?? '';
        $department = $_GET['department'] ?? '';
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = 10;

        // Get all departments for the filter dropdown
        $departments = $this->userModel->getAllDepartments();

        // Get users with their leave balances
        $users = $this->userModel->getAllUsers($search, '', $department);

        // Calculate pagination
        $totalUsers = count($users);
        $totalPages = ceil($totalUsers / $perPage);
        $currentPage = max(1, min($page, $totalPages));
        $offset = ($currentPage - 1) * $perPage;

        // Slice the users array for the current page
        $users = array_slice($users, $offset, $perPage);

        // Get leave balances for each user
        $employees = [];
        foreach ($users as $user) {
            $balances = $this->leaveBalanceModel->getUserLeaveBalances($user['id']);
            $user['balances'] = $balances;
            $employees[] = $user;
        }

        $this->view('admin/leave_balances', [
            'employees' => $employees,
            'departments' => $departments,
            'search' => $search,
            'department' => $department,
            'currentPage' => $currentPage,
            'totalPages' => $totalPages,
            'success' => $_GET['success'] ?? null,
            'error' => $_GET['error'] ?? null
        ]);
    }

    // Initialize leave balances for all users
    public function initializeLeaveBalances() {
        try {
            // Get all active users
            $users = $this->userModel->getAllUsers();

            if (empty($users)) {
                $this->redirect('/admin/leave-balances?error=' . urlencode('Aucun utilisateur trouvé pour l\'initialisation.'));
                return;
            }

            $successCount = 0;
            $failCount = 0;
            $errors = [];
            $currentYear = date('Y');

            // Initialize leave balances for each user
            foreach ($users as $user) {
                try {
                    $result = $this->leaveBalanceModel->initializeUserLeaveBalances($user['id'], $currentYear);
                    if ($result) {
                        $successCount++;
                        error_log("Successfully initialized leave balances for user {$user['prenom']} {$user['nom']} (ID: {$user['id']})");
                    } else {
                        $failCount++;
                        $errors[] = "Échec pour {$user['prenom']} {$user['nom']}";
                        error_log("Failed to initialize leave balances for user {$user['prenom']} {$user['nom']} (ID: {$user['id']})");
                    }
                } catch (Exception $e) {
                    $failCount++;
                    $errors[] = "Erreur pour {$user['prenom']} {$user['nom']}: " . $e->getMessage();
                    error_log("Exception while initializing leave balances for user {$user['prenom']} {$user['nom']} (ID: {$user['id']}): " . $e->getMessage());
                }
            }

            // Prepare detailed message
            if ($failCount === 0) {
                $message = "✅ Initialisation réussie! Soldes initialisés pour {$successCount} utilisateurs pour l'année {$currentYear}.";
                $this->redirect('/admin/leave-balances?success=' . urlencode($message));
            } else {
                $message = "⚠️ Initialisation partiellement réussie: {$successCount} succès, {$failCount} échecs.";
                if (count($errors) <= 5) {
                    $message .= " Erreurs: " . implode(', ', $errors);
                }
                $this->redirect('/admin/leave-balances?error=' . urlencode($message));
            }
        } catch (Exception $e) {
            error_log("Critical error in initializeLeaveBalances: " . $e->getMessage());
            $this->redirect('/admin/leave-balances?error=' . urlencode('Erreur critique lors de l\'initialisation des soldes: ' . $e->getMessage()));
        }
    }

    // CSV Import functionality
    public function importCsv() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/create-user');
            return;
        }

        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            $this->redirect('/create-user?error=' . urlencode('Erreur lors du téléchargement du fichier CSV'));
            return;
        }

        $csvFile = $_FILES['csv_file']['tmp_name'];
        $successCount = 0;
        $errorCount = 0;
        $errors = [];

        try {
            if (($handle = fopen($csvFile, "r")) !== FALSE) {
                // Skip header row
                $header = fgetcsv($handle, 1000, ",");

                // Expected columns: matricule_rh, nom, prenom, login, email, password, role, departement, poste, telephone, date_entree
                $expectedColumns = ['matricule_rh', 'nom', 'prenom', 'login', 'email', 'password', 'role', 'departement', 'poste', 'telephone', 'date_entree'];

                $rowNumber = 1;
                while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                    $rowNumber++;

                    // Skip empty rows
                    if (empty(array_filter($data))) {
                        continue;
                    }

                    // Validate required fields (matricule_rh, nom, prenom, login, email, password, role, departement)
                    if (count($data) < 8 || empty($data[0]) || empty($data[1]) || empty($data[2]) || empty($data[3]) || empty($data[4]) || empty($data[5]) || empty($data[6]) || empty($data[7])) {
                        $errors[] = "Ligne $rowNumber: Champs obligatoires manquants (matricule_rh, nom, prenom, login, email, password, role, departement)";
                        $errorCount++;
                        continue;
                    }

                    // Validate email format
                    if (!filter_var($data[4], FILTER_VALIDATE_EMAIL)) {
                        $errors[] = "Ligne $rowNumber: Format d'email invalide '{$data[4]}'";
                        $errorCount++;
                        continue;
                    }

                    // Check if email already exists (primary validation)
                    if ($this->userModel->emailExists($data[4])) {
                        $errors[] = "Ligne $rowNumber: L'email '{$data[4]}' existe déjà (l'email est maintenant l'identifiant principal)";
                        $errorCount++;
                        continue;
                    }

                    // Check if login already exists (for backward compatibility)
                    if ($this->userModel->loginExists($data[3])) {
                        $errors[] = "Ligne $rowNumber: L'utilisateur '{$data[3]}' existe déjà";
                        $errorCount++;
                        continue;
                    }

                    // Check if matricule_rh already exists
                    $existingMatricule = $this->userModel->getUserByMatricule($data[0]);
                    if ($existingMatricule) {
                        $errors[] = "Ligne $rowNumber: Le matricule RH '{$data[0]}' existe déjà";
                        $errorCount++;
                        continue;
                    }

                    // Prepare user data with all required fields
                    $userData = [
                        'matricule_rh' => $data[0],
                        'nom' => $data[1],
                        'prenom' => $data[2],
                        'login' => $data[3],
                        'email' => $data[4],
                        'password' => $data[5],
                        'role' => $data[6],
                        'departement' => $data[7], // Required field
                        'date_creation' => date('Y-m-d H:i:s'),
                        'actif' => 1
                    ];

                    // Add optional fields if provided
                    if (!empty($data[8])) $userData['poste'] = $data[8];
                    if (!empty($data[9])) $userData['telephone'] = $data[9];
                    if (!empty($data[10])) $userData['date_entree'] = $data[10];

                    // Create user
                    $success = $this->userModel->createUser($userData);
                    if ($success) {
                        $successCount++;
                    } else {
                        $errors[] = "Ligne $rowNumber: Erreur lors de la création de l'utilisateur '{$data[2]}'";
                        $errorCount++;
                    }
                }
                fclose($handle);
            }
        } catch (Exception $e) {
            $this->redirect('/create-user?error=' . urlencode('Erreur lors du traitement du fichier CSV: ' . $e->getMessage()));
            return;
        }

        // Prepare result message
        $message = "Import terminé: $successCount utilisateurs créés";
        if ($errorCount > 0) {
            $message .= ", $errorCount erreurs";
        }

        $this->redirect('/list-users?success=' . urlencode($message));
    }

    // Download CSV template
    public function downloadCsvTemplate() {
        $filename = 'template_utilisateurs.csv';

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $output = fopen('php://output', 'w');

        // Write header
        fputcsv($output, ['matricule_rh', 'nom', 'prenom', 'login', 'email', 'password', 'role', 'departement', 'poste', 'telephone', 'date_entree']);

        // Write example rows
        fputcsv($output, ['EMP-JD001', 'Dupont', 'Jean', 'jdupont', '<EMAIL>', 'motdepasse123', 'employe', 'IT', 'Développeur', '+33123456789', '2025-01-15']);
        fputcsv($output, ['RSC-MS002', 'Martin', 'Sophie', 'smartin', '<EMAIL>', 'motdepasse456', 'responsable', 'RH', 'Responsable RH', '+33987654321', '2024-12-01']);

        fclose($output);
        exit;
    }

    // Process monthly accruals
    public function processAccruals() {
        $result = $this->leaveBalanceModel->processMonthlyAccruals();

        if ($result) {
            $message = "Les attributions mensuelles ont été traitées avec succès.";
            $this->redirect('/admin/leave-balances?success=' . urlencode($message));
        } else {
            $message = "Erreur lors du traitement des attributions mensuelles.";
            $this->redirect('/admin/leave-balances?error=' . urlencode($message));
        }
    }

    // Edit leave balance for a specific user
    public function editLeaveBalance($id = null) {
        // Get user ID from parameter or GET request
        $userId = $id ?? $_GET['id'] ?? 0;

        if (empty($userId)) {
            $this->redirect('/admin/leave-balances');
            return;
        }

        // Get user details
        $user = $this->userModel->getUserById($userId);

        if (!$user) {
            $this->redirect('/admin/leave-balances');
            return;
        }

        // Get user's leave balances
        $balances = $this->leaveBalanceModel->getUserLeaveBalances($userId);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Process form submission
            $payeTotal = (float)($_POST['paye_total'] ?? 0);
            $payeUsed = (float)($_POST['paye_used'] ?? 0);
            $exceptionnelTotal = (float)($_POST['exceptionnel_total'] ?? 0);
            $exceptionnelUsed = (float)($_POST['exceptionnel_used'] ?? 0);
            $sansSoldeTotal = (float)($_POST['sans_solde_total'] ?? 0);
            $sansSoldeUsed = (float)($_POST['sans_solde_used'] ?? 0);
            $accrualRate = (float)($_POST['accrual_rate'] ?? 0);

            // Update leave balances in the database
            $success = $this->updateUserLeaveBalances($userId, [
                'payé' => [
                    'total' => $payeTotal,
                    'used' => $payeUsed,
                    'remaining' => max(0, $payeTotal - $payeUsed),
                    'accrual_rate' => $accrualRate
                ],
                'exceptionnel' => [
                    'total' => $exceptionnelTotal,
                    'used' => $exceptionnelUsed,
                    'remaining' => max(0, $exceptionnelTotal - $exceptionnelUsed)
                ],
                'sans solde' => [
                    'total' => $sansSoldeTotal,
                    'used' => $sansSoldeUsed,
                    'remaining' => max(0, $sansSoldeTotal - $sansSoldeUsed)
                ]
            ]);

            if ($success) {
                $this->redirect('/admin/leave-balances?success=Les soldes ont été mis à jour avec succès.');
            } else {
                $this->view('admin/edit_leave_balance', [
                    'error' => 'Erreur lors de la mise à jour des soldes',
                    'user' => $user,
                    'balances' => $balances
                ]);
            }
        } else {
            // Display form with user details and balances
            $this->view('admin/edit_leave_balance', [
                'user' => $user,
                'balances' => $balances
            ]);
        }
    }

    // View leave accrual history for a specific user
    public function leaveAccrualHistory($id = null) {
        // Get user ID from parameter or GET request
        $userId = $id ?? $_GET['id'] ?? 0;

        if (empty($userId)) {
            $this->redirect('/admin/leave-balances');
            return;
        }

        // Get user details
        $user = $this->userModel->getUserById($userId);

        if (!$user) {
            $this->redirect('/admin/leave-balances');
            return;
        }

        // Get user's leave accrual history
        $history = $this->leaveBalanceModel->getAccrualHistory($userId);

        $this->view('admin/leave_accrual_history', [
            'user' => $user,
            'history' => $history
        ]);
    }

    // Manage department leave balances
    public function departmentLeaveBalances() {
        // Get year parameter from URL, default to current year
        $year = $_GET['year'] ?? date('Y');

        // Check if any department has balances for this year
        $departmentBalances = $this->departmentLeaveBalanceModel->getAllDepartmentBalances($year);

        // If no balances exist, show message to initialize
        if (empty($departmentBalances)) {
            $this->view('admin/department_leave_balances', [
                'departmentBalances' => [],
                'year' => $year,
                'info' => 'Aucun solde départemental trouvé. Veuillez initialiser les soldes.'
            ]);
            return;
        }

        $this->view('admin/department_leave_balances', [
            'departmentBalances' => $departmentBalances,
            'year' => $year,
            'success' => $_GET['success'] ?? null,
            'error' => $_GET['error'] ?? null
        ]);
    }

    // Initialize department leave balances
    public function initializeDepartmentLeaveBalances() {
        // Get year parameter from URL, default to current year
        $year = $_GET['year'] ?? date('Y');
        $totalDays = 15; // Default value as per requirements

        $result = $this->departmentLeaveBalanceModel->initializeAllDepartmentLeaveBalances($year, $totalDays);

        if ($result) {
            $message = "Les soldes départementaux ont été initialisés avec succès.";
            $this->redirect('/admin/department-leave-balances?success=' . urlencode($message));
        } else {
            $message = "Une erreur est survenue lors de l'initialisation des soldes départementaux.";
            $this->redirect('/admin/department-leave-balances?error=' . urlencode($message));
        }
    }

    // Edit department leave balance
    public function editDepartmentLeaveBalance() {
        $departmentName = isset($_GET['department']) ? urldecode($_GET['department']) : '';
        $year = $_GET['year'] ?? date('Y');

        if (empty($departmentName)) {
            $this->redirect('/admin/department-leave-balances');
            return;
        }

        // Get department balance details
        $balance = $this->departmentLeaveBalanceModel->getDepartmentLeaveBalance($departmentName, $year);

        if (!$balance) {
            $this->redirect('/admin/department-leave-balances?error=Département non trouvé');
            return;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Process form submission
            $totalDays = (float)($_POST['total_days'] ?? 15);

            try {
                $this->db->beginTransaction();

                // Update department balance in the database
                $stmt = $this->db->prepare("
                    UPDATE department_leave_balances
                    SET
                        total_days = ?,
                        remaining_days = ? - used_days
                    WHERE department_name = ? AND year = ?
                ");

                $success = $stmt->execute([$totalDays, $totalDays, $departmentName, $year]);

                if ($success) {
                    $this->db->commit();
                    $this->redirect('/admin/department-leave-balances?success=Le solde départemental a été mis à jour avec succès.');
                } else {
                    $this->db->rollBack();
                    $this->view('admin/edit_department_leave_balance', [
                        'error' => 'Erreur lors de la mise à jour du solde départemental',
                        'department' => $departmentName,
                        'balance' => $balance,
                        'year' => $year
                    ]);
                }
            } catch (Exception $e) {
                $this->db->rollBack();
                $this->view('admin/edit_department_leave_balance', [
                    'error' => 'Erreur lors de la mise à jour du solde départemental: ' . $e->getMessage(),
                    'department' => $departmentName,
                    'balance' => $balance,
                    'year' => $year
                ]);
            }
        } else {
            // Display form with department balance details
            $this->view('admin/edit_department_leave_balance', [
                'department' => $departmentName,
                'balance' => $balance,
                'year' => $year
            ]);
        }
    }

    // Helper method to update user leave balances
    private function updateUserLeaveBalances($userId, $balances) {
        try {
            $this->db->beginTransaction();

            foreach ($balances as $type => $values) {
                $stmt = $this->db->prepare("
                    UPDATE leave_balances
                    SET
                        annual_allocation = ?,
                        used_days = ?,
                        remaining_days = ?,
                        accrual_rate = ?
                    WHERE user_id = ? AND leave_type = ? AND year = ?
                ");

                $accrualRate = $values['accrual_rate'] ?? 0;

                $stmt->execute([
                    $values['total'],
                    $values['used'],
                    $values['remaining'],
                    $accrualRate,
                    $userId,
                    $type,
                    date('Y')
                ]);
            }

            $this->db->commit();
            return true;
        } catch (Exception $e) {
            $this->db->rollBack();
            error_log("Error updating leave balances: " . $e->getMessage());
            return false;
        }
    }
}
