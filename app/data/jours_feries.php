<?php
/**
 * <PERSON><PERSON><PERSON> contenant les jours fériés pour différents pays
 */

// Jours fériés pour 2025 (France)
$joursFeriesFrancais = [
    '2025-01-01' => 'Jour de l\'an',
    '2025-04-21' => '<PERSON><PERSON>',
    '2025-05-01' => 'Fête du Travail',
    '2025-05-08' => 'Victoire 1945',
    '2025-05-29' => 'Ascension',
    '2025-06-09' => 'Lundi de Pentecôte',
    '2025-07-14' => 'Fête Nationale',
    '2025-08-15' => 'Assomption',
    '2025-11-01' => 'Toussaint',
    '2025-11-11' => 'Armistice 1918',
    '2025-12-25' => 'Noël',
];

// Jours fériés pour 2025 (Tunisie) - Dates basées sur les estimations actuelles et les fêtes fixes
$joursFeriesTunisie = [
    '2025-01-01' => 'Jour de l\'an',
    '2025-03-20' => 'Fête de l\'Indépendance',
    '2025-04-10' => 'Aïd <PERSON>', // Date probable, à confirmer
    '2025-04-11' => 'Aïd <PERSON> Fitr (lendemain)', // Date probable, à confirmer
    '2025-05-01' => 'Fête du Travail',
    '2025-06-17' => 'Aïd El Adha', // Date probable, à confirmer
    '2025-06-18' => 'Aïd El Adha (lendemain)', // Date probable, à confirmer
    '2025-07-25' => 'Fête de la République',
    '2025-08-13' => 'Fête de la Femme',
    '2025-10-06' => 'Mouled', // Date probable, à confirmer
];

/**
 * Fonction pour obtenir les prochains jours fériés
 * 
 * @param array $joursFeries Liste des jours fériés
 * @param int $nombre Nombre de jours fériés à retourner
 * @return array Les prochains jours fériés
 */
function getProchainsJoursFeries($joursFeries, $nombre = 5) {
    $dateActuelle = date('Y-m-d');
    $prochains = [];
    
    foreach ($joursFeries as $date => $nom) {
        if ($date >= $dateActuelle) {
            $prochains[$date] = $nom;
            if (count($prochains) >= $nombre) break;
        }
    }
    
    return $prochains;
}
?>