<?php
/**
 * Monthly Leave Accrual Process Script
 *
 * This script is designed to be run on the 1st of each month to process leave accruals
 * for all active employees. It can be set up as a cron job.
 *
 * Example cron entry (run at midnight on the 1st of each month):
 * 0 0 1 * * php /path/to/process_monthly_accruals.php
 */

// Set the correct path to your application
define('APP_PATH', dirname(__DIR__));

// Include necessary files
require_once APP_PATH . '/config/config.php';
require_once APP_PATH . '/core/Database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/app/models/LeaveBalanceModel.php';

// Initialize the database connection
$db = Database::getInstance()->getConnection();

// Create an instance of the LeaveBalanceModel
$leaveBalanceModel = new LeaveBalanceModel();

// Log the start of the process
echo "Starting monthly leave accrual process at " . date('Y-m-d H:i:s') . PHP_EOL;

// Process the accruals
$result = $leaveBalanceModel->processMonthlyAccruals();

if ($result) {
    echo "Monthly leave accrual process completed successfully." . PHP_EOL;
} else {
    echo "Error: Monthly leave accrual process failed." . PHP_EOL;
}

// Log the end of the process
echo "Finished at " . date('Y-m-d H:i:s') . PHP_EOL;
