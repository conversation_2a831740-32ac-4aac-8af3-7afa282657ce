<?php
/**
 * Setup System Logs Table Script
 *
 * This script executes the SQL file to create the system logs table.
 */

// Set the correct path to your application
define('APP_PATH', dirname(__DIR__));

// Include necessary files
require_once APP_PATH . '/config/config.php';
require_once APP_PATH . '/core/Database.php';

// Initialize the database connection
$db = Database::getInstance()->getConnection();

// Path to the SQL file
$sqlFile = APP_PATH . '/sql/system_logs.sql';

// Check if the file exists
if (!file_exists($sqlFile)) {
    die("SQL file not found: $sqlFile\n");
}

// Read the SQL file
$sql = file_get_contents($sqlFile);

// Split the SQL file into individual statements
$statements = array_filter(array_map('trim', explode(';', $sql)));

// Execute each statement
$success = true;
foreach ($statements as $statement) {
    if (!empty($statement)) {
        try {
            $result = $db->exec($statement);
            if ($result === false) {
                $errorInfo = $db->errorInfo();
                echo "Error executing statement: " . $errorInfo[2] . "\n";
                $success = false;
            }
        } catch (PDOException $e) {
            echo "PDO Exception: " . $e->getMessage() . "\n";
            $success = false;
        }
    }
}

// Output result
if ($success) {
    echo "System logs table setup completed successfully.\n";
} else {
    echo "System logs table setup completed with errors.\n";
}
