<?php
// <PERSON><PERSON><PERSON> to update leave balances according to new rules

// Define paths
define('ROOT_PATH', dirname(__DIR__));
define('CONFIG_PATH', ROOT_PATH . '/config');

// Include config
require_once CONFIG_PATH . '/config.php';

// Connect to database
try {
    $db = new PDO(
        "mysql:host=" . Config::DB_HOST . ";dbname=" . Config::DB_NAME . ";charset=utf8mb4",
        Config::DB_USER,
        Config::DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );

    echo "Connected to database successfully.\n";
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Read and execute the SQL file
try {
    $sql = file_get_contents(ROOT_PATH . '/sql/update_leave_balances.sql');

    // Split SQL file into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)), 'strlen');

    // Begin transaction
    $db->beginTransaction();

    // Execute each statement
    foreach ($statements as $statement) {
        $db->exec($statement);
        echo "Executed: " . substr($statement, 0, 50) . "...\n";
    }

    // Commit transaction
    $db->commit();

    echo "Leave balances updated successfully!\n";
} catch (PDOException $e) {
    // Rollback transaction on error
    $db->rollBack();
    die("Error updating leave balances: " . $e->getMessage());
}
