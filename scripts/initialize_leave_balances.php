<?php
/**
 * Initialize Leave Balances Script
 *
 * This script initializes leave balances for all active users in the system.
 * It should be run once when setting up the leave balance system or when adding new users.
 */

// Set the correct path to your application
define('APP_PATH', dirname(__DIR__));

// Include necessary files
require_once APP_PATH . '/config/config.php';
require_once APP_PATH . '/core/Database.php';
require_once APP_PATH . '/core/Model.php';
require_once APP_PATH . '/app/models/UserModel.php';
require_once APP_PATH . '/app/models/LeaveBalanceModel.php';

// Initialize the database connection
$db = Database::getInstance()->getConnection();

// Create instances of the models
$userModel = new UserModel();
$leaveBalanceModel = new LeaveBalanceModel();

// Get the current year
$currentYear = date('Y');

// Log the start of the process
echo "Starting leave balance initialization process at " . date('Y-m-d H:i:s') . PHP_EOL;

// Get all active users
$users = $userModel->getAllUsers();

// Initialize leave balances for each user
$successCount = 0;
$failCount = 0;

foreach ($users as $user) {
    echo "Initializing leave balances for user {$user['prenom']} {$user['nom']} (ID: {$user['id']})..." . PHP_EOL;

    $result = $leaveBalanceModel->initializeUserLeaveBalances($user['id'], $currentYear);

    if ($result) {
        echo "  Success!" . PHP_EOL;
        $successCount++;
    } else {
        echo "  Failed!" . PHP_EOL;
        $failCount++;
    }
}

// Log the results
echo PHP_EOL;
echo "Initialization completed with {$successCount} successes and {$failCount} failures." . PHP_EOL;
echo "Finished at " . date('Y-m-d H:i:s') . PHP_EOL;
