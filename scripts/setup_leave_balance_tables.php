<?php
/**
 * Setup Leave Balance Tables Script
 *
 * This script executes the SQL file to create the leave balance tables.
 */

// Set the correct path to your application
define('APP_PATH', dirname(__DIR__));

// Include necessary files
require_once APP_PATH . '/config/config.php';
require_once APP_PATH . '/core/Database.php';

// Initialize the database connection
$db = Database::getInstance()->getConnection();

// Paths to the SQL files
$sqlFiles = [
    'tables' => dirname(__DIR__) . '/sql/leave_balances.sql',
    'initialize_procedure' => dirname(__DIR__) . '/sql/initialize_user_leave_balances.sql',
    'accrual_procedure' => dirname(__DIR__) . '/sql/process_monthly_accruals.sql',
    'accrual_event' => dirname(__DIR__) . '/sql/monthly_leave_accrual_event.sql'
];

// Log the start of the process
echo "Starting setup of leave balance tables at " . date('Y-m-d H:i:s') . PHP_EOL;

// Execute each SQL file
$successCount = 0;
$failCount = 0;

foreach ($sqlFiles as $type => $sqlFilePath) {
    echo "Processing {$type} SQL file: {$sqlFilePath}" . PHP_EOL;

    // Check if the SQL file exists
    if (!file_exists($sqlFilePath)) {
        echo "Error: SQL file not found at {$sqlFilePath}" . PHP_EOL;
        continue;
    }

    // Read the SQL file
    $sql = file_get_contents($sqlFilePath);

    // Execute the SQL file directly
    try {
        $db->exec($sql);
        echo "Successfully executed {$type} SQL file." . PHP_EOL;
        $successCount++;
    } catch (PDOException $e) {
        echo "Error executing {$type} SQL file: " . $e->getMessage() . PHP_EOL;
        $failCount++;
    }
}

// Log the results
echo PHP_EOL;
echo "Setup completed with {$successCount} successful statements and {$failCount} failed statements." . PHP_EOL;
echo "Finished at " . date('Y-m-d H:i:s') . PHP_EOL;
