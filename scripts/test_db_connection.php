<?php
// Set the correct path to your application
define('APP_PATH', dirname(__DIR__));

// Include necessary files
require_once APP_PATH . '/config/config.php';
require_once APP_PATH . '/core/Database.php';

// Initialize the database connection
$db = Database::getInstance()->getConnection();

// Test the connection
try {
    $stmt = $db->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Successfully connected to the database." . PHP_EOL;
    echo "Tables in the database:" . PHP_EOL;
    
    foreach ($tables as $table) {
        echo "- $table" . PHP_EOL;
    }
} catch (PDOException $e) {
    echo "Error connecting to the database: " . $e->getMessage() . PHP_EOL;
}
