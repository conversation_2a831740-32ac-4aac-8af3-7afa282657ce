-- Add reference_demande column to demandes_conges table
-- This migration adds a reference system for leave requests

-- Add the reference_demande column
ALTER TABLE `demandes_conges` 
ADD COLUMN `reference_demande` VARCHAR(50) DEFAULT NULL AFTER `id`;

-- Add index for better performance on reference lookups
ALTER TABLE `demandes_conges` 
ADD INDEX `idx_reference_demande` (`reference_demande`);

-- Update existing records to have references (backward compatibility)
-- This will generate references for existing demandes based on their creation date and user matricule
UPDATE demandes_conges d
JOIN users u ON d.user_id = u.id
SET d.reference_demande = CONCAT(
    DATE_FORMAT(d.date_demande, '%d%m'),
    '-',
    u.matricule_rh
)
WHERE d.reference_demande IS NULL;

-- Verify the update
SELECT COUNT(*) as total_demandes, 
       COUNT(reference_demande) as demandes_with_reference 
FROM demandes_conges;
