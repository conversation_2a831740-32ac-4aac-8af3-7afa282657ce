-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Hôte : 127.0.0.1
-- <PERSON><PERSON><PERSON><PERSON> le : mar. 13 mai 2025 à 17:08
-- Version du serveur : 10.4.32-MariaDB
-- Version de PHP : 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de données : `gestion_conges`
--

-- --------------------------------------------------------

--
-- Structure de la table `demandes_conges`
--

CREATE TABLE `demandes_conges` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `type` enum('payé','exceptionnel','sans solde') NOT NULL,
  `date_debut` date NOT NULL,
  `date_fin` date NOT NULL,
  `statut` enum('en cours','acceptée','refusée') DEFAULT 'en cours',
  `date_demande` timestamp NOT NULL DEFAULT current_timestamp(),
  `demi_journee` tinyint(1) DEFAULT 0,
  `demi_type` varchar(20) DEFAULT NULL,
  `motif` text DEFAULT NULL,
  `valide_par` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `demandes_conges`
--

INSERT INTO `demandes_conges` (`id`, `user_id`, `type`, `date_debut`, `date_fin`, `statut`, `date_demande`, `demi_journee`, `demi_type`, `motif`, `valide_par`) VALUES
(5, 17, 'payé', '2025-05-20', '2025-05-22', 'acceptée', '2025-04-15 13:40:12', 0, NULL, NULL, NULL),
(6, 17, 'payé', '2025-06-20', '2025-06-22', 'refusée', '2025-04-15 13:40:56', 0, NULL, NULL, NULL),
(33, 17, 'payé', '2025-04-28', '2025-05-02', 'en cours', '2025-04-16 22:36:00', 1, '', 'Vacances', NULL),
(34, 17, 'payé', '2025-04-28', '2025-05-02', 'en cours', '2025-04-16 22:45:37', 1, '', 'Vacances', NULL),
(35, 17, 'payé', '2025-04-28', '2025-05-02', 'en cours', '2025-04-16 22:56:04', 1, '', 'vacances', NULL),
(36, 17, 'payé', '2025-04-28', '2025-05-02', 'en cours', '2025-04-16 22:59:55', 1, '', 'Vacances', NULL),
(37, 17, 'payé', '2025-04-28', '2025-05-02', 'en cours', '2025-04-16 23:01:46', 1, '', 'vacances', NULL),
(38, 17, 'payé', '2025-04-28', '2025-05-02', 'en cours', '2025-04-16 23:03:49', 1, '', 'Vacances', NULL),
(43, 17, 'payé', '2025-05-05', '2025-05-09', 'en cours', '2025-04-18 11:09:36', 1, '', 'Vacances', NULL),
(44, 17, 'payé', '2025-05-05', '2025-05-09', 'en cours', '2025-04-23 16:24:39', 1, '', '', NULL),
(46, 17, 'payé', '2025-05-05', '2025-05-07', 'en cours', '2025-04-23 17:59:30', 1, '', '', NULL),
(47, 17, 'payé', '2025-05-26', '2025-05-30', 'en cours', '2025-05-07 14:38:00', 1, '', '', NULL);

-- --------------------------------------------------------

--
-- Structure de la table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `type` enum('info','success','error','warning') DEFAULT 'info',
  `title` varchar(255) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Structure de la table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `login` varchar(50) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `nom` varchar(100) DEFAULT NULL,
  `prenom` varchar(100) DEFAULT NULL,
  `role` enum('employe','responsable','planificateur','admin') NOT NULL,
  `photo_profil` varchar(255) DEFAULT NULL,
  `activite` varchar(100) DEFAULT NULL,
  `date_entree` date DEFAULT NULL,
  `date_creation` timestamp NULL DEFAULT current_timestamp(),
  `actif` tinyint(1) DEFAULT 1,
  `telephone` varchar(20) DEFAULT NULL,
  `reset_token` varchar(64) DEFAULT NULL,
  `reset_expires` datetime DEFAULT NULL,
  `poste` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Déchargement des données de la table `users`
--

INSERT INTO `users` (`id`, `login`, `email`, `password`, `nom`, `prenom`, `role`, `photo_profil`, `activite`, `date_entree`, `date_creation`, `actif`, `telephone`, `reset_token`, `reset_expires`, `poste`) VALUES
(4, 'admin', 'admin', '$2y$10$67rqjluJUBM/2CRYRAbARudrjLEpCQ6kbv/LP3QPO2Rnm.l/If3sC', 'Super', 'Admin', 'admin', NULL, NULL, NULL, '2025-04-17 17:47:54', 1, NULL, NULL, NULL, NULL),
(17, 'employe1', 'employe1', '$2y$10$ud8nAzaLqmLri.P.679..OMIQV0fRuPDHPlWGqW6EjgXcEOS8p8VC', 'Dupont', 'Pierre', 'employe', NULL, NULL, NULL, '2025-04-17 17:47:54', 1, NULL, NULL, NULL, NULL),
(18, 'planificateur1', 'planificateur1', '$2y$10$RANa6CXst.TR.HbdcQV4h.Z1dqzgAWfPUZOBVXyPNHDFbID4E2MQ2', 'Bernard', 'Jacques', 'planificateur', NULL, NULL, NULL, '2025-04-17 17:47:54', 1, NULL, NULL, NULL, NULL),
(19, 'responsable1', 'responsable1', '$2y$10$n8jN17qu6C6t2HBOVzUSdul082dFt1daGwmKCKjz04AIdEKfnYPNG', 'Durand', 'Michel', 'responsable', NULL, NULL, NULL, '2025-04-17 17:47:54', 1, NULL, NULL, NULL, NULL);

--
-- Index pour les tables déchargées
--

--
-- Index pour la table `demandes_conges`
--
ALTER TABLE `demandes_conges`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Index pour la table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`);

--
-- Index pour la table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `login` (`login`),
  ADD UNIQUE KEY `unique_email` (`email`);

--
-- AUTO_INCREMENT pour les tables déchargées
--

--
-- AUTO_INCREMENT pour la table `demandes_conges`
--
ALTER TABLE `demandes_conges`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=48;

--
-- AUTO_INCREMENT pour la table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT pour la table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- Contraintes pour les tables déchargées
--

--
-- Contraintes pour la table `demandes_conges`
--
ALTER TABLE `demandes_conges`
  ADD CONSTRAINT `demandes_conges_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
