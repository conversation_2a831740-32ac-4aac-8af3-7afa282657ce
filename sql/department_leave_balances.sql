-- Create department_leave_balances table
CREATE TABLE `department_leave_balances` (
  `id` int NOT NULL AUTO_INCREMENT,
  `department_name` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
  `year` int NOT NULL,
  `total_days` float NOT NULL DEFAULT '15',
  `used_days` float NOT NULL DEFAULT '0',
  `remaining_days` float NOT NULL DEFAULT '15',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `department_year_unique` (`department_name`,`year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert initial data for existing departments
INSERT INTO `department_leave_balances` (`department_name`, `year`, `total_days`, `used_days`, `remaining_days`) VALUES
('admin', 2025, 15, 0, 15),
('Support client', 2025, 15, 0, 15),
('Adminstration', 2025, 15, 0, 15);
