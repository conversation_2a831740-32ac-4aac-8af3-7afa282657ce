-- Create system_logs table
CREATE TABLE IF NOT EXISTS `system_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `username` varchar(100) NOT NULL,
  `action` varchar(50) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add foreign key constraint if user_id references users table
ALTER TABLE `system_logs`
  ADD CONSTRAINT `fk_system_logs_users` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Insert some sample log data
INSERT INTO `system_logs` (`user_id`, `username`, `action`, `details`, `ip_address`, `created_at`) VALUES
(4, 'admin', 'Login', 'Successful login', '127.0.0.1', NOW() - INTERVAL 1 HOUR),
(4, 'admin', 'Create User', 'Created user: jean.dupont', '127.0.0.1', NOW() - INTERVAL 55 MINUTE),
(17, 'jean.dupont', 'Login', 'Successful login', '127.0.0.1', NOW() - INTERVAL 50 MINUTE),
(17, 'jean.dupont', 'Create Request', 'Created leave request #1', '127.0.0.1', NOW() - INTERVAL 45 MINUTE),
(18, 'marie.dubois', 'Login', 'Successful login', '127.0.0.1', NOW() - INTERVAL 40 MINUTE),
(18, 'marie.dubois', 'Create Request', 'Created leave request #2', '127.0.0.1', NOW() - INTERVAL 35 MINUTE),
(4, 'admin', 'Approve Request', 'Approved leave request #1', '127.0.0.1', NOW() - INTERVAL 30 MINUTE),
(4, 'admin', 'Reject Request', 'Rejected leave request #2: Insufficient notice', '127.0.0.1', NOW() - INTERVAL 25 MINUTE),
(NULL, 'system', 'Backup', 'Daily backup completed successfully', '127.0.0.1', NOW() - INTERVAL 20 MINUTE),
(NULL, 'system', 'Cleanup', 'Removed expired notifications', '127.0.0.1', NOW() - INTERVAL 15 MINUTE),
(17, 'jean.dupont', 'Login', 'Successful login', '127.0.0.1', NOW() - INTERVAL 10 MINUTE),
(17, 'jean.dupont', 'View Request', 'Viewed leave request #1', '127.0.0.1', NOW() - INTERVAL 5 MINUTE),
(4, 'admin', 'Department Balance', 'Updated department leave balance for Support client', '127.0.0.1', NOW() - INTERVAL 2 MINUTE);
