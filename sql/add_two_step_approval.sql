-- Migration to add two-step approval workflow for leave requests
-- This adds the necessary columns and updates the status system

-- Add new columns for two-step approval
ALTER TABLE `demandes_conges` 
ADD COLUMN `planificateur_id` int DEFAULT NULL AFTER `responsable_id`,
ADD COLUMN `date_approbation_responsable` timestamp NULL DEFAULT NULL AFTER `planificateur_id`,
ADD COLUMN `date_approbation_planificateur` timestamp NULL DEFAULT NULL AFTER `date_approbation_responsable`;

-- Add indexes for better performance
ALTER TABLE `demandes_conges` 
ADD INDEX `idx_planificateur_id` (`planificateur_id`),
ADD INDEX `idx_date_approbation_responsable` (`date_approbation_responsable`),
ADD INDEX `idx_date_approbation_planificateur` (`date_approbation_planificateur`);

-- Update existing demandes to maintain backward compatibility
-- Set existing 'en cours' demandes to 'en_attente_responsable'
UPDATE `demandes_conges` 
SET `statut` = 'en_attente_responsable' 
WHERE `statut` = 'en cours';

-- For existing approved demandes, set both approval dates and IDs if available
UPDATE `demandes_conges` 
SET `date_approbation_responsable` = `date_decision`,
    `date_approbation_planificateur` = `date_decision`
WHERE `statut` = 'acceptee' AND `date_decision` IS NOT NULL;

-- Update existing approved demandes status to new format
UPDATE `demandes_conges` 
SET `statut` = 'approuvee' 
WHERE `statut` = 'acceptee';

-- Add foreign key constraints for data integrity
ALTER TABLE `demandes_conges`
ADD CONSTRAINT `fk_demandes_planificateur` 
FOREIGN KEY (`planificateur_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- Note: The responsable_id foreign key should already exist, but if not:
-- ALTER TABLE `demandes_conges`
-- ADD CONSTRAINT `fk_demandes_responsable` 
-- FOREIGN KEY (`responsable_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;
