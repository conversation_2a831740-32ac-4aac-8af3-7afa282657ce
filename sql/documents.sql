-- Create documents table
CREATE TABLE IF NOT EXISTS `documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nom` varchar(255) NOT NULL,
  `lien` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `categorie` varchar(50) DEFAULT 'general',
  `ordre` int(11) DEFAULT 0,
  `date_creation` timestamp NOT NULL DEFAULT current_timestamp(),
  `date_modification` timestamp NULL DEFAULT NULL ON UPDATE current_timestamp(),
  `actif` tinyint(1) DEFAULT 1,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert sample documents
INSERT INTO `documents` (`nom`, `lien`, `description`, `categorie`, `ordre`) VALUES
('Politique de congés', '#', 'Règles et procédures pour les congés', 'general', 1),
('Guide de l\'employé', '#', 'Informations générales pour les employés', 'general', 2);
