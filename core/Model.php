<?php
// Base model class that all models will extend

require_once __DIR__ . '/Database.php';

class Model {
    public $db;

    public function __construct() {
        try {
            // Get database instance
            $this->db = Database::getInstance()->getConnection();

            if (!$this->db) {
                error_log("Model::__construct - Failed to get database connection");
                throw new Exception("Failed to get database connection");
            }
        } catch (Exception $e) {
            error_log("Model::__construct - Exception: " . $e->getMessage());
            // Don't throw the exception here, let the controller handle it
            $this->db = null;
        }
    }
}
