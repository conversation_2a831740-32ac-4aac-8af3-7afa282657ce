<?php
// Router class to handle routing requests to controllers

class Router {
    private $routes = [];
    private $notFoundCallback;

    // Register a route
    public function addRoute($url, $controller, $action) {
        $this->routes[$url] = [
            'controller' => $controller,
            'action' => $action
        ];
    }

    // Set 404 handler
    public function setNotFound($callback) {
        $this->notFoundCallback = $callback;
    }

    // Dispatch the route
    public function dispatch($url) {
        // Remove query string
        $url = strtok($url, '?');

        // Default to home if no URL
        if ($url == '/') {
            $url = '/home';
        }

        // Debug information
        error_log("Router: Attempting to dispatch URL: " . $url);

        // Special case for API routes with numeric IDs
        if (preg_match('#^/api/absences/(\d+)$#', $url, $matches)) {
            error_log("Router: Found API route with numeric ID: " . $matches[1]);
            if (isset($this->routes['/api/absences/{id}'])) {
                $this->executeRoute($this->routes['/api/absences/{id}'], ['id' => $matches[1]]);
                return;
            }
        }

        // First check for exact route match
        if (isset($this->routes[$url])) {
            error_log("Router: Found exact route match for: " . $url);
            $this->executeRoute($this->routes[$url], []);
            return;
        }

        // Check for routes with parameters
        foreach ($this->routes as $route => $routeData) {
            // Skip routes without parameters
            if (strpos($route, '{') === false) {
                continue;
            }

            // Debug route pattern
            error_log("Router: Checking parameterized route: " . $route);

            // Convert route pattern to regex
            $pattern = $this->convertRouteToRegex($route);
            error_log("Router: Converted to regex pattern: " . $pattern);

            // Check if URL matches the pattern
            if (preg_match($pattern, $url, $matches)) {
                error_log("Router: URL matches pattern for route: " . $route);

                // Extract parameter values
                $params = [];
                preg_match_all('/{([^\/]+)}/', $route, $paramNames);

                // Skip the first match (full string)
                array_shift($matches);

                // Map parameter names to values
                foreach ($paramNames[1] as $index => $name) {
                    $params[$name] = $matches[$index] ?? null;
                    error_log("Router: Parameter {$name} = " . ($matches[$index] ?? 'null'));
                }

                // Execute the route with parameters
                error_log("Router: Executing route with parameters: " . json_encode($params));
                $this->executeRoute($routeData, $params);
                return;
            } else {
                error_log("Router: URL does not match pattern for route: " . $route);
            }
        }

        // Route not found
        $this->notFound();
    }

    // Convert route pattern to regex
    private function convertRouteToRegex($route) {
        // Escape special regex characters first
        $pattern = preg_quote($route, '/');

        // Replace escaped parameter placeholders with regex patterns
        // For numeric IDs (like /api/absences/{id} or /admin/leave_balances/history/{id})
        if (strpos($route, '{id}') !== false) {
            $pattern = str_replace('\{id\}', '(\d+)', $pattern);
        }

        // For other parameter types, use general pattern
        $pattern = preg_replace('/\\\\{([^}]+)\\\\}/', '([^\/]+)', $pattern);

        // Add delimiters
        $pattern = '/^' . $pattern . '$/';

        // Debug the pattern
        error_log("Router: Route pattern '$route' converted to regex: '$pattern'");

        return $pattern;
    }

    // Execute a route
    private function executeRoute($routeData, $params) {
        $controllerName = $routeData['controller'];
        $action = $routeData['action'];

        // Debug information
        error_log("Router::executeRoute - Controller: $controllerName, Action: $action, Params: " . json_encode($params));

        // Include controller
        $controllerFile = __DIR__ . '/../app/controllers/' . $controllerName . '.php';

        if (file_exists($controllerFile)) {
            require_once $controllerFile;

            // Create controller instance
            $controller = new $controllerName();

            // Special case for API routes with numeric IDs
            if ($controllerName === 'ApiController' && $action === 'getAbsenceDetails') {
                // Check if we have an 'id' parameter
                if (isset($params['id'])) {
                    error_log("Router::executeRoute - Calling getAbsenceDetails with ID: " . $params['id']);
                    $controller->getAbsenceDetails($params['id']);
                    return;
                }

                // If we don't have an 'id' parameter but have a numeric parameter, use that
                if (!empty($params) && is_numeric(reset($params))) {
                    $id = reset($params);
                    error_log("Router::executeRoute - Calling getAbsenceDetails with numeric parameter: " . $id);
                    $controller->getAbsenceDetails($id);
                    return;
                }
            }

            // Call action with parameters for other routes
            if (method_exists($controller, $action)) {
                // For AdminController methods that expect ID parameters, pass them correctly
                if ($controllerName === 'AdminController' && !empty($params)) {
                    if (isset($params['id'])) {
                        // Pass the ID as the first parameter
                        $controller->$action($params['id']);
                    } else {
                        // Pass all parameters as array values
                        call_user_func_array([$controller, $action], array_values($params));
                    }
                } else {
                    call_user_func_array([$controller, $action], array_values($params));
                }
            } else {
                // Action not found
                error_log("Router::executeRoute - Action not found: $action");
                $this->notFound();
            }
        } else {
            // Controller not found
            error_log("Router::executeRoute - Controller file not found: $controllerFile");
            $this->notFound();
        }
    }

    // Handle 404 errors
    private function notFound() {
        if (is_callable($this->notFoundCallback)) {
            call_user_func($this->notFoundCallback);
        } else {
            header("HTTP/1.0 404 Not Found");
            echo '404 Page Not Found';
        }
        exit;
    }
}
