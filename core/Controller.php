<?php
// Base controller class that all controllers will extend

class Controller {
    // Load a view
    protected function view($view, $data = []) {
        // Extract data so it can be used directly in the view
        if (is_array($data)) {
            extract($data);
        }
        
        // Include the view file
        $viewFile = __DIR__ . '/../app/views/' . $view . '.php';
        if (file_exists($viewFile)) {
            require_once $viewFile;
        } else {
            die("View {$view} not found");
        }
    }
    
    // Redirect to another page
    protected function redirect($url) {
        header("Location: " . $url);
        exit();
    }
}
