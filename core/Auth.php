<?php
/**
 * Authentication and Authorization Core Class
 * Provides methods for checking user authentication and role-based access controls
 */

class Auth {
    /**
     * Check if a user is authenticated
     *
     * @return bool True if user is authenticated, false otherwise
     */
    public static function isAuthenticated() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }

    /**
     * Check if current user has a specific role
     *
     * @param string|array $role Role or array of roles to check
     * @return bool True if user has the role, false otherwise
     */
    public static function hasRole($role) {
        if (!self::isAuthenticated()) {
            return false;
        }

        // If checking for multiple roles
        if (is_array($role)) {
            return in_array($_SESSION['role'], $role);
        }

        return $_SESSION['role'] === $role;
    }

    /**
     * Check if user has permission to access a resource based on role
     *
     * @param string $permission The permission to check
     * @return bool True if user has permission, false otherwise
     */
    public static function hasPermission($permission) {
        if (!self::isAuthenticated()) {
            return false;
        }

        // Define role-based permissions
        $rolePermissions = [
            'admin' => [
                'view_all_users',
                'manage_users',
                'view_all_demandes',
                'manage_system_settings',
                'manage_jours_feries',
                'manage_leave_balances',
                'manage_department_leave_balances',
                'approve_reject_any_demande',
                'create_demande',
                'cancel_any_demande',
                'edit_profile_information'
            ],
            'responsable' => [
                'view_team_members',
                'view_team_demandes',
                'approve_reject_team_demandes',
                'create_demande',
                'cancel_own_demande',
                'view_team_statistics'
            ],
            'planificateur' => [
                'view_all_demandes',
                'approve_reject_any_demande',
                'view_planning',
                'manage_jours_feries',
                'create_demande',
                'cancel_own_demande'
            ],
            'employe' => [
                'create_demande',
                'view_own_demandes',
                'cancel_own_demande'
            ]
        ];

        // Check if the user's role has the requested permission
        if (isset($_SESSION['role']) && isset($rolePermissions[$_SESSION['role']])) {
            return in_array($permission, $rolePermissions[$_SESSION['role']]);
        }

        return false;
    }

    /**
     * Redirect to login if user is not authenticated
     *
     * @return void
     */
    public static function requireLogin() {
        if (!self::isAuthenticated()) {
            header('Location: /login');
            exit();
        }
    }

    /**
     * Redirect to home if user doesn't have required role
     *
     * @param string|array $role Role or array of roles required
     * @return void
     */
    public static function requireRole($role) {
        self::requireLogin();

        if (!self::hasRole($role)) {
            header('Location: /home');
            exit();
        }
    }

    /**
     * Redirect to home if user doesn't have required permission
     *
     * @param string $permission Permission required
     * @return void
     */
    public static function requirePermission($permission) {
        self::requireLogin();

        if (!self::hasPermission($permission)) {
            header('Location: /home');
            exit();
        }
    }
}
