<?php
// Autoloader for automatically loading classes

class Autoloader {
    public static function register() {
        spl_autoload_register(function ($className) {
            // Define directories to search
            $directories = [
                __DIR__ . '/../core/',
                __DIR__ . '/../app/controllers/',
                __DIR__ . '/../app/models/'
            ];
            
            // Search in each directory
            foreach ($directories as $directory) {
                $file = $directory . $className . '.php';
                if (file_exists($file)) {
                    require_once $file;
                    return;
                }
            }
        });
    }
}
