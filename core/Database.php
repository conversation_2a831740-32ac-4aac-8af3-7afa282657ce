<?php
// This file is based on the original db.php but refactored into a class

class Database {
    private static $instance = null;
    private $pdo;

    private function __construct() {
        // Using config from the Config class
        require_once __DIR__ . '/../config/config.php';

        $host = Config::DB_HOST;
        $port = Config::DB_PORT;
        $dbname = Config::DB_NAME;
        $user = Config::DB_USER;
        $pass = Config::DB_PASS;

        try {
            // Set connection timeout to avoid hanging
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_TIMEOUT => 5, // 5 seconds timeout
                PDO::ATTR_EMULATE_PREPARES => false
            ];

            $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
            error_log("Database::__construct - Connecting to database: $dsn");

            $this->pdo = new PDO($dsn, $user, $pass, $options);

            // Set character set to UTF-8
            $this->pdo->exec("SET NAMES utf8mb4");
            error_log("Database::__construct - Connection successful");
        } catch (PDOException $e) {
            error_log("Database::__construct - Connection error: " . $e->getMessage());
            // Don't die here, let the calling code handle the error
            $this->pdo = null;
            throw $e;
        }
    }

    // Singleton pattern to ensure only one database connection
    public static function getInstance() {
        if (self::$instance === null) {
            try {
                self::$instance = new self();
                if (!self::$instance->pdo) {
                    error_log("Database::getInstance - Failed to create instance (PDO is null)");
                    throw new Exception("Failed to create database instance");
                }
            } catch (Exception $e) {
                error_log("Database::getInstance - Exception: " . $e->getMessage());
                // Create an instance with null PDO to avoid repeated connection attempts
                self::$instance = new self();
                self::$instance->pdo = null;
            }
        }
        return self::$instance;
    }

    // Get the PDO connection
    public function getConnection() {
        return $this->pdo;
    }
}
