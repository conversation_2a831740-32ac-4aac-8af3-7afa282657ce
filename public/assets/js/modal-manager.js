/**
 * Universal Modal Manager for Gestion Congés Application
 * Provides consistent click-outside-to-close functionality for all modals
 */

class ModalManager {
    constructor() {
        this.activeModals = new Set();
        this.init();
    }

    init() {
        // Initialize on DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupModalListeners());
        } else {
            this.setupModalListeners();
        }
    }

    setupModalListeners() {
        // Find all modal elements and set up click-outside listeners
        this.findAndSetupModals();

        // Set up mutation observer to handle dynamically added modals
        this.setupMutationObserver();

        // Set up global escape key listener
        this.setupEscapeKeyListener();
    }

    findAndSetupModals() {
        // Common modal selectors used in the application
        const modalSelectors = [
            '.modal', // Generic modal class
            '[id$="Modal"]', // Elements ending with "Modal"
            '[id$="-modal"]', // Elements ending with "-modal"
            '.fixed.inset-0.bg-black.bg-opacity-50', // Tailwind modal backdrop pattern
            '.fixed.inset-0.bg-gray-600.bg-opacity-50', // Alternative backdrop pattern
            '.fixed.inset-0.bg-gray-500.bg-opacity-75' // Another backdrop pattern
        ];

        modalSelectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(modal => {
                this.setupModalClickOutside(modal);
            });
        });
    }

    setupModalClickOutside(modal) {
        // Skip if already set up
        if (modal.hasAttribute('data-modal-manager-setup')) {
            return;
        }

        // Mark as set up
        modal.setAttribute('data-modal-manager-setup', 'true');

        // Add click listener for backdrop clicks
        modal.addEventListener('click', (e) => {
            // Only close if clicking directly on the modal backdrop (not on modal content)
            if (e.target === modal) {
                this.closeModal(modal);
            }
        });

        // Find modal content and prevent clicks from bubbling up
        const modalContent = this.findModalContent(modal);
        if (modalContent) {
            modalContent.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
    }

    findModalContent(modal) {
        // Common modal content selectors
        const contentSelectors = [
            '.modal-content',
            '.modal-card',
            '.confirmation-modal-card',
            '.bg-white.rounded-lg',
            '.bg-white.rounded',
            '[class*="modal-card"]'
        ];

        for (const selector of contentSelectors) {
            const content = modal.querySelector(selector);
            if (content) {
                return content;
            }
        }

        // Fallback: find first child that looks like modal content
        const children = modal.children;
        for (const child of children) {
            if (child.classList.contains('bg-white') ||
                child.classList.contains('modal') ||
                child.tagName.toLowerCase() === 'form') {
                return child;
            }
        }

        return null;
    }

    closeModal(modal) {
        const modalId = modal.id;

        // Try to use existing close functions first
        if (this.tryExistingCloseFunction(modalId)) {
            return;
        }

        // Fallback to generic close
        this.genericCloseModal(modal);
    }

    tryExistingCloseFunction(modalId) {
        // Map of modal IDs to their specific close functions
        const closeFunctions = {
            'policyModal': 'closePolicyModal',
            'holidaysModal': 'closeHolidaysModal',
            'detailsModal': 'closeModal',
            'absenceModal': 'closeModal',
            'holidayModal': 'closeModal',
            'eventModal': 'closeEventModal',
            'memberDetailsModal': 'closeMemberModal',
            'confirmationModal': () => {
                if (window.confirmationModal && window.confirmationModal.hide) {
                    window.confirmationModal.hide(false);
                    return true;
                }
                return false;
            }
        };

        // Try specific close function
        const closeFunction = closeFunctions[modalId];
        if (closeFunction) {
            if (typeof closeFunction === 'string' && window[closeFunction]) {
                window[closeFunction]();
                return true;
            } else if (typeof closeFunction === 'function') {
                return closeFunction();
            }
        }

        // Try generic closeModal function with modalId
        if (window.closeModal && typeof window.closeModal === 'function') {
            try {
                window.closeModal(modalId);
                return true;
            } catch (e) {
                // Try without parameter
                try {
                    window.closeModal();
                    return true;
                } catch (e2) {
                    // Continue to fallback
                }
            }
        }

        // Try closeDocumentModal for document modals
        if (modalId.includes('document') && window.closeDocumentModal) {
            window.closeDocumentModal(modalId);
            return true;
        }

        return false;
    }

    genericCloseModal(modal) {
        // Generic modal closing with animation support
        const modalCard = modal.querySelector('.modal-card, .confirmation-modal-card, .modal-content');

        if (modalCard) {
            // Animate out if classes are available
            if (modalCard.classList.contains('modal-show')) {
                modal.classList.remove('modal-show');
                modalCard.classList.remove('modal-show');

                setTimeout(() => {
                    modal.classList.add('hidden');
                    document.body.style.overflow = '';
                }, 300);
            } else if (modalCard.classList.contains('scale-100')) {
                modalCard.classList.remove('scale-100', 'opacity-100');
                modalCard.classList.add('scale-95', 'opacity-0');

                setTimeout(() => {
                    modal.classList.add('hidden');
                    document.body.style.overflow = '';
                }, 300);
            } else {
                // Simple hide
                modal.classList.add('hidden');
                document.body.style.overflow = '';
            }
        } else {
            // Simple hide
            modal.classList.add('hidden');
            document.body.style.overflow = '';
        }
    }

    setupMutationObserver() {
        // Watch for dynamically added modals
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Check if the added node is a modal
                        if (this.isModal(node)) {
                            this.setupModalClickOutside(node);
                        }

                        // Check for modals within the added node
                        const modals = node.querySelectorAll && node.querySelectorAll('.modal, [id$="Modal"], [id$="-modal"]');
                        if (modals) {
                            modals.forEach(modal => this.setupModalClickOutside(modal));
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    isModal(element) {
        return element.classList.contains('modal') ||
               element.id.endsWith('Modal') ||
               element.id.endsWith('-modal') ||
               (element.classList.contains('fixed') &&
                element.classList.contains('inset-0') &&
                (element.classList.contains('bg-black') ||
                 element.classList.contains('bg-gray-600') ||
                 element.classList.contains('bg-gray-500')));
    }

    setupEscapeKeyListener() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // Find the topmost visible modal
                const visibleModals = document.querySelectorAll('.modal:not(.hidden), [id$="Modal"]:not(.hidden), [id$="-modal"]:not(.hidden)');
                if (visibleModals.length > 0) {
                    const topModal = visibleModals[visibleModals.length - 1];
                    this.closeModal(topModal);
                }
            }
        });
    }
}

// Initialize the modal manager
window.modalManager = new ModalManager();

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModalManager;
}
