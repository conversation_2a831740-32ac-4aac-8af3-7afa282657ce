document.addEventListener('DOMContentLoaded', function() {
        // Get form elements
        const dateDebut = document.getElementById('date_debut');
        const dateFin = document.getElementById('date_fin');
        const demiJourDebut = document.getElementById('demi_jour_debut');
        const demiJourFin = document.getElementById('demi_jour_fin');
        const periodeDebut = document.getElementById('periode_debut');
        const periodeFin = document.getElementById('periode_fin');
        const calculJours = document.getElementById('calculJours');
        const nbJours = document.getElementById('nbJours');

        // Enable/disable period selectors
        demiJourDebut.addEventListener('change', function() {
            periodeDebut.disabled = !this.checked;
        });

        demiJourFin.addEventListener('change', function() {
            periodeFin.disabled = !this.checked;
        });

        // Calculate number of days
        function updateDaysCount() {
            if (dateDebut.value && dateFin.value) {
                try {
                    const start = new Date(dateDebut.value);
                    const end = new Date(dateFin.value);

                    if (!isNaN(start) && !isNaN(end) && end >= start) {
                        // Calculate days difference (+1 because inclusive)
                        const diffTime = end - start;
                        let diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

                        if (demiJourDebut.checked) {
                            diffDays -= 0.5;
                        }

                        if (demiJourFin.checked) {
                            diffDays -= 0.5;
                        }

                        nbJours.textContent = diffDays;
                        calculJours.classList.remove('hidden');
                    } else {
                        calculJours.classList.add('hidden');
                    }
                } catch (e) {
                    calculJours.classList.add('hidden');
                }
            } else {
                calculJours.classList.add('hidden');
            }
        }

        // Format date inputs to make sure they're in ISO format (YYYY-MM-DD)
        function formatDateInput(input) {
            input.addEventListener('input', function(e) {
                // Check if the entered value matches the French formats
                const frenchDatePattern1 = /^(\d{2})\/(\d{2})\/(\d{4})$/; // DD/MM/YYYY
                const frenchDatePattern2 = /^(\d{2})-(\d{2})-(\d{4})$/; // DD-MM-YYYY
                const value = e.target.value;

                if (frenchDatePattern1.test(value)) {
                    // Convert from DD/MM/YYYY to YYYY-MM-DD
                    const matches = value.match(frenchDatePattern1);
                    const formattedDate = `${matches[3]}-${matches[2]}-${matches[1]}`;
                    e.target.value = formattedDate;
                } else if (frenchDatePattern2.test(value)) {
                    // Convert from DD-MM-YYYY to YYYY-MM-DD
                    const matches = value.match(frenchDatePattern2);
                    const formattedDate = `${matches[3]}-${matches[2]}-${matches[1]}`;
                    e.target.value = formattedDate;
                }
            });
        }

        // Apply date formatting to date inputs
        formatDateInput(dateDebut);
        formatDateInput(dateFin);

        // Add blur event to handle manual entries
        dateDebut.addEventListener('blur', function() {
            // Try to convert from DD/MM/YYYY format when losing focus
            const value = this.value;
            if (value && value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
                const parts = value.split(/[\/\-]/);
                if (parts.length === 3) {
                    this.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                }
            }
        });

        dateFin.addEventListener('blur', function() {
            // Try to convert from DD/MM/YYYY format when losing focus
            const value = this.value;
            if (value && value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
                const parts = value.split(/[\/\-]/);
                if (parts.length === 3) {
                    this.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                }
            }
        });

        // Add event listeners for date changes
        dateDebut.addEventListener('change', updateDaysCount);
        dateFin.addEventListener('change', updateDaysCount);

        // Initialize if values are already set
        updateDaysCount();

        // Submit form validation
        document.querySelector('form').addEventListener('submit', function(event) {
            try {
                // At this point, all dates should be in YYYY-MM-DD format due to our conversion handlers
                const startDate = new Date(dateDebut.value);
                const endDate = new Date(dateFin.value);

                if (isNaN(startDate) || isNaN(endDate)) {
                    event.preventDefault();
                    alert(
                        'Format de date invalide. Veuillez utiliser le format JJ/MM/AAAA ou AAAA-MM-JJ.');

                    // Try to automatically fix the format if possible
                    if (dateDebut.value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
                        const parts = dateDebut.value.split(/[\/\-]/);
                        if (parts.length === 3) {
                            dateDebut.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                        }
                    }

                    if (dateFin.value.match(/^\d{2}[\/\-]\d{2}[\/\-]\d{4}$/)) {
                        const parts = dateFin.value.split(/[\/\-]/);
                        if (parts.length === 3) {
                            dateFin.value = `${parts[2]}-${parts[1]}-${parts[0]}`;
                        }
                    }
                } else if (endDate < startDate) {
                    event.preventDefault();
                    alert('La date de fin doit être postérieure ou égale à la date de début.');
                }
            } catch (e) {
                event.preventDefault();
                alert('Format de date invalide. Veuillez utiliser le format JJ/MM/AAAA ou AAAA-MM-JJ.');
            }
        });

        // File upload handling
        const fileInput = document.getElementById('justificatif');
        const fileNameSpan = document.getElementById('file-name');

        fileInput.addEventListener('change', function() {
            if (this.files && this.files.length > 0) {
                const file = this.files[0];

                // Check file size (max 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    alert('Le fichier est trop volumineux. La taille maximale est de 5 MB.');
                    this.value = "";
                    fileNameSpan.textContent = 'Aucun fichier sélectionné';
                    return;
                }

                // Display file name
                fileNameSpan.textContent = file.name;
            } else {
                fileNameSpan.textContent = 'Aucun fichier sélectionné';
            }
        });
    });