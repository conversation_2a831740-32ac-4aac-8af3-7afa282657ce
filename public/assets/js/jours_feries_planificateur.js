// Jours feries planificateur functionality
document.addEventListener('DOMContentLoaded', function() {
    try {
        const mainContent = document.getElementById('main-content');

        // Client-side search functionality
        const searchInput = document.getElementById('search');
        if (searchInput) {
            searchInput.addEventListener('input', function(e) {
                try {
                    const searchValue = e.target.value.toLowerCase();
                    filterTable();
                } catch (error) {
                    console.error('Error in search input handler:', error);
                }
            });
        }

        // Country filter - server-side
        const paysSelect = document.getElementById('pays');
        if (paysSelect) {
            paysSelect.addEventListener('change', function(e) {
                try {
                    applyServerFilters();
                } catch (error) {
                    console.error('Error in pays filter handler:', error);
                }
            });
        }

        // Year filter - server-side
        const yearSelect = document.getElementById('year');
        if (yearSelect) {
            yearSelect.addEventListener('change', function(e) {
                try {
                    applyServerFilters();
                } catch (error) {
                    console.error('Error in year filter handler:', error);
                }
            });
        }

        // Function to filter the table client-side
        function filterTable() {
            try {
                const searchInput = document.getElementById('search');
                if (!searchInput) return;

                const searchValue = searchInput.value.toLowerCase();
                const rows = document.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    try {
                        // Skip the "no results" row if it exists
                        if (row.querySelector('td[colspan]')) {
                            return;
                        }

                        const nameCell = row.querySelector('td:nth-child(1)');
                        const paysCell = row.querySelector('td:nth-child(4)');

                        if (!nameCell || !paysCell) return;

                        const name = nameCell.textContent.toLowerCase();
                        const pays = paysCell.textContent.toLowerCase();

                        // Show the row if the search term is found in either name or country
                        if (name.includes(searchValue) || pays.includes(searchValue)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    } catch (error) {
                        console.error('Error processing table row:', error);
                    }
                });

                // Check if all rows are hidden, show a "no results" message
                let allHidden = true;
                rows.forEach(row => {
                    if (row.style.display !== 'none' && !row.querySelector('td[colspan]')) {
                        allHidden = false;
                    }
                });

                // Get or create the "no results" row
                let noResultsRow = document.querySelector('tr.no-results-row');
                if (!noResultsRow) {
                    const tbody = document.querySelector('tbody');
                    if (tbody) {
                        noResultsRow = document.createElement('tr');
                        noResultsRow.className = 'no-results-row';
                        noResultsRow.innerHTML = '<td colspan="5" class="px-6 py-4 text-center text-gray-500">Aucun jour férié trouvé</td>';
                        tbody.appendChild(noResultsRow);
                    }
                }

                // Show or hide the "no results" row
                if (noResultsRow) {
                    noResultsRow.style.display = allHidden ? '' : 'none';
                }
            } catch (error) {
                console.error('Error in filterTable:', error);
            }
        }

            // Function to apply server-side filters (for year and country)
            function applyServerFilters() {
                try {
                    const paysValue = document.getElementById('pays')?.value || '';
                    const yearValue = document.getElementById('year')?.value || '';

                    // Build URL with parameters
                    let url = '/jours-feries';
                    const params = new URLSearchParams();

                    if (yearValue) {
                        params.append('year', yearValue);
                    }

                    if (paysValue) {
                        params.append('pays', paysValue);
                    }

                    if (params.toString()) {
                        url += '?' + params.toString();
                    }

                    // Update URL without page reload using History API
                    history.pushState({}, '', url);

                    // Fetch new data via AJAX
                    fetchFilteredData(paysValue, yearValue);
                } catch (error) {
                    console.error('Error in applyServerFilters:', error);
                    // Fallback to page reload only if there's an error
                    fallbackPageReload();
                }
            }

            // Function to fetch filtered data via AJAX
            function fetchFilteredData(pays, year) {
                try {
                    const formData = new FormData();
                    if (pays) formData.append('pays', pays);
                    if (year) formData.append('year', year);
                    formData.append('ajax', '1');

                    fetch('/jours-feries', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.text();
                    })
                    .then(html => {
                        // Update table content
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(html, 'text/html');
                        const newTableBody = doc.querySelector('tbody');
                        const currentTableBody = document.querySelector('tbody');

                        if (newTableBody && currentTableBody) {
                            currentTableBody.innerHTML = newTableBody.innerHTML;
                            // Re-apply current search filter
                            filterTable();
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching filtered data:', error);
                        // Fallback to page reload
                        fallbackPageReload();
                    });
                } catch (error) {
                    console.error('Error in fetchFilteredData:', error);
                    // Fallback to page reload
                    fallbackPageReload();
                }
            }

            // Fallback function for page reload (only used when AJAX fails)
            function fallbackPageReload() {
                try {
                    const paysValue = document.getElementById('pays')?.value || '';
                    const yearValue = document.getElementById('year')?.value || '';

                    let url = '/jours-feries';
                    const params = new URLSearchParams();

                    if (yearValue) {
                        params.append('year', yearValue);
                    }

                    if (paysValue) {
                        params.append('pays', paysValue);
                    }

                    if (params.toString()) {
                        url += '?' + params.toString();
                    }

                    window.location.href = url;
                } catch (error) {
                    console.error('Error in fallbackPageReload:', error);
                }
            }
    } catch (error) {
        console.error('Error initializing jours_feries_planificateur:', error);
    }
});