// statistiques_planificateur.js
document.addEventListener('DOMContentLoaded', function() {
    try {
        // Year filter change handler
        const yearFilter = document.getElementById('yearFilter');
        if (yearFilter) {
            yearFilter.addEventListener('change', function() {
                try {
                    const year = this.value;
                    const url = '/statistiques?year=' + encodeURIComponent(year);

                    // Update URL without page reload using History API
                    history.pushState({}, '', url);

                    // Fetch new data via AJAX
                    fetchStatisticsData(year);
                } catch (error) {
                    console.error('Error in year filter handler:', error);
                    // Fallback to page reload
                    window.location.href = '/statistiques?year=' + encodeURIComponent(this.value);
                }
            });
        }

        // Function to fetch statistics data via AJAX
        function fetchStatisticsData(year) {
            try {
                const formData = new FormData();
                formData.append('year', year);
                formData.append('ajax', '1');

                fetch('/statistiques', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.text();
                })
                .then(html => {
                    // Update the statistics content
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newContent = doc.querySelector('#statistics-content');
                    const currentContent = document.querySelector('#statistics-content');

                    if (newContent && currentContent) {
                        currentContent.innerHTML = newContent.innerHTML;
                        // Re-initialize any charts if needed
                        if (typeof initializeCharts === 'function') {
                            initializeCharts();
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching statistics data:', error);
                    // Fallback to page reload
                    window.location.href = '/statistiques?year=' + encodeURIComponent(year);
                });
            } catch (error) {
                console.error('Error in fetchStatisticsData:', error);
                // Fallback to page reload
                window.location.href = '/statistiques?year=' + encodeURIComponent(year);
            }
        }
    } catch (error) {
        console.error('Error initializing statistiques_planificateur:', error);
    }

    // Chart: Capacity Forecast
    const capacityForecastCtx = document.getElementById('capacityForecastChart');
    if (capacityForecastCtx) {
        new Chart(capacityForecastCtx, {
            type: 'line',
            data: {
                labels: monthlyLabels || ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'],
                datasets: [{
                    label: 'Prévision de capacité (%)',
                    data: capacityData || [85, 80, 75, 60, 85, 90, 70, 65, 80, 85, 90, 95],
                    borderColor: 'rgb(20, 184, 166)',
                    backgroundColor: 'rgba(20, 184, 166, 0.1)',
                    tension: 0.3,
                    fill: true
                }, {
                    label: 'Seuil critique',
                    data: [60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60],
                    borderColor: 'rgba(239, 68, 68, 0.7)',
                    borderDash: [5, 5],
                    borderWidth: 2,
                    pointRadius: 0,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                aspectRatio: 2,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 12,
                            padding: 10,
                            font: {
                                size: 11
                            }
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.raw + '%';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        min: 0,
                        max: 100,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            },
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    }

    // Chart: Department Capacity
    const departmentCapacityCtx = document.getElementById('departmentCapacityChart');
    if (departmentCapacityCtx) {
        new Chart(departmentCapacityCtx, {
            type: 'bar',
            data: {
                labels: departmentLabels || ['Développement', 'Marketing', 'RH', 'Finance', 'Support'],
                datasets: [{
                    label: 'Jours de congés moyens',
                    data: departmentData || [18, 15, 20, 22, 17],
                    backgroundColor: 'rgba(20, 184, 166, 0.8)',
                    barPercentage: 0.7,
                    categoryPercentage: 0.8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: true,
                aspectRatio: 2,
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 12,
                            padding: 10,
                            font: {
                                size: 11
                            }
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                return context.raw + ' jours/employé';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        min: 0,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value + ' j';
                            },
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    }
                }
            }
        });
    }
});