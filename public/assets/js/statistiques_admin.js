 // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mainContent = document.getElementById('main-content');

            // Chart: Monthly Requests
            const monthlyRequestsCtx = document.getElementById('monthlyRequestsChart').getContext('2d');
            new Chart(monthlyRequestsCtx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
                    datasets: [{
                        label: 'Demandes de congés',
                        // data: <?= json_encode(array_values($demandesParMois)) ?>,
                        data: chartData.demandesParMois,
                        borderColor: 'rgb(79, 70, 229)',
                        backgroundColor: 'rgba(79, 70, 229, 0.1)',
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Chart: Requests by Type
            const requestsByTypeCtx = document.getElementById('requestsByTypeChart').getContext('2d');
            new Chart(requestsByTypeCtx, {
                type: 'doughnut',
                data: {
                    labels: chartData.demandesParTypeLabels,
                    // labels: <?= json_encode(array_keys($demandesParType)) ?>,
                    datasets: [{
                        // data: <?= json_encode(array_values($demandesParType)) ?>,
                        data: chartData.demandesParTypeValues,
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 15
                            }
                        }
                    },
                    cutout: '65%'
                }
            });

            // Chart: Busy Months
            const busyMonthsCtx = document.getElementById('busyMonthsChart').getContext('2d');
            new Chart(busyMonthsCtx, {
                type: 'bar',
                data: {
                    labels: ['Août', 'Juillet', 'Décembre', 'Mai', 'Avril'],
                    datasets: [{
                        label: 'Demandes',
                        data: [40, 35, 32, 25, 20],
                        backgroundColor: [
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(59, 130, 246, 0.8)'
                        ],
                        borderWidth: 0,
                        borderRadius: 4,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        y: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        });