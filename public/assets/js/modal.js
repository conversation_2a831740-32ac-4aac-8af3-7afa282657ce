// Modern confirmation modal function
function confirmSuccess(message, confirmText = 'Confirmer', cancelText = 'Annuler') {
    return new Promise((resolve) => {
        // Create modal HTML
        const modalHTML = `
            <div id="confirmationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50" style="opacity: 0;">
                <div class="confirmation-modal-card bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0 w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                            <i class="fas fa-question text-purple-600"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900">Confirmation</h3>
                    </div>
                    <div class="mb-6">
                        <p class="text-gray-700">${message}</p>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button id="cancelBtn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                            ${cancelText}
                        </button>
                        <button id="confirmBtn" class="px-4 py-2 border border-transparent rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                            ${confirmText}
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        const modal = document.getElementById('confirmationModal');
        const modalCard = modal.querySelector('.confirmation-modal-card');
        const confirmBtn = document.getElementById('confirmBtn');
        const cancelBtn = document.getElementById('cancelBtn');

        // Show modal with animation
        setTimeout(() => {
            modal.style.opacity = '1';
            modalCard.classList.add('modal-show');
        }, 10);

        // Handle confirm
        confirmBtn.addEventListener('click', () => {
            closeModal();
            resolve(true);
        });

        // Handle cancel
        cancelBtn.addEventListener('click', () => {
            closeModal();
            resolve(false);
        });

        // Handle backdrop click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
                resolve(false);
            }
        });

        // Handle escape key
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                closeModal();
                resolve(false);
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);

        function closeModal() {
            modal.style.opacity = '0';
            modalCard.classList.remove('modal-show');
            setTimeout(() => {
                modal.remove();
            }, 300);
        }
    });
}

// Alternative simple confirmation for fallback
function simpleConfirm(message) {
    return confirm(message);
}

// Auto-fallback if confirmSuccess fails
window.confirmSuccess = window.confirmSuccess || simpleConfirm;
