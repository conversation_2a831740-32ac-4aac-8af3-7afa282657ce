 // Enhanced Modal functions with click-outside support
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.classList.remove('hidden');

            // The modal manager will automatically handle click-outside functionality
            // No need to manually add listeners here as it's handled globally
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            modal.classList.add('hidden');

            // Reset error/success messages and form fields
            const errorElems = document.querySelectorAll(`#${modalId} [id$="-error"]`);
            const successElems = document.querySelectorAll(`#${modalId} [id$="-success"]`);

            errorElems.forEach(elem => elem.classList.add('hidden'));
            successElems.forEach(elem => elem.classList.add('hidden'));

            // Reset form if present
            const form = modal.querySelector('form');
            if (form) form.reset();

            // Restore body scroll
            document.body.style.overflow = '';
        }

        // Document modal handler
        let currentDocumentType = '';
        function openDocumentModal(documentType) {
            // Map the old format to the new format expected by the server
            const typeMapping = {
                'work-certificate': 'attestation_travail',
                'payslip': 'fiche_paie',
                'salary-certificate': 'attestation_salaire'
            };

            currentDocumentType = typeMapping[documentType] || documentType;
            const modal = document.getElementById('document-request-modal');
            const title = document.getElementById('document-modal-title');
            const typeSummary = document.getElementById('document-type-summary');
            const dateSummary = document.getElementById('document-date-summary');

            // Document type specific content
            switch(documentType) {
                case 'work-certificate':
                    title.textContent = "Demande d'attestation de travail";
                    typeSummary.textContent = "Attestation de travail";
                    break;
                case 'payslip':
                    title.textContent = "Demande de fiche de paie";
                    typeSummary.textContent = "Fiche de paie";
                    break;
                case 'salary-certificate':
                    title.textContent = "Demande d'attestation de salaire";
                    typeSummary.textContent = "Attestation de salaire";
                    break;
            }

            // Set current date and time as request date
            const now = new Date();
            const currentDateTime = now.toLocaleString('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });

            dateSummary.textContent = currentDateTime;

            openModal('document-request-modal');
        }

        // Document request
        function submitDocumentRequest() {
            // Map document types to user-friendly names (using server format)
            const documentNames = {
                'attestation_travail': 'Attestation de travail',
                'fiche_paie': 'Fiche de paie',
                'attestation_salaire': 'Attestation de salaire'
            };

            const documentName = documentNames[currentDocumentType] || currentDocumentType;

            // Close the original modal immediately
            closeModal('document-request-modal');

            // Send AJAX request to create document request
            fetch('/demande-document', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    type: currentDocumentType,
                    document_name: documentName
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success modal
                    showSuccessModal('Votre demande sera envoyée au service RH pour traitement');
                } else {
                    // Show error modal
                    showErrorModal(`Erreur lors de l'envoi de votre demande : ${data.message || 'Erreur inconnue'}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorModal('Erreur lors de l\'envoi de votre demande. Veuillez réessayer.');
            });
        }

        // Show success modal
        function showSuccessModal(message) {
            const messageElement = document.getElementById('success-message');
            messageElement.textContent = message;
            openModal('success-modal');
        }

        // Show error modal
        function showErrorModal(message) {
            const messageElement = document.getElementById('error-message');
            messageElement.textContent = message;
            openModal('error-modal');
        }



        // Form submission handlers with AJAX
        document.addEventListener('DOMContentLoaded', function() {
            // Photo upload form
            const photoForm = document.getElementById('photo-upload-form');
            if (photoForm) {
                photoForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const errorElement = document.getElementById('photo-upload-error');

                    // Check if a file was selected
                    const fileInput = document.getElementById('photo-upload');
                    if (!fileInput.files || !fileInput.files[0]) {
                        errorElement.textContent = 'Veuillez sélectionner une image.';
                        errorElement.classList.remove('hidden');
                        return;
                    }

                    // Check file size (max 5MB)
                    if (fileInput.files[0].size > 5 * 1024 * 1024) {
                        errorElement.textContent = 'L\'image est trop grande. La taille maximale est de 5 Mo.';
                        errorElement.classList.remove('hidden');
                        return;
                    }

                    // Reset error message
                    errorElement.classList.add('hidden');

                    fetch('/user/uploadPhoto', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            // Update profile pic on success
                            const profilePic = document.getElementById('profile-pic');
                            if (profilePic) {
                                // Add timestamp to prevent caching
                                profilePic.src = data.photoUrl + '?t=' + new Date().getTime();
                                console.log('Updated profile pic src to:', profilePic.src);
                            } else {
                                console.error('Profile pic element not found');
                            }

                            // Also update preview if it exists
                            const photoPreview = document.getElementById('photo-preview');
                            if (photoPreview) {
                                photoPreview.src = data.photoUrl + '?t=' + new Date().getTime();
                            }

                            closeModal('upload-photo-modal');

                            // Show success message
                            const successMsg = document.createElement('div');
                            successMsg.className = 'bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6';
                            successMsg.innerHTML = `<p>${data.message}</p>`;

                            const header = document.querySelector('header');
                            header.parentNode.insertBefore(successMsg, header.nextSibling);

                            // Remove message after 5 seconds
                            setTimeout(() => {
                                successMsg.remove();
                            }, 5000);
                        } else {
                            // Show error message
                            errorElement.textContent = data.message || 'Une erreur est survenue lors du téléchargement de la photo.';
                            errorElement.classList.remove('hidden');
                            console.error('Upload error:', data);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        errorElement.textContent = 'Une erreur est survenue lors du téléchargement de la photo. Veuillez réessayer.';
                        errorElement.classList.remove('hidden');
                    });
                });
            }

            // Edit profile form
            const profileForm = document.getElementById('edit-profile-form');
            if (profileForm) {
                profileForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const errorElement = document.getElementById('edit-profile-error');

                    // Reset error message
                    errorElement.classList.add('hidden');

                    fetch('/user/update', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Server returned ' + response.status + ': ' + response.statusText);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            // Close modal and reload page to show updated info
                            closeModal('edit-profile-modal');
                            location.reload();
                        } else {
                            // Show error message
                            errorElement.textContent = data.message;
                            errorElement.classList.remove('hidden');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        errorElement.textContent = 'Une erreur est survenue lors de la mise à jour du profil. Veuillez réessayer.';
                        errorElement.classList.remove('hidden');
                    });
                });
            }

            // Password validation functions
            function validatePasswordRequirements(password) {
                const requirements = {
                    length: password.length >= 8,
                    letter: /[a-zA-Z]/.test(password) && /[a-z]/.test(password) && /[A-Z]/.test(password),
                    number: /\d/.test(password),
                    special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
                };

                return requirements;
            }

            function updateModalRequirement(element, isValid) {
                const icon = element.querySelector('i');
                if (isValid) {
                    element.classList.remove('text-red-500');
                    element.classList.add('text-green-500');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-check');
                } else {
                    element.classList.remove('text-green-500');
                    element.classList.add('text-red-500');
                    icon.classList.remove('fa-check');
                    icon.classList.add('fa-times');
                }
            }

            function updateModalPasswordValidation() {
                const newPassword = document.getElementById('new-password');
                const confirmPassword = document.getElementById('confirm-password');
                const submitBtn = document.getElementById('modal-submit-btn');
                const passwordMatchMessage = document.getElementById('modal-password-match-message');

                // Password requirement elements
                const lengthReq = document.getElementById('modal-length-req');
                const letterReq = document.getElementById('modal-letter-req');
                const numberReq = document.getElementById('modal-number-req');
                const specialReq = document.getElementById('modal-special-req');

                if (!newPassword || !confirmPassword || !submitBtn) return;

                const requirements = validatePasswordRequirements(newPassword.value);

                // Update visual feedback for each requirement individually
                updateModalRequirement(lengthReq, requirements.length);
                updateModalRequirement(letterReq, requirements.letter);
                updateModalRequirement(numberReq, requirements.number);
                updateModalRequirement(specialReq, requirements.special);

                // Update password field border only when ALL requirements are met
                const allRequirementsMet = Object.values(requirements).every(req => req);
                if (allRequirementsMet) {
                    newPassword.classList.remove('border-red-500');
                    newPassword.classList.add('border-green-500');
                } else {
                    newPassword.classList.remove('border-green-500');
                    newPassword.classList.add('border-red-500');
                }

                // Check password match
                const passwordsMatch = newPassword.value === confirmPassword.value && confirmPassword.value !== '';

                if (passwordsMatch) {
                    confirmPassword.classList.remove('border-red-500');
                    confirmPassword.classList.add('border-green-500');
                    passwordMatchMessage.classList.remove('hidden');
                } else if (confirmPassword.value !== '') {
                    confirmPassword.classList.remove('border-green-500');
                    confirmPassword.classList.add('border-red-500');
                    passwordMatchMessage.classList.add('hidden');
                } else {
                    confirmPassword.classList.remove('border-red-500', 'border-green-500');
                    passwordMatchMessage.classList.add('hidden');
                }

                // Update submit button only when ALL requirements are met AND passwords match
                if (allRequirementsMet && passwordsMatch) {
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('bg-gray-400', 'cursor-not-allowed');
                    submitBtn.classList.add('bg-blue-500', 'hover:bg-blue-600');
                } else {
                    submitBtn.disabled = true;
                    submitBtn.classList.remove('bg-blue-500', 'hover:bg-blue-600');
                    submitBtn.classList.add('bg-gray-400', 'cursor-not-allowed');
                }
            }

            // Password change form
            const passwordForm = document.getElementById('password-change-form');
            if (passwordForm) {
                // Add event listeners for real-time validation
                const newPasswordField = document.getElementById('new-password');
                const confirmPasswordField = document.getElementById('confirm-password');

                if (newPasswordField) {
                    newPasswordField.addEventListener('input', updateModalPasswordValidation);
                }

                if (confirmPasswordField) {
                    confirmPasswordField.addEventListener('input', updateModalPasswordValidation);
                }

                // Initial validation
                updateModalPasswordValidation();

                // Reset form when modal is opened
                const changePasswordModal = document.getElementById('change-password-modal');
                if (changePasswordModal) {
                    // Listen for modal open events
                    const observer = new MutationObserver(function(mutations) {
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                                if (!changePasswordModal.classList.contains('hidden')) {
                                    // Modal is being opened, reset form
                                    resetPasswordForm();
                                }
                            }
                        });
                    });
                    observer.observe(changePasswordModal, { attributes: true });
                }

                function resetPasswordForm() {
                    // Clear all form fields
                    document.getElementById('current-password').value = '';
                    document.getElementById('new-password').value = '';
                    document.getElementById('confirm-password').value = '';

                    // Reset all visual indicators
                    const newPassword = document.getElementById('new-password');
                    const confirmPassword = document.getElementById('confirm-password');
                    const passwordMatchMessage = document.getElementById('modal-password-match-message');

                    // Reset borders
                    newPassword.classList.remove('border-red-500', 'border-green-500');
                    confirmPassword.classList.remove('border-red-500', 'border-green-500');

                    // Hide match message
                    passwordMatchMessage.classList.add('hidden');

                    // Reset requirement indicators
                    const requirements = ['modal-length-req', 'modal-letter-req', 'modal-number-req', 'modal-special-req'];
                    requirements.forEach(reqId => {
                        const element = document.getElementById(reqId);
                        const icon = element.querySelector('i');
                        element.classList.remove('text-green-500');
                        element.classList.add('text-red-500');
                        icon.classList.remove('fa-check');
                        icon.classList.add('fa-times');
                    });

                    // Reset submit button
                    const submitBtn = document.getElementById('modal-submit-btn');
                    submitBtn.disabled = true;
                    submitBtn.classList.remove('bg-blue-500', 'hover:bg-blue-600');
                    submitBtn.classList.add('bg-gray-400', 'cursor-not-allowed');

                    // Clear any error/success messages
                    const errorElement = document.getElementById('password-change-error');
                    const successElement = document.getElementById('password-change-success');
                    if (errorElement) errorElement.classList.add('hidden');
                    if (successElement) successElement.classList.add('hidden');
                }

                passwordForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData(this);
                    const errorElement = document.getElementById('password-change-error');
                    const successElement = document.getElementById('password-change-success');

                    // Basic validation
                    const newPassword = document.getElementById('new-password').value;
                    const confirmPassword = document.getElementById('confirm-password').value;

                    if (newPassword !== confirmPassword) {
                        errorElement.textContent = 'Les nouveaux mots de passe ne correspondent pas.';
                        errorElement.classList.remove('hidden');
                        successElement.classList.add('hidden');
                        return;
                    }

                    // Reset messages
                    errorElement.classList.add('hidden');
                    successElement.classList.add('hidden');

                    fetch('/user/changePassword', {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Server returned ' + response.status + ': ' + response.statusText);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            // Show success message
                            successElement.textContent = data.message;
                            successElement.classList.remove('hidden');

                            // Clear form
                            document.getElementById('current-password').value = '';
                            document.getElementById('new-password').value = '';
                            document.getElementById('confirm-password').value = '';

                            // Close modal after 3 seconds
                            setTimeout(() => {
                                closeModal('change-password-modal');
                            }, 3000);
                        } else {
                            // Show error message
                            errorElement.textContent = data.message;
                            errorElement.classList.remove('hidden');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        errorElement.textContent = 'Une erreur est survenue lors du changement de mot de passe. Veuillez réessayer.';
                        errorElement.classList.remove('hidden');
                    });
                });
            }
        });

        // Initialize photo preview
        window.onload = function() {

            // Photo upload preview
            const photoUpload = document.getElementById('photo-upload');
            if (photoUpload) {
                photoUpload.addEventListener('change', function(e) {
                    if (e.target.files && e.target.files[0]) {
                        const reader = new FileReader();
                        reader.onload = function(event) {
                            document.getElementById('photo-preview').src = event.target.result;
                        };
                        reader.readAsDataURL(e.target.files[0]);
                    }
                });
            }

            // Check for URL messages and display them
            const urlParams = new URLSearchParams(window.location.search);
            const successMsg = urlParams.get('success');
            const errorMsg = urlParams.get('error');

            if (successMsg) {
                const successElement = document.createElement('div');
                successElement.className = 'bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6';
                successElement.innerHTML = `<p>${decodeURIComponent(successMsg)}</p>`;

                const header = document.querySelector('header');
                header.parentNode.insertBefore(successElement, header.nextSibling);

                // Remove from URL without refreshing
                history.replaceState({}, document.title, window.location.pathname);

                // Remove message after 5 seconds
                setTimeout(() => {
                    successElement.remove();
                }, 5000);
            }

            if (errorMsg) {
                const errorElement = document.createElement('div');
                errorElement.className = 'bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6';
                errorElement.innerHTML = `<p>${decodeURIComponent(errorMsg)}</p>`;

                const header = document.querySelector('header');
                header.parentNode.insertBefore(errorElement, header.nextSibling);

                // Remove from URL without refreshing
                history.replaceState({}, document.title, window.location.pathname);

                // Remove message after 5 seconds
                setTimeout(() => {
                    errorElement.remove();
                }, 5000);
            }
        };