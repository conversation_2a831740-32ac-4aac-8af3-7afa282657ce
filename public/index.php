<?php
// Main entry point of the application

// Start session
session_start();

// Define paths
define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/app');
define('APPROOT', ROOT_PATH . '/app'); // Alternative constant name used by some views
define('CORE_PATH', ROOT_PATH . '/core');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('PUBLIC_PATH', ROOT_PATH . '/public');

// Include core files
require_once CORE_PATH . '/Autoloader.php';
require_once CORE_PATH . '/Controller.php';
require_once CORE_PATH . '/Model.php';
require_once CORE_PATH . '/Router.php';
require_once CORE_PATH . '/Database.php';
require_once CORE_PATH . '/Auth.php';

// Include config
require_once CONFIG_PATH . '/config.php';
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Register autoloader
Autoloader::register();

// Initialize router
$router = new Router();

// Define routes
$router->addRoute('/home', 'HomeController', 'index');
$router->addRoute('/login', 'AuthController', 'index');  // Use index for the login page
$router->addRoute('/login/submit', 'AuthController', 'login'); // Use login for form submission
// Add explicit route for login index page
$router->addRoute('/login/index', 'AuthController', 'index');
$router->addRoute('/logout', 'AuthController', 'logout');

// Employé routes
$router->addRoute('/dashboard-employe', 'EmployeController', 'dashboard');
$router->addRoute('/nouvelle-demande', 'DemandeController', 'nouvelle');
$router->addRoute('/mes-demandes', 'DemandeController', 'liste');
$router->addRoute('/details-demande', 'DemandeController', 'details');
$router->addRoute('/annuler-demande', 'DemandeController', 'annuler');
$router->addRoute('/demande-document', 'DemandeController', 'demande_document');
$router->addRoute('/demandes/validate', 'DemandeController', 'validateRequest');

// Admin routes
$router->addRoute('/dashboard-admin', 'AdminController', 'dashboard');
$router->addRoute('/list-users', 'AdminController', 'listUsers');
$router->addRoute('/edit-user', 'AdminController', 'editUser');
$router->addRoute('/delete-user', 'AdminController', 'deleteUser');
$router->addRoute('/all-demandes', 'AdminController', 'allDemandes');
$router->addRoute('/demande-details', 'DemandeController', 'details'); // Shared route for viewing demande details
$router->addRoute('/settings', 'AdminController', 'settings');
$router->addRoute('/logs', 'AdminController', 'logs');
$router->addRoute('/jours-feries-admin', 'AdminController', 'joursFeries');
$router->addRoute('/admin/import-csv', 'AdminController', 'importCsv');
$router->addRoute('/admin/download-csv-template', 'AdminController', 'downloadCsvTemplate');

// Leave balance management routes
$router->addRoute('/admin/leave_balances', 'AdminController', 'leaveBalances');
$router->addRoute('/admin/leave-balances', 'AdminController', 'leaveBalances'); // Alternative with hyphens
$router->addRoute('/admin/leave_balances/initialize', 'AdminController', 'initializeLeaveBalances');
$router->addRoute('/admin/leave-balances/initialize', 'AdminController', 'initializeLeaveBalances'); // Alternative with hyphens
$router->addRoute('/admin/leave_balances/process-accruals', 'AdminController', 'processAccruals');
$router->addRoute('/admin/leave_balances/edit/{id}', 'AdminController', 'editLeaveBalance');
$router->addRoute('/admin/leave-balances/edit/{id}', 'AdminController', 'editLeaveBalance'); // Alternative with hyphens
$router->addRoute('/admin/leave_balances/history/{id}', 'AdminController', 'leaveAccrualHistory');
$router->addRoute('/admin/leave-balances/history/{id}', 'AdminController', 'leaveAccrualHistory'); // Alternative with hyphens

// Department Leave Balance routes
$router->addRoute('/admin/department_leave_balances', 'AdminController', 'departmentLeaveBalances');
$router->addRoute('/admin/department-leave-balances', 'AdminController', 'departmentLeaveBalances'); // Alternative with hyphens
$router->addRoute('/admin/department-leave-balances/initialize', 'AdminController', 'initializeDepartmentLeaveBalances');
$router->addRoute('/admin/edit_department_leave_balance', 'AdminController', 'editDepartmentLeaveBalance');
$router->addRoute('/admin/department-leave-balances/edit', 'AdminController', 'editDepartmentLeaveBalance'); // Alternative with hyphens

// Planificateur routes
$router->addRoute('/dashboard-planificateur', 'PlanificateurController', 'dashboard');
$router->addRoute('/planning', 'PlanificateurController', 'planning');
$router->addRoute('/jours-feries', 'PlanificateurController', 'joursFeries');
$router->addRoute('/absences-a-venir', 'PlanificateurController', 'absencesAVenir');
$router->addRoute('/planificateur/demandes_approbation', 'PlanificateurController', 'demandes_approbation');
$router->addRoute('/planificateur/approuverDemande', 'PlanificateurController', 'approuverDemande');
$router->addRoute('/planificateur/rejeterDemande', 'PlanificateurController', 'rejeterDemande');
$router->addRoute('/planificateur/mes-demandes', 'DemandeController', 'liste');
$router->addRoute('/planificateur/nouvelle-demande', 'DemandeController', 'nouvelle');
$router->addRoute('/planificateur/details-demande', 'DemandeController', 'details');
$router->addRoute('/planificateur/annuler-demande', 'DemandeController', 'annulerDemande');

// Responsable routes
$router->addRoute('/dashboard-responsable', 'ResponsableController', 'dashboard');
$router->addRoute('/responsable/team_members', 'ResponsableController', 'team_members');
$router->addRoute('/responsable/team_availability', 'ResponsableController', 'team_availability');
$router->addRoute('/responsable/demandes_approbation', 'ResponsableController', 'demandes_approbation');
$router->addRoute('/responsable/historique_demandes', 'ResponsableController', 'historique_demandes');
$router->addRoute('/responsable/approuverDemande', 'ResponsableController', 'approuverDemande');
$router->addRoute('/responsable/rejeterDemande', 'ResponsableController', 'rejeterDemande');
$router->addRoute('/responsable/statistiques', 'ResponsableController', 'statistiques');
$router->addRoute('/responsable/mes-demandes', 'ResponsableController', 'mes_demandes');
$router->addRoute('/responsable/nouvelle-demande', 'ResponsableController', 'nouvelle_demande');
$router->addRoute('/responsable/details-demande', 'DemandeController', 'details');
$router->addRoute('/responsable/annuler-demande', 'DemandeController', 'annulerDemande');

// Keep old routes for backward compatibility
$router->addRoute('/team-members', 'ResponsableController', 'team_members');
$router->addRoute('/team-availability', 'ResponsableController', 'team_availability');
$router->addRoute('/demandes-approbation', 'ResponsableController', 'demandes_approbation');
$router->addRoute('/historique-demandes', 'ResponsableController', 'historique_demandes');
$router->addRoute('/approuver-demande', 'ResponsableController', 'approuverDemande');
$router->addRoute('/rejeter-demande', 'ResponsableController', 'rejeterDemande');

// Document routes
$router->addRoute('/documents', 'DocumentController', 'liste');
$router->addRoute('/documents/ajouter', 'DocumentController', 'ajouter');
$router->addRoute('/documents/modifier', 'DocumentController', 'modifier');
$router->addRoute('/documents/supprimer', 'DocumentController', 'supprimer');
$router->addRoute('/documents/serve', 'DocumentController', 'serve');

// API routes
$router->addRoute('/api/absences/{id}', 'ApiController', 'getAbsenceDetails');
$router->addRoute('/api/holidays', 'ApiController', 'getHolidays');

// Add explicit routes for common absence IDs to ensure they work
// This is a fallback in case the parameterized route doesn't work
for ($i = 1; $i <= 100; $i++) {
    $router->addRoute('/api/absences/' . $i, 'ApiController', 'getAbsenceDetails');
}

// Common routes
$router->addRoute('/notifications', 'NotificationController', 'index');
$router->addRoute('/notifications/click', 'NotificationController', 'click');
$router->addRoute('/notifications/mark-as-read', 'NotificationController', 'markAsRead');
$router->addRoute('/notifications/mark-all-as-read', 'NotificationController', 'markAllAsRead');
$router->addRoute('/notifications/delete', 'NotificationController', 'delete');
$router->addRoute('/profil', 'UserController', 'profile');
$router->addRoute('/update-profil', 'UserController', 'update');
$router->addRoute('/change-password', 'UserController', 'changePassword');
$router->addRoute('/user/update', 'UserController', 'update');
$router->addRoute('/user/changePassword', 'UserController', 'changePassword');
$router->addRoute('/create-user', 'UserController', 'create');
$router->addRoute('/user/uploadPhoto', 'UserController', 'uploadPhoto');
$router->addRoute('/statistiques', 'StatistiqueController', 'index');

// Set 404 handler
$router->setNotFound(function() {
    header("HTTP/1.0 404 Not Found");
    include APP_PATH . '/views/errors/404.php';
});

// Get URL path
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Handle the case where path contains index.php from .htaccess rewrite
if (strpos($path, '/index.php') === 0) {
    $path = substr($path, strlen('/index.php'));
}

// For built-in server doesn't process .htaccess, so handle that case differently
if (php_sapi_name() == 'cli-server') {
    // For built-in PHP server, just use the path as-is
    // No additional processing needed
} else {
    // For production server with .htaccess
    // Remove base directory from path if present
    $scriptDir = dirname($_SERVER['SCRIPT_NAME']);
    if ($scriptDir !== '/' && strpos($path, $scriptDir) === 0) {
        $path = substr($path, strlen($scriptDir));
    }
}

// Ensure path starts with /
if (empty($path) || $path[0] !== '/') {
    $path = '/' . $path;
}

// For empty path, redirect to home
if ($path == '' || $path == '/') {
    $path = '/home';
}

// Debug information
error_log("Processing path: " . $path);

// Extract query string to handle login with error parameters
$query = parse_url($_SERVER['REQUEST_URI'], PHP_URL_QUERY);
if ($path == '/login' && !empty($query)) {
    // Keep path as /login but pass the query parameter to the view
    $_GET = [];
    parse_str($query, $_GET);
}

// Dispatch route
$router->dispatch($path);
