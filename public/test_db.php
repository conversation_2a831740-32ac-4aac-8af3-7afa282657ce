<?php
// Test database connection
require_once '../config/config.php';

echo "Testing database connection...<br>";
echo "Host: " . Config::DB_HOST . "<br>";
echo "Port: " . Config::DB_PORT . "<br>";
echo "Database: " . Config::DB_NAME . "<br>";
echo "User: " . Config::DB_USER . "<br>";

try {
    // Try PDO connection
    $pdo = new PDO(
        "mysql:host=" . Config::DB_HOST . ";port=" . Config::DB_PORT . ";dbname=" . Config::DB_NAME,
        Config::DB_USER,
        Config::DB_PASS
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "PDO connection successful!<br>";
    
    // Test query
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $count = $stmt->fetchColumn();
    echo "Number of users in database: " . $count . "<br>";
    
} catch (PDOException $e) {
    echo "PDO connection failed: " . $e->getMessage() . "<br>";
}
