# Gestion de Congés - MVC Refactoring

Cette application est une refactorisation du système de gestion des congés dans une architecture MVC (Model-View-Controller).

## Structure du projet

```
├── app/
│   ├── controllers/     → Logique de traitement des requêtes
│   ├── models/          → Interaction avec la base de données et logique métier
│   └── views/           → Templates HTML et affichage
├── config/
│   └── config.php       → Configurations globales
├── core/
│   ├── Autoloader.php   → Chargement automatique des classes
│   ├── Controller.php   → Classe de base pour les contrôleurs
│   ├── Database.php     → Connexion à la base de données
│   ├── Model.php        → Classe de base pour les modèles
│   └── Router.php       → Routeur pour diriger les requêtes
├── public/
│   ├── assets/          → Fichiers statiques (CSS, JS, images)
│   │   ├── css/
│   │   ├── js/
│   │   └── img/
│   └── index.php        → Point d'entrée de l'application
└── PHPMailer-master/    → Bibliothèque PHPMailer
```

## Installation et configuration

1. <PERSON><PERSON><PERSON> le répertoire sur votre serveur
2. Configurez un vhost pour pointer vers le dossier `/public`
3. Assurez-vous que les permissions sont correctes pour l'écriture
4. V<PERSON>rifiez que la configuration de la base de données est correcte dans `/config/config.php`

## Fonctionnalités

- Authentification des utilisateurs
- Gestion des demandes de congés
- Tableaux de bord adaptés aux différents rôles
- Notifications
- Gestion de profil utilisateur

## Notes techniques

- Architecture MVC stricte
- Toute la logique métier est dans les modèles
- Toute la logique de contrôle des requêtes est dans les contrôleurs
- Tout l'affichage est dans les vues
- Utilisation d'un routeur pour une structure d'URL propre
- PHPMailer pour l'envoi d'emails

## Crédits

Application originale développée par l'équipe Gestion de Congés.
Refactorisation MVC réalisée en Mai 2025.
